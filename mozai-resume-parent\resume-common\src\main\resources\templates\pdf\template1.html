<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
            background: white;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
            }
        }

        .resume-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
            background: white;
        }

        .header {
            background: #4A90E2;
            color: white;
            padding: 15px 30px;
            margin-bottom: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .header-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .contact-info {
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 12px;
            flex-wrap: wrap;
        }

        .contact-item {
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .section {
            margin-bottom: 20px;
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e0e0e0;
        }

        .section-title {
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 5px;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            letter-spacing: 0.5px;
        }

        .item {
            margin-bottom: 15px;
            padding: 0;
        }

        .item:last-child {
            margin-bottom: 0;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            align-items: flex-start;
        }

        .item-time {
            color: #666;
            font-size: 14px;
            min-width: 120px;
            flex-shrink: 0;
        }

        .item-title {
            font-weight: bold;
            color: #333;
            font-size: 14px;
            flex: 1;
            text-align: center;
            margin: 0 15px;
        }

        .item-detail {
            color: #4A90E2;
            font-size: 14px;
            min-width: 120px;
            text-align: right;
            flex-shrink: 0;
        }

        .item-content {
            text-align: justify;
            font-size: 13px;
            line-height: 1.6;
            color: #555;
            margin-top: 8px;
            padding-left: 120px;
        }

        ul {
            margin-top: 5px;
            margin-bottom: 5px;
            padding-left: 140px;
        }

        li {
            margin-bottom: 3px;
            font-size: 13px;
            line-height: 1.6;
        }

        .skill-item {
            margin-bottom: 10px;
        }

        .skill-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .skill-description {
            color: #555;
            font-size: 13px;
            line-height: 1.6;
            margin-left: 120px;
        }

        .section-content {
            font-size: 13px;
            line-height: 1.6;
            color: #555;
            text-align: justify;
        }

        /* 内容区域整体样式 */
        .content-area {
            padding: 0;
            background: white;
        }

        /* 响应式和打印优化 */
        @media print {
            body {
                background: white;
            }
            .section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 页眉部分 -->
        <div class="header">
            <div class="header-name">${name}</div>
            <div class="contact-info">
                <div class="contact-item">👤 ${age}岁</div>
                <div class="contact-item">📞 ${phone}</div>
                <div class="contact-item">✉️ ${email}</div>
                <div class="contact-item">📍 ${hometown}</div>
                <div class="contact-item">求职意向：${jobObjective}</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 教育经历 -->
            ${education}

            <!-- 工作经历 -->
            ${work}

            <!-- 项目经验 -->
            ${projects}

            <!-- 技能特长 -->
            ${skills}

            <!-- 证书奖项 -->
            ${certificates}

            <!-- 校园经历 -->
            ${campus}

            <!-- 兴趣爱好 -->
            ${interests}

            <!-- 自我评价 -->
            ${selfEvaluation}
        </div>
    </div>
</body>
</html> 