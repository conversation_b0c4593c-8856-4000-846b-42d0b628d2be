<template>
  <div class="form-container">
    <div class="education-list">
      <div v-for="(edu, index) in formData" :key="index" class="education-item">
        <div class="education-header">
          <h3>教育经历 #{{ index + 1 }}</h3>
          <el-button 
            v-if="formData.length > 1" 
            type="danger" 
            size="small" 
            icon="Delete" 
            circle 
            @click="removeEducation(index)"
          ></el-button>
        </div>
        
        <el-form :model="edu" label-width="100px">
          <el-form-item label="学校名称" required>
            <el-input v-model="edu.school" placeholder="请输入学校名称" />
          </el-form-item>
          
          <el-form-item label="专业名称" required>
            <el-input v-model="edu.major" placeholder="请输入专业名称" />
          </el-form-item>
          
          <el-form-item label="学历" required>
            <el-select v-model="edu.degree" placeholder="请选择学历">
              <el-option label="高中" value="高中" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="就读时间" required>
            <el-date-picker
              v-model="edu.startDate"
              type="month"
              placeholder="开始时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="edu.endDate"
              type="month"
              placeholder="结束时间（在读则留空）"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <el-checkbox v-model="edu.inProgress" style="margin-left: 10px">在读</el-checkbox>
          </el-form-item>
          
          <el-form-item label="专业排名">
            <el-input v-model="edu.ranking" placeholder="例如：专业前10%、班级第一等">
              <template #append>
                <el-select v-model="edu.rankingType" style="width: 100px;">
                  <el-option label="专业排名" value="专业排名" />
                  <el-option label="班级排名" value="班级排名" />
                  <el-option label="年级排名" value="年级排名" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="主修课程">
            <el-input
              type="textarea"
              v-model="edu.courses"
              placeholder="请输入主修课程，用逗号分隔"
              :rows="2"
            />
          </el-form-item>
          
          <el-form-item label="GPA">
            <el-input v-model="edu.gpa" placeholder="如：3.8/4.0" />
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input
              type="textarea"
              v-model="edu.courses"
              placeholder="请描述在校期间的主要学习内容、成绩、获奖情况等"
              :rows="4"
            />
          </el-form-item>
        </el-form>
        
        <el-divider v-if="index < formData.length - 1" />
      </div>
    </div>
    
    <div class="add-education">
      <el-button type="dashed" @click="addEducation" icon="Plus">添加教育经历</el-button>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref([])

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && newVal.length) {
    formData.value = JSON.parse(JSON.stringify(newVal))
  } else {
    // 如果没有数据，创建一个空白的教育经历
    formData.value = [createEmptyEducation()]
  }
}, { immediate: true, deep: true })

// 创建空白的教育经历对象
function createEmptyEducation() {
  return {
    school: '',
    major: '',
    degree: '',
    startDate: '',
    endDate: '',
    inProgress: false,
    ranking: '',
    rankingType: '专业排名',
    courses: '',
    gpa: '',
    description: ''
  }
}

// 添加教育经历
const addEducation = () => {
  formData.value.push(createEmptyEducation())
}

// 移除教育经历
const removeEducation = (index) => {
  formData.value.splice(index, 1)
}

// 保存表单
const saveForm = () => {
  // 基本验证
  let isValid = true
  formData.value.forEach((edu, index) => {
    if (!edu.school || !edu.major || !edu.degree || !edu.startDate) {
      ElMessage.warning(`教育经历 #${index + 1} 中的学校、专业、学历和开始时间为必填项`)
      isValid = false
    }
  })
  
  if (!isValid) return
  
  // 处理"在读"情况
  const processedData = formData.value.map(edu => {
    const processed = { ...edu }
    if (processed.inProgress) {
      processed.endDate = '至今'
    }
    return processed
  })
  
  emit('update', processedData)
  ElMessage.success('教育背景信息已保存')
}

// 重置表单
const resetForm = () => {
  if (props.data && props.data.length) {
    formData.value = JSON.parse(JSON.stringify(props.data))
  } else {
    formData.value = [createEmptyEducation()]
  }
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.education-list {
  margin-bottom: 20px;
}

.education-item {
  margin-bottom: 20px;
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.education-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.date-separator {
  margin: 0 10px;
}

.add-education {
  margin: 20px 0;
  text-align: center;
}

.add-education .el-button {
  width: 100%;
  border-style: dashed;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 