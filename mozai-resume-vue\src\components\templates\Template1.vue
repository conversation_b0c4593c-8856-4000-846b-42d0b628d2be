<template>
  <div class="resume-template template-1">
    <!-- 简历头部 -->
    <div class="resume-header">
      <div class="resume-title">
        <h1>{{
            resume && resume.modules && resume.modules.basic ? (resume.modules.basic.name || '求职者') : '求职者'
          }}</h1>
      </div>
      <div class="resume-contact">
        <div class="contact-item" v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.age">
          <el-icon>
            <user/>
          </el-icon>
          {{ resume.modules.basic.age }}岁
        </div>
        <div class="contact-item" v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.phone">
          <el-icon>
            <phone/>
          </el-icon>
          {{ resume.modules.basic.phone }}
        </div>
        <div class="contact-item" v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.email">
          <el-icon>
            <message/>
          </el-icon>
          {{ resume.modules.basic.email }}
        </div>
        <div class="contact-item"
             v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.address">
          <el-icon>
            <location/>
          </el-icon>
          {{ resume.modules.basic.address }}
        </div>
        <div class="resume-objective"
             v-if="resume && resume.modules && resume.modules.basic && resume.modules.basic.jobObjective">
          <el-icon>
            <aim/>
          </el-icon>
          {{ resume.modules.basic.jobObjective }}
        </div>
      </div>
    </div>

    <!-- 简历内容 -->
    <div class="resume-content">
      <!-- 教育经历 -->
      <div v-if="hasEducation" class="resume-section">
        <div class="section-header">
          <h2>教育经历</h2>
        </div>
        <div class="education-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="education-header">
              <div class="edu-date">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
              <div class="edu-school">{{ edu.school }}</div>
              <div class="edu-degree">
                {{ edu.major }}
                <span v-if="edu.degree && edu.degree.trim() !== ''">
                  （{{ edu.degree }}）
                </span>
              </div>
            </div>
            <div class="education-details">

              <div v-if="edu.courses" class="edu-courses">主修课程：{{ edu.courses }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作经验 -->
      <div v-if="hasWork" class="resume-section">
        <div class="section-header">
          <h2>工作经验</h2>
        </div>
        <div class="work-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="work-header">
              <div class="work-date">{{ work.time[0] }} - {{ work.time[1] || '至今' }}</div>
              <div class="work-company">{{ work.company }}</div>
              <div class="work-position">{{ work.position }}</div>
            </div>
            <div class="work-description" v-html="formatContent(work.description)"></div>
          </div>
        </div>
      </div>

      <!-- 项目经验 -->
      <div v-if="hasProjects" class="resume-section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <div class="project-role">{{ project.role }}</div>
              <div class="project-date">{{ project.time[0] }} - {{ project.time[1] || '至今' }}</div>
            </div>

            <div class="project-description" v-html="formatContent(project.description)">
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="resume-section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ practice.name }}</div>
              <div class="project-role">{{ practice.role }}</div>
              <div class="project-date">{{ practice.time[0] }} - {{ practice.time[1] || '至今' }}</div>
            </div>

            <div class="project-description" v-html="formatContent(practice.description)">
            </div>
          </div>
        </div>
      </div>

      <!-- 技能特长 -->
      <div v-if="hasSkills" class="resume-section">
        <div class="section-header">
          <h2>技能特长</h2>
        </div>
        <div class="skills-content">
          <div v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-description-item">
            <div class="skill-description-body" v-html="formatContent(skill.description)">
            </div>
          </div>
        </div>
      </div>

      <!-- 证书奖项 -->
      <div v-if="hasCertificates" class="resume-section">
        <div class="section-header">
          <h2>证书奖项</h2>
        </div>
        <div class="certificate-content">
          <MdPreview :modelValue="typeof resume.modules.certificates === 'string' ? 
            resume.modules.certificates : 
            (typeof resume.modules.certificates === 'object' ? resume.modules.certificates.certificateName : '')"/>
        </div>
      </div>

      <!-- 校园经历 -->
      <div v-if="hasCampus" class="resume-section">
        <div class="section-header">
          <h2>校园经历</h2>
        </div>
        <div class="campus-content" v-html="formattedCampus"></div>
      </div>

      <!-- 兴趣爱好 -->
      <div v-if="hasInterests" class="resume-section">
        <div class="section-header">
          <h2>兴趣爱好</h2>
        </div>
        <div class="interests-content" v-html="formattedInterests"></div>
      </div>

      <!-- 个人评价 -->
      <div v-if="hasEvaluation" class="resume-section">
        <div class="section-header">
          <h2>个人评价</h2>
        </div>
        <div class="evaluation-content" v-html="formattedEvaluation"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, toRefs} from 'vue';
import {MdPreview} from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';
import { User, Phone, Message, Location, Aim } from '@element-plus/icons-vue'
import Avatar from '@/components/Avatar.vue'

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

const {resume} = toRefs(props);

// 格式化日期
const formatDate = (date) => {
  if (!date) return '2024-01';

  try {
    // 尝试解析日期
    const dateObj = new Date(date);

    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '2024-01';
    }

    // 格式化日期为 YYYY-MM 格式
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  } catch (e) {
    // 如果发生任何错误，返回默认日期
    return '2024-01';
  }
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasSkillsWithDescriptions = computed(() => {
  if (!props.resume.modules || !props.resume.modules.skills) return false;
  return props.resume.modules.skills.some(skill => skill.description && skill.description.trim() !== '');
});
const hasCertificates = computed(() => {
  if (!props.resume.modules || !props.resume.modules.certificates) return false;
  const cert = props.resume.modules.certificates;
  return typeof cert === 'string' ? cert.trim() !== '' :
      (typeof cert === 'object' && cert.certificateName && cert.certificateName.trim() !== '');
});
const hasCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return false;
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? campus.trim() !== '' :
      (typeof campus === 'object' && campus.description && campus.description.trim() !== '');
});
const hasInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return false;
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? interests.trim() !== '' :
      (typeof interests === 'object' && interests.description && interests.description.trim() !== '');
});
const hasEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return false;
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? evaluation.trim() !== '' :
      (typeof evaluation === 'object' && evaluation.description && evaluation.description.trim() !== '');
});
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化工作描述为列表项
const getWorkItems = (description) => {
  if (!description) return [];
  return description.split('\n').filter(item => item.trim() !== '');
};

// 格式化项目描述为列表项
const getProjectItems = (description) => {
  if (!description) return [];
  return description.split('\n').filter(item => item.trim() !== '');
};

// 根据技能等级获取文本描述
const getSkillLevelText = (level) => {
  switch (level) {
    case 1:
      return '了解';
    case 2:
      return '掌握';
    case 3:
      return '熟悉';
    case 4:
      return '精通';
    case 5:
      return '专家';
    default:
      return '了解';
  }
};

const formatContent1 = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 处理列表（保持原有逻辑）
  let formatted = content
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');

  // 处理标题（保持原有逻辑）
  formatted = formatted
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>');

  // 处理加粗和斜体（保持原有逻辑）
  formatted = formatted
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 为非列表、非标题的普通文本行添加序号
  const lines = formatted.split('\n');
  let numberedLines = '';
  let lineNumber = 1;

  lines.forEach(line => {
    // 跳过空行、HTML标签行（如列表、标题）
    if (!line.trim() || line.match(/^<\/?[a-z][\s\S]*>$/i)) {
      numberedLines += line + '\n';
    } else {
      // 添加序号
      numberedLines += `<span class="line-number">${lineNumber++}.</span> ${line}\n`;
    }
  });

  // 处理段落（保持原有逻辑，但在添加序号后）
  formatted = numberedLines
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');

  return formatted;
};

// 格式化内容
const formattedCertificates = computed(() => {
  if (!props.resume.modules || !props.resume.modules.certificates) return '';
  const cert = props.resume.modules.certificates;
  return typeof cert === 'string' ? formatContent1(cert) :
      (typeof cert === 'object' && cert.certificateName ? formatContent1(cert.certificateName) : '');
});

const formattedCampus = computed(() => {
  if (!props.resume.modules || !props.resume.modules.campus) return '';
  const campus = props.resume.modules.campus;
  return typeof campus === 'string' ? formatContent1(campus) :
      (typeof campus === 'object' && campus.description ? formatContent1(campus.description) : '');
});

const formattedInterests = computed(() => {
  if (!props.resume.modules || !props.resume.modules.interests) return '';
  const interests = props.resume.modules.interests;
  return typeof interests === 'string' ? formatContent(interests) :
      (typeof interests === 'object' && interests.description ? formatContent(interests.description) : '');
});

const formattedEvaluation = computed(() => {
  if (!props.resume.modules || !props.resume.modules.selfEvaluation) return '';
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? formatContent(evaluation) :
      (typeof evaluation === 'object' && evaluation.description ? formatContent(evaluation.description) : '');
});

// 格式化文本内容，支持Markdown格式
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 简单的Markdown转HTML处理
  return content
      // 处理标题
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
      // 处理加粗和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理列表
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
      // 处理段落
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
};

// 计算较亮的颜色变体（用于渐变效果）
const getLighterColor = (hexColor) => {
  if (!hexColor) return '#2ecc71'; // 默认备用颜色

  // 将十六进制颜色转换为RGB
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);

  // 亮化颜色（简单方法：将RGB值调高）
  const lighterR = Math.min(255, r + 20);
  const lighterG = Math.min(255, g + 40);
  const lighterB = Math.min(255, b + 20);

  // 转回十六进制格式
  return '#' +
      lighterR.toString(16).padStart(2, '0') +
      lighterG.toString(16).padStart(2, '0') +
      lighterB.toString(16).padStart(2, '0');
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  background: white;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
}

.resume-contact {
  display: flex;
  flex-wrap: wrap; /* 允许子元素换行 */
  /* 可选：设置子元素间距 */
  gap: 10px; /* 行内和行间距 */
}

.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 2px solid v-bind('resume.themeColor || "#3498db"');
  background: linear-gradient(90deg, v-bind('resume.themeColor || "#3498db"'), v-bind('getLighterColor(resume.themeColor) || "#2ecc71"'));
  color: white;
  height: 80px;
}

.resume-title h1 {
  font-size: 28px;
  margin: 0;
  font-weight: bold;
}


.skill-index {
  flex-shrink: 0; /* 防止序号被压缩 */
  color: #666; /* 灰色字体 */
  font-weight: 500; /* 加粗 */
}

.project-title {
  font-weight: bold;
}

.resume-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.header-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.resume-content {
  padding: 20px;
}

.resume-section {
  margin-bottom: 0px;
}

.section-header {
  position: relative;
  border-bottom: none;
  margin-bottom: 10px;
}

.section-header h2 {
  font-size: 18px;
  color: v-bind('resume.themeColor || "#3498db"');
  margin: 0;
  padding: 10px 0;
  font-weight: bold;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: v-bind('resume.themeColor || "#3498db"');
}

.section-header::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #eaecef;
}

.basic-info {
  display: flex;
  flex-wrap: wrap;
}

.basic-info-left,
.basic-info-right {
  flex: 1;
  min-width: 250px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.7;
}

.info-label {
  width: 80px;
  color: #666;
  font-weight: bold;
}

.info-value {
  flex: 1;
  color: #333;
}

.photo-container {
  width: 120px;
  height: 150px;
  margin-left: 20px;
  overflow: hidden;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo,
.photo-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.education-content,
.work-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}


.edu-time {
  width: 190px;
  color: #666;
  font-weight: bold;
}

.edu-gpa {
  flex: 1;
  min-width: 200px;
  color: #666;
}

.edu-courses {
  width: 100%;
  color: #666;
  font-size: 0.95em;
  line-height: 1.7;
  text-align: justify;
}

.work-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.work-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.work-date {
  font-weight: bold;
}

.work-company {
  font-weight: bold;
}

.work-position {
  font-weight: bold;
}

.work-description {
  line-height: 1.7;
  text-align: justify;
}

.skills-content {
  display: flex;
  flex-direction: column;
}

.skills-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 8px;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skills-descriptions {
  margin-top: 15px;
  border-top: 1px dashed #eee;
  padding-top: 15px;
}

.skill-description-item {
  margin-bottom: 3px;
  display: flex;
}

.skill-description-text {
  font-size: 13px;
  color: #444;
  line-height: 1.7;
  text-align: justify;
  white-space: pre-line;
}

.skill-name {
  width: 100px;
  font-weight: bold;
}

.skill-level-container {
  flex: 1;
  height: 8px;
  background-color: #eee;
  border-radius: 4px;
  overflow: hidden;
}

.skill-level {
  height: 100%;
  background-color: v-bind('resume.themeColor || "#3498db"');
  border-radius: 4px;
}

.skill-text {
  width: 40px;
  text-align: right;
  font-size: 12px;
  color: #666;
}

.language-skills {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.language-title,
.language-bars,
.language-labels {
  display: flex;
  justify-content: space-around;
}

.language-title {
  justify-content: space-between;
  padding: 0 20px;
  font-weight: bold;
}

.language-bar {
  height: 10px;
  width: 100%;
  background-color: #eee;
  border-radius: 5px;
  overflow: hidden;
  margin: 0 10px;
}

.language-level {
  height: 100%;
  border-radius: 5px;
}

.language-level.computer {
  background-color: v-bind('resume.themeColor || "#3498db"');
}

.language-level.english {
  background-color: v-bind('resume.themeColor || "#3498db"');
}

.language-labels {
  justify-content: space-between;
  padding: 0 20px;
  color: #666;
  font-size: 12px;
}

.certificate-content,
.campus-content,
.interests-content {
  line-height: 1.7;
  text-align: justify;
}

/* 确保列表样式正确 */
.certificate-content ul,
.campus-content ul,
.interests-content ul {
  padding-left: 20px;
  margin: 0;
}

.certificate-content li,
.campus-content li,
.interests-content li {
  margin-bottom: 3px;
}

.certificate-content p,
.campus-content p,
.interests-content p {
  margin: 0 0 5px 0;
}

.project-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.project-date {
  font-weight: bold;
}

.project-name {
  font-weight: bold;
}

.project-role {
  font-weight: bold;
}

.project-description {
  line-height: 1.7;
  text-align: justify;
}

.project-description ul {
  margin: 0;
  padding-left: 20px;
}

.project-description li {
  margin-bottom: 8px;
  line-height: 1.7;
}

.project-description li:last-child {
  margin-bottom: 0;
}

.projects-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.education-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.edu-school {
  font-weight: bold;
}

.skill-description-item[data-v-3bf984d1] {
  margin-bottom: 0px;
}

.skill-description-text[data-v-3bf984d1] {
  line-height: 1.7;
}

.edu-date {
  font-weight: bold;
}

.edu-degree {
  font-weight: bold;
}
</style> 