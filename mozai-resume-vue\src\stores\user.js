import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo, logout as apiLogout } from '../api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
    isLoading: false
  }),
  
  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    setUserInfo(userInfo) {
      console.log('设置用户信息:', userInfo)
      this.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    
    async fetchUserInfo() {
      // 即使没有token也调用后端API，让后端处理登录状态和SSO验证
      // 如果已登录，后端会返回用户信息
      // 如果未登录，后端filter会处理重定向到SSO登录
      
      try {
        this.isLoading = true
        console.log('开始获取用户信息')
        const res = await getUserInfo()
        console.log('获取用户信息响应:', res)
        
        if (res.code === 0 && res.data) {
          console.log('获取用户信息成功:', res.data)
          this.setUserInfo(res.data)
          return res.data
        } else {
          console.warn('获取用户信息失败:', res.message)
          return null
        }
      } catch (error) {
        console.error('获取用户信息出错:', error)
        return null
      } finally {
        this.isLoading = false
      }
    },
    
    async logout() {
      console.log('开始执行退出登录')
      try {
        // 无论是否有token，都调用退出登录接口
        console.log('调用后端退出登录接口')
        const res = await apiLogout()
        console.log('退出登录接口响应:', res)
        
        if (res && res.code === 0) {
          console.log('后端退出登录成功')
        } else {
          console.warn('后端退出登录返回非成功状态:', res)
        }
      } catch (error) {
        console.error('调用退出登录接口出错:', error)
      } finally {
        // 清除本地存储的token和用户信息
        console.log('清除本地token和用户信息')
        this.token = ''
        this.userInfo = {}
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        console.log('退出登录流程完成')
      }
    }
  },
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    nickname: (state) => {
      console.log('获取昵称，当前userInfo:', state.userInfo)
      const nick = state.userInfo?.nickname || state.userInfo?.username || '用户'
      console.log('计算得到的昵称:', nick)
      return nick
    }
  }
}) 