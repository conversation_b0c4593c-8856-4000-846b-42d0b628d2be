# PDF生成相关配置
pdf:
  # Playwright配置
  playwright:
    enabled: true # 是否启用Playwright PDF生成器
    timeout: 30000 # Playwright超时时间（毫秒）
    headless: true # 是否使用无头模式
    debug: false # 是否启用Playwright调试模式
    browser-path: "" # 浏览器路径（留空使用默认）
    user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" # 用户代理
    wait-for-network-idle: true # 是否等待网络空闲
    print-background: true # 是否打印背景
    # 页面配置
    page:
      format: A4 # 页面格式
      orientation: portrait # 页面方向
      margin-top: 20 # 上边距（mm）
      margin-bottom: 20 # 下边距（mm）
      margin-left: 20 # 左边距（mm）
      margin-right: 20 # 右边距（mm）
  
  # 降级配置
  fallback:
    threshold: 3 # 连续失败多少次后进入降级模式
    window-minutes: 30 # 降级模式持续时间（分钟）
    enable-auto-recovery: true # 是否启用自动恢复
  # 调试配置
  debug:
    enabled: false  # 是否启用PDF调试模式
    detailed: false # 是否启用详细调试模式
    log-level: INFO # 日志级别：TRACE, DEBUG, INFO, WARN, ERROR
    output-html: false # 是否输出生成的HTML内容到文件
    output-path: /tmp/pdf-debug # 调试文件输出路径
  
  # 性能监控配置
  performance:
    enable-monitoring: true # 是否启用性能监控
    max-concurrent-generations: 10 # 最大并发生成数
    slow-query-threshold-ms: 5000 # 慢查询阈值（毫秒）
    enable-statistics: true # 是否启用统计功能
    statistics-reset-interval: 3600 # 统计重置间隔（秒）
  
  # 生成配置（内存优化）
  generation:
    timeout-ms: 30000 # PDF生成超时时间（毫秒）
    max-html-size: 5242880 # 最大HTML内容大小（5MB，降低以减少内存使用）
    enable-fallback: true # 启用降级方案以处理内存不足
    retry-count: 2 # 失败重试次数
    enable-cache: true # 是否启用模板缓存
    cache-ttl: 3600 # 缓存TTL（秒）
    # 内存管理配置
    memory:
      max-heap-usage-percent: 80 # 最大堆内存使用百分比
      gc-before-generation: true # 生成前执行GC
      enable-memory-monitoring: true # 启用内存监控
  
  # 模板配置
  template:
    base-path: classpath:templates/resume # 模板基础路径
    encoding: UTF-8 # 模板文件编码
    enable-validation: true # 是否启用模板验证
    preload-templates: true # 是否预加载模板
  
  # 字体配置（内存优化）
  font:
    default-family: SimSun # 默认字体族
    enable-font-embedding: false # 禁用字体嵌入以减少内存使用
    font-path: classpath:fonts # 字体文件路径
    use-base64-encoding: false # 禁用base64编码以避免内存溢出
    enable-font-cache: true # 启用字体缓存
    max-font-cache-size: 2 # 最大字体缓存大小(MB)
    fallback-fonts: # 备用字体列表
      - Microsoft YaHei
      - 微软雅黑
      - SimHei
      - 黑体
      - Arial
      - sans-serif
  
  # 字体文件配置
  fonts:
    fonts:
      - name: SimSun
        path: classpath:fonts/simsun.ttf
        family: SimSun
        style: normal
        weight: 400
        embedded: true
        encoding: UTF-8
        isDefault: true
      - name: Microsoft YaHei
        path: classpath:fonts/simhei.ttf
        family: Microsoft YaHei
        style: normal
        weight: 400
        embedded: true
        encoding: UTF-8
  
  # 页面配置
  page:
    format: A4 # 页面格式：A4, A3, LETTER等
    orientation: PORTRAIT # 页面方向：PORTRAIT, LANDSCAPE
    margin-top: 20 # 上边距（mm）
    margin-bottom: 20 # 下边距（mm）
    margin-left: 20 # 左边距（mm）
    margin-right: 20 # 右边距（mm）
  
  # 图片处理配置
  image:
    max-width: 800 # 图片最大宽度（像素）
    max-height: 600 # 图片最大高度（像素）
    quality: 0.8 # 图片质量（0.1-1.0）
    enable-compression: true # 是否启用图片压缩
  
  # 错误处理配置
  error:
    enable-detailed-messages: true # 是否启用详细错误信息
    log-stack-trace: true # 是否记录堆栈跟踪
    enable-error-recovery: true # 是否启用错误恢复
    max-error-count: 5 # 最大错误计数
  
  # 内存优化配置
  memory:
    max-heap-usage-percent: 80.0 # 最大堆内存使用百分比
    gc-threshold: 70.0 # GC触发阈值
    gc-before-generation: true # 是否在PDF生成前执行GC
    enable-memory-monitoring: true # 是否启用内存监控
    font-cache-size: 2 # 字体缓存大小(MB)
    use-lightweight-fonts: true # 是否使用轻量级字体方案
    max-concurrent-generations: 3 # 最大并发PDF生成数
    estimated-memory-per-pdf: 100 # PDF生成内存预估(MB)

# 开发环境配置
---
spring:
  profiles: dev
pdf:
  debug:
    enabled: true
    detailed: true
    log-level: DEBUG
    output-html: true
  performance:
    enable-monitoring: true
    enable-statistics: true

# 测试环境配置  
---
spring:
  profiles: test
pdf:
  debug:
    enabled: true
    detailed: false
    log-level: INFO
  performance:
    enable-monitoring: true
    max-concurrent-generations: 5

# 生产环境配置
---
spring:
  profiles: prod
pdf:
  debug:
    enabled: false
    detailed: false
    log-level: WARN
    output-html: false
  performance:
    enable-monitoring: true
    max-concurrent-generations: 20
    slow-query-threshold-ms: 3000
  generation:
    timeout-ms: 20000
    retry-count: 3