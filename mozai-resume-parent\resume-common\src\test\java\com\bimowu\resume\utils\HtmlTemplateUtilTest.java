package com.bimowu.resume.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * HtmlTemplateUtil 测试类
 * 主要测试模板1检测逻辑的改进
 */
public class HtmlTemplateUtilTest {

    @Test
    @DisplayName("测试模板1检测 - 包含所有特征")
    public void testDetectTemplate1WithAllFeatures() {
        String template1Html = """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <style>
                    .header {
                        background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
                        color: #4A90E2;
                    }
                    body {
                        background: #fafafa;
                    }
                </style>
            </head>
            <body>
                <div class="resume-container">
                    <div class="header">
                        <div class="contact-info">
                            <div class="contact-row">
                                <div class="contact-item">
                                    <span>👤 ${age}岁</span>
                                    <span>📞 ${phone}</span>
                                    <span>✉️ ${email}</span>
                                    <span>📍 ${hometown}</span>
                                    <span>🎯 ${jobObjective}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="content-area">
                        ${education}
                    </div>
                </div>
            </body>
            </html>
            """;

        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1(template1Html, 1L);
        
        assertTrue(result.isTemplate1(), "应该检测为模板1");
        assertEquals("DUAL_VERIFICATION", result.getDetectionMethod(), "应该使用双重验证方法");
        assertTrue(result.getConfidence() >= 0.5, "置信度应该大于等于0.5");
        assertTrue(result.getDetectionFeatures().size() >= 5, "应该检测到至少5个特征");
    }

    @Test
    @DisplayName("测试模板1检测 - 仅有部分特征")
    public void testDetectTemplate1WithPartialFeatures() {
        String partialTemplate1Html = """
            <div class="resume-container">
                <div class="header" style="background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);">
                    <h1>${name}</h1>
                </div>
            </div>
            """;

        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1(partialTemplate1Html, null);
        
        assertTrue(result.isTemplate1(), "应该检测为模板1（基于蓝色渐变特征）");
        assertEquals("CONTENT_ANALYSIS", result.getDetectionMethod(), "应该使用内容分析方法");
        assertTrue(result.getConfidence() >= 0.4, "置信度应该大于等于0.4");
    }

    @Test
    @DisplayName("测试模板1检测 - 非模板1内容")
    public void testDetectTemplate1WithNonTemplate1Content() {
        String template2Html = """
            <div class="resume-container">
                <div class="header" style="border-bottom: 2px solid #2ecc71;">
                    <h1>${name}</h1>
                </div>
            </div>
            """;

        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1(template2Html, 2L);
        
        assertFalse(result.isTemplate1(), "不应该检测为模板1");
        assertTrue(result.getConfidence() < 0.5, "置信度应该小于0.5");
    }

    @Test
    @DisplayName("测试模板1检测 - templateId冲突情况")
    public void testDetectTemplate1WithConflictingTemplateId() {
        String template1Html = """
            <div class="header" style="background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);">
                <div class="contact-info">
                    <div class="contact-row">
                        <span>👤 ${age}岁</span>
                    </div>
                </div>
            </div>
            """;

        // templateId为2，但内容特征匹配模板1
        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1(template1Html, 2L);
        
        // 应该检测到冲突，但仍可能被识别为模板1（取决于置信度）
        assertTrue(result.getDetectionFeatures().stream()
                .anyMatch(feature -> feature.contains("templateId冲突")), 
                "应该检测到templateId冲突");
    }

    @Test
    @DisplayName("测试模板1检测 - 空内容")
    public void testDetectTemplate1WithEmptyContent() {
        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1("", null);
        
        assertFalse(result.isTemplate1(), "空内容不应该检测为模板1");
        assertEquals("EMPTY_CONTENT", result.getDetectionMethod(), "应该标记为空内容");
        assertEquals(0.0, result.getConfidence(), "置信度应该为0");
    }

    @Test
    @DisplayName("测试模板1检测 - null内容")
    public void testDetectTemplate1WithNullContent() {
        HtmlTemplateUtil.TemplateDetectionResult result = HtmlTemplateUtil.detectTemplate1(null, 1L);
        
        assertFalse(result.isTemplate1(), "null内容不应该检测为模板1");
        assertEquals("EMPTY_CONTENT", result.getDetectionMethod(), "应该标记为空内容");
        assertEquals(0.0, result.getConfidence(), "置信度应该为0");
    }
}