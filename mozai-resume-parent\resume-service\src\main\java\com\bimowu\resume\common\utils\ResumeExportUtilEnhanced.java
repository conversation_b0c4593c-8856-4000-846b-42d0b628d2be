package com.bimowu.resume.common.utils;

import com.bimowu.resume.common.service.impl.PDFGenerationStrategy;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 增强的简历导出工具类
 * 使用策略模式支持新旧PDF生成系统的平滑切换
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ResumeExportUtilEnhanced {
    
    private static PDFGenerationStrategy pdfGenerationStrategy;
    
    @Autowired
    public void setPdfGenerationStrategy(PDFGenerationStrategy pdfGenerationStrategy) {
        ResumeExportUtilEnhanced.pdfGenerationStrategy = pdfGenerationStrategy;
    }
    
    /**
     * 导出PDF格式简历（使用策略模式）
     */
    public static byte[] exportToPdf(ResumeFullSaveDto resume) throws Exception {
        if (pdfGenerationStrategy == null) {
            throw new IllegalStateException("PDF生成策略未初始化");
        }
        
        // 输出简历信息和模板ID
        if (resume.getResumeVo() != null) {
            log.info("开始生成PDF简历，简历ID: {}, 模板ID: {}, 标题: {}", 
                resume.getResumeVo().getResumeId(), 
                resume.getResumeVo().getTemplateId(),
                resume.getResumeVo().getTitle());
        } else {
            log.warn("resumeVo为空，将使用默认配置");
        }
        
        try {
            // 使用策略模式生成PDF
            return pdfGenerationStrategy.generatePDF(resume);
            
        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw e;
        }
    }
    
    /**
     * 获取PDF生成系统状态
     */
    public static PDFGenerationStrategy.SystemStatus getSystemStatus() {
        if (pdfGenerationStrategy == null) {
            return null;
        }
        return pdfGenerationStrategy.getSystemStatus();
    }
    
    /**
     * 手动切换到新系统
     */
    public static void switchToNewSystem() {
        if (pdfGenerationStrategy != null) {
            pdfGenerationStrategy.switchToNewSystem();
        }
    }
    
    /**
     * 手动切换到旧系统
     */
    public static void switchToLegacySystem() {
        if (pdfGenerationStrategy != null) {
            pdfGenerationStrategy.switchToLegacySystem();
        }
    }
}