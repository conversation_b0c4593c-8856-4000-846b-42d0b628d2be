package com.bimowu.resume.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 提取的样式信息DTO
 * 用于存储从前端Vue模板中提取的样式数据
 */
@Data
public class ExtractedStyles {
    
    /**
     * CSS样式内容
     */
    private String css;
    
    /**
     * 字体信息列表
     */
    private List<FontInfo> fonts;
    
    /**
     * 颜色调色板
     */
    private ColorPalette colors;
    
    /**
     * 布局信息
     */
    private LayoutInfo layout;
    
    /**
     * 响应式规则
     */
    private ResponsiveRules responsive;
    
    /**
     * 模板版本
     */
    private String version;
    
    /**
     * 提取时间戳
     */
    private Long timestamp;
    
    /**
     * 字体信息
     */
    @Data
    public static class FontInfo {
        /**
         * 字体族名称
         */
        private String family;
        
        /**
         * 字体样式（normal, italic, oblique）
         */
        private String style;
        
        /**
         * 字体权重
         */
        private int weight;
        
        /**
         * 字体大小
         */
        private String size;
        
        /**
         * 行高
         */
        private String lineHeight;
        
        /**
         * 是否为Web字体
         */
        private boolean isWebFont;
        
        /**
         * 字体来源
         */
        private String source;
    }
    
    /**
     * 颜色调色板
     */
    @Data
    public static class ColorPalette {
        /**
         * 主色调
         */
        private String primary;
        
        /**
         * 次要颜色
         */
        private String secondary;
        
        /**
         * 强调色
         */
        private String accent;
        
        /**
         * 背景色
         */
        private String background;
        
        /**
         * 文本颜色
         */
        private String text;
        
        /**
         * 边框颜色
         */
        private String border;
        
        /**
         * 自定义颜色映射
         */
        private Map<String, String> customColors;
    }
    
    /**
     * 布局信息
     */
    @Data
    public static class LayoutInfo {
        /**
         * 宽度
         */
        private int width;
        
        /**
         * 高度
         */
        private int height;
        
        /**
         * 外边距
         */
        private Margins margins;
        
        /**
         * 内边距
         */
        private Padding padding;
        
        /**
         * Flexbox信息
         */
        private FlexboxInfo flexbox;
        
        /**
         * Grid信息
         */
        private GridInfo grid;
        
        /**
         * 显示类型
         */
        private String display;
        
        /**
         * 定位类型
         */
        private String position;
    }
    
    /**
     * 外边距信息
     */
    @Data
    public static class Margins {
        private String top;
        private String right;
        private String bottom;
        private String left;
    }
    
    /**
     * 内边距信息
     */
    @Data
    public static class Padding {
        private String top;
        private String right;
        private String bottom;
        private String left;
    }
    
    /**
     * Flexbox布局信息
     */
    @Data
    public static class FlexboxInfo {
        /**
         * 主轴方向
         */
        private String direction;
        
        /**
         * 换行方式
         */
        private String wrap;
        
        /**
         * 主轴对齐方式
         */
        private String justifyContent;
        
        /**
         * 交叉轴对齐方式
         */
        private String alignItems;
        
        /**
         * 多行对齐方式
         */
        private String alignContent;
        
        /**
         * 间距
         */
        private String gap;
    }
    
    /**
     * Grid布局信息
     */
    @Data
    public static class GridInfo {
        /**
         * 列模板
         */
        private String templateColumns;
        
        /**
         * 行模板
         */
        private String templateRows;
        
        /**
         * 间距
         */
        private String gap;
        
        /**
         * 项目对齐方式
         */
        private String justifyItems;
        
        /**
         * 项目对齐方式
         */
        private String alignItems;
        
        /**
         * 内容对齐方式
         */
        private String justifyContent;
        
        /**
         * 内容对齐方式
         */
        private String alignContent;
    }
    
    /**
     * 响应式规则
     */
    @Data
    public static class ResponsiveRules {
        /**
         * 断点配置
         */
        private Map<String, String> breakpoints;
        
        /**
         * 媒体查询规则
         */
        private Map<String, MediaQuery> mediaQueries;
    }
    
    /**
     * 媒体查询
     */
    @Data
    public static class MediaQuery {
        /**
         * 查询条件
         */
        private String condition;
        
        /**
         * CSS内容
         */
        private String css;
        
        /**
         * 优先级
         */
        private int priority;
    }
}