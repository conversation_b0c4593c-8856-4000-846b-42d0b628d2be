<template>
  <div class="form-container">
    <div v-if="showEditor">
      <div class="editor-header">
        <div class="editor-actions">
          <el-button type="success" size="small" @click="polishSkillContent" :disabled="!formData.content">
            <el-icon class="icon"><magic-stick /></el-icon> AI润色
          </el-button>
        </div>
      </div>

      <div class="editor-content">
        <MdEditor
            v-model="formData.content"
            height="300px"
            :toolbars="toolbars"
            :preview="true"
            :previewTheme="'default'"
            :showCodeRowNumber="true"
            :previewOnly="false"
            :previewWidth="'50%'"
            @onSave="saveForm"
            @change="handleContentChange"
            placeholder="请输入您的技能水平和相关经验描述..."
        />
      </div>

      <div class="tips">
        <el-alert
            title="撰写建议"
            type="info"
            :closable="false"
            show-icon
        >
          <div class="tips-content">
            <p>1. 描述您对该技能的掌握程度和实际应用经验</p>
            <p>2. 提及您使用该技能完成的项目或解决的问题</p>
            <p>3. 如有相关认证或培训经历，可以一并提及</p>
            <p>4. 说明您如何持续提升该技能水平</p>
            <p>5. 可以使用加粗、斜体等富文本格式增强表现力</p>
          </div>
        </el-alert>
      </div>
    </div>

    <!-- AI润色对话框 -->
    <el-dialog
        v-model="polishDialogVisible"
        title="AI润色"
        width="60%"
        :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box" v-html="markdownToHtml(formData.content)"></div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box" v-html="markdownToHtml(polishedContent)"></div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { MdEditor } from 'md-editor-v3'
import { MagicStick, Close, EditPen } from '@element-plus/icons-vue'
import 'md-editor-v3/lib/style.css'
import { getSkillSegmentList, polishSkill } from '../api/resume'

const props = defineProps({
  data: {
    type: String,
    required: true
  },
  skillName: {
    type: String,
    default: ''
  },
  skillLevel: {
    type: Number,
    default: 1
  },
  showEditor: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update'])

// 控制编辑器显示/隐藏
const showEditor = ref(props.showEditor)

// 监听props.showEditor的变化
watch(() => props.showEditor, (newVal) => {
  showEditor.value = newVal
}, { immediate: true })

// 监听技能名称或熟练度变化，获取对应的技能描述
watch(
  [() => props.skillName, () => props.skillLevel],
  async ([newSkillName, newSkillLevel]) => {
    if (newSkillName && newSkillLevel) {
      // 尝试将skillName解析为数字ID
      const skillId = Number.isInteger(Number(newSkillName)) ? Number(newSkillName) : null;
      if (skillId) {
        await fetchSkillDescription(skillId, newSkillLevel);
      } else {
        console.warn('未提供有效的技能ID:', newSkillName);
      }
    }
  },
  { immediate: true }
)

// 将技能等级转换为中文描述
const getLevelText = (level) => {
  const levelTexts = ['一般', '良好', '熟练', '擅长', '精通']
  return levelTexts[level - 1] || '熟练' // 默认返回"熟练"
}

// 获取技能描述
const fetchSkillDescription = async (skillId, skillLevel) => {
  if (!skillId || !skillLevel) return
  
  try {
    // 如果skillId不是数字，表示传入的是名称而不是ID
    const id = Number(skillId) || null
    if (!id) {
      console.error('获取技能描述失败: skillId不是有效的数字ID')
      return
    }
    
    // 将数字等级转换为中文描述
    const proficiency = getLevelText(skillLevel)
    
    const response = await getSkillSegmentList(id, proficiency)
    if (response && response.data && response.data.length > 0) {
      // 更新编辑器内容，使用第一个段落的文本
      formData.value.content = response.data[0].text || ''
    }
  } catch (error) {
    console.error('获取技能描述失败:', error)
  }
}

// 暴露showEditor变量，让父组件可以访问
defineExpose({
  showEditor
})

// 富文本编辑器配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'codeRow',
  'link',
  'save',
  'pageFullscreen',
  'fullscreen',
  'preview',
]

const preview = ref(true)

// 表单数据
const formData = ref({
  content: ''
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 确保content始终是字符串类型
    formData.value.content = typeof newVal === 'object' ? '' : newVal || ''
  } else {
    formData.value.content = ''
  }
}, { immediate: true, deep: true })

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  emit('update', newVal)
}, { immediate: false })

// 隐藏编辑器
const hideEditor = () => {
  showEditor.value = false
  saveForm()
}

// Markdown转HTML的简单实现
const markdownToHtml = (markdown) => {
  if (!markdown) return '';
  return markdown
      // 处理加粗
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // 处理斜体
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理标题
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
      // 处理列表
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
      // 处理段落和换行
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
}

// 润色内容
const polishSkillContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }

  polishDialogVisible.value = true
  polishLoading.value = true

  // 调用后端AI润色API
  polishSkill(formData.value.content).then(res => {
    if (res.code === 0 || res.code === 200) {
      polishedContent.value = res.data
    } else {
      // 显示后端返回的具体错误消息
      const errorMsg = res.message || res.msg || '润色失败'
      ElMessage.error(errorMsg)
      polishedContent.value = formData.value.content
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    // 显示具体的错误消息
    ElMessage.error(error.message || '润色请求失败，请稍后重试')
    polishedContent.value = formData.value.content
    polishLoading.value = false
  })
}

// 获取技能所属分类
const getSkillCategory = (skillName) => {
  if (!skillName) return 'other';

  // 编程语言
  const programmingLanguages = ['JavaScript', 'TypeScript', 'Java', 'Python', 'C++', 'C#', 'Go', 'PHP', 'Ruby', 'Swift', 'Kotlin'];

  // 前端技术
  const frontendTech = ['React', 'Vue.js', 'Angular', 'HTML5', 'CSS3', 'SCSS/SASS', 'WebAssembly', 'Webpack', 'Redux', 'Vuex', 'Next.js', 'Nuxt.js'];

  // 后端技术
  const backendTech = ['Node.js', 'Express', 'Spring Boot', 'Django', 'Flask', 'Laravel', 'Ruby on Rails', '.NET Core', 'NestJS', 'GraphQL', 'RESTful API'];

  // 数据库
  const databaseTech = ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch', 'SQLite', 'Oracle', 'SQL Server', 'Firebase'];

  // 移动开发
  const mobileTech = ['Android原生', 'iOS原生', 'Flutter', 'React Native', '微信小程序', 'UniApp', 'Ionic'];

  // DevOps/云服务
  const devopsTech = ['Docker', 'Kubernetes', 'Jenkins', 'Git', 'AWS', 'Azure', 'Google Cloud', 'Nginx', 'Linux', 'CI/CD'];

  // 数据科学/AI
  const dataScienceTech = ['TensorFlow', 'PyTorch', '机器学习', '深度学习', '数据分析', 'Pandas', 'NumPy', 'Scikit-learn', '自然语言处理', '计算机视觉'];

  if (programmingLanguages.includes(skillName)) return 'programming';
  if (frontendTech.includes(skillName)) return 'frontend';
  if (backendTech.includes(skillName)) return 'backend';
  if (databaseTech.includes(skillName)) return 'database';
  if (mobileTech.includes(skillName)) return 'mobile';
  if (devopsTech.includes(skillName)) return 'devops';
  if (dataScienceTech.includes(skillName)) return 'datascience';

  return 'other';
};

// 获取技能水平描述
const getSkillLevelText = (level) => {
  const levelTextMap = {
    1: '一般',
    2: '良好',
    3: '熟练',
    4: '擅长',
    5: '精通'
  };

  return levelTextMap[level] || '未知';
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value
  }
  polishDialogVisible.value = false
  ElMessage.success('已应用所选内容')
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value.content)
  ElMessage.success('技能描述信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}

// 处理内容变化
const handleContentChange = (newContent) => {
  formData.value.content = newContent
  emit('update', newContent)
}
</script>

<style scoped>
.form-container {
  /* 删除padding属性，使宽度与技能特长模块一致 */
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 5px;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin: 20px 0;
}

.tips-content p {
  margin: 5px 0;
}

.loading-container {
  padding: 20px;
}

.loading-text {
  text-align: center;
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content,
.polished-content {
  flex: 1;
}

.original-content h4,
.polished-content h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.content-box {
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f8f9fb;
  min-height: 150px;
  line-height: 1.6;
}

.polish-options {
  margin-top: 20px;
}

.polish-options h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 