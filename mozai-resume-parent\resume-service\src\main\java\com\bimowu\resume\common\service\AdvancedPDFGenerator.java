package com.bimowu.resume.common.service;


import com.bimowu.resume.dto.ExtractedStyles;

import java.io.OutputStream;
import java.util.Map;

/**
 * 高级PDF生成器接口
 * 支持Flying Saucer引擎的PDF生成
 */
public interface AdvancedPDFGenerator {

    /**
     * 从HTML生成PDF
     * @param htmlContent HTML内容
     * @param outputStream 输出流
     * @return 是否成功
     */
    boolean generatePDFFromHTML(String htmlContent, OutputStream outputStream);
    
    /**
     * 从HTML和CSS生成PDF
     * @param htmlContent HTML内容
     * @param cssContent CSS内容
     * @param outputStream 输出流
     * @return 是否成功
     */
    boolean generatePDFFromHTMLAndCSS(String htmlContent, String cssContent, OutputStream outputStream);
    
    /**
     * 验证HTML内容是否可以生成PDF
     * @param htmlContent HTML内容
     * @return 验证结果
     */
    ValidationResult validateHTML(String htmlContent);
    
    /**
     * 获取生成器信息
     * @return 生成器信息
     */
    GeneratorInfo getGeneratorInfo();
    
    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String[] errors;
        private String[] warnings;
        
        public ValidationResult(boolean valid) {
            this.valid = valid;
        }
        
        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String[] getErrors() { return errors; }
        public void setErrors(String[] errors) { this.errors = errors; }
        
        public String[] getWarnings() { return warnings; }
        public void setWarnings(String[] warnings) { this.warnings = warnings; }
    }
    
    /**
     * 生成器信息
     */
    class GeneratorInfo {
        private String name;
        private String version;
        private String[] supportedFeatures;
        
        // getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public String[] getSupportedFeatures() { return supportedFeatures; }
        public void setSupportedFeatures(String[] supportedFeatures) { this.supportedFeatures = supportedFeatures; }
    }
}