# PDF模板生成系统数据库文档

## 概述

本目录包含PDF模板生成系统相关的数据库脚本和文档。

## 文件说明

### 表结构文件
- `pdf_template_tables.sql` - 完整的表结构定义
- `V1.1__Create_PDF_Template_Tables.sql` - Flyway迁移脚本
- `init_pdf_template_data.sql` - 初始化测试数据

### 表说明

#### 1. resume_template_style_config (模板样式配置表)
存储每个模板的样式配置信息，包括CSS、字体、颜色、布局等。

**主要字段：**
- `template_id` - 模板ID（唯一）
- `css_content` - CSS样式内容
- `font_config` - 字体配置JSON
- `color_palette` - 颜色调色板JSON
- `layout_config` - 布局配置JSON
- `responsive_rules` - 响应式规则JSON
- `version` - 样式版本号
- `enabled` - 是否启用

#### 2. resume_style_sync_record (样式同步记录表)
记录样式同步的历史记录，用于追踪和监控。

**主要字段：**
- `template_id` - 模板ID
- `sync_type` - 同步类型（FULL/INCREMENTAL）
- `status` - 同步状态（SUCCESS/FAILED/PENDING）
- `before_version` - 同步前版本
- `after_version` - 同步后版本
- `duration` - 同步耗时
- `trigger_type` - 触发方式（AUTO/MANUAL）

## 使用说明

### 1. 初始化数据库

```sql
-- 执行表结构创建
source pdf_template_tables.sql;

-- 插入初始数据（可选）
source init_pdf_template_data.sql;
```

### 2. 使用Flyway迁移

如果项目使用Flyway进行数据库版本管理：

```bash
# 将V1.1__Create_PDF_Template_Tables.sql放到db/migration目录
# 运行Flyway迁移
flyway migrate
```

### 3. 常用查询

```sql
-- 查看所有模板的样式配置
SELECT template_id, version, enabled, last_sync_time 
FROM resume_template_style_config;

-- 查看模板同步历史
SELECT template_id, status, sync_time, duration 
FROM resume_style_sync_record 
ORDER BY sync_time DESC;

-- 查看同步成功率
SELECT 
    template_id,
    COUNT(*) as total_syncs,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM resume_style_sync_record 
GROUP BY template_id;
```

## 索引说明

为了提高查询性能，创建了以下索引：

### resume_template_style_config
- `uk_template_id` - 模板ID唯一索引
- `idx_enabled` - 启用状态索引
- `idx_last_sync_time` - 最后同步时间索引
- `idx_version` - 版本索引

### resume_style_sync_record
- `idx_template_id` - 模板ID索引
- `idx_status` - 状态索引
- `idx_sync_time` - 同步时间索引
- `idx_trigger_type` - 触发类型索引
- `idx_template_status` - 模板ID+状态复合索引
- `idx_sync_time_status` - 同步时间+状态复合索引

## 维护建议

1. **定期清理历史记录**：样式同步记录会随时间增长，建议定期清理过期记录
2. **监控同步状态**：定期检查同步失败的记录，及时处理问题
3. **备份重要数据**：模板样式配置是核心数据，需要定期备份
4. **性能监控**：关注查询性能，必要时调整索引策略

## 故障排除

### 常见问题

1. **同步记录过多导致查询慢**
   ```sql
   -- 清理30天前的记录
   DELETE FROM resume_style_sync_record 
   WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
   ```

2. **模板样式配置丢失**
   ```sql
   -- 检查配置是否被禁用
   SELECT * FROM resume_template_style_config WHERE enabled = 0;
   
   -- 恢复配置
   UPDATE resume_template_style_config SET enabled = 1 WHERE template_id = ?;
   ```

3. **版本冲突**
   ```sql
   -- 查看版本历史
   SELECT template_id, before_version, after_version, sync_time 
   FROM resume_style_sync_record 
   WHERE template_id = ? 
   ORDER BY sync_time DESC;
   ```