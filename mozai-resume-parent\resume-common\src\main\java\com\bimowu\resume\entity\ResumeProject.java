package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume_project")
public class ResumeProject implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "pro_id", type = IdType.AUTO)
    private Long proId;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 职位类别id
     */
    private Long catId;

    /**
     * 项目内容（字符串形式，用于兼容）
     */
    private String content;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
