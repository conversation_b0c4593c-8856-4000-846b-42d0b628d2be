#!/usr/bin/env node

/**
 * PDF增强功能验证脚本
 * 用于检查所有必要的文件和配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('=== PDF增强功能验证脚本 ===\n');

// 需要检查的文件列表
const requiredFiles = [
  'src/components/PDFExportButton.vue',
  'src/components/PDFSystemManager.vue',
  'src/views/PDFTestPage.vue',
  'src/api/resume.js',
  'src/router/index.js',
  'src/components/Header.vue',
  'src/views/ResumeEditor.vue'
];

// 需要检查的内容
const contentChecks = [
  {
    file: 'src/components/PDFExportButton.vue',
    patterns: ['exportResumeToPdf', 'previewResumePdf', 'getPdfSystemStatus'],
    description: 'PDFExportButton组件包含必要的API调用'
  },
  {
    file: 'src/components/PDFSystemManager.vue',
    patterns: ['switchPdfSystem', 'getPdfSystemHealth', 'refreshStatus'],
    description: 'PDFSystemManager组件包含系统管理功能'
  },
  {
    file: 'src/api/resume.js',
    patterns: ['exportResumeToPdf', 'previewResumePdf', 'getPdfSystemStatus', 'switchPdfSystem'],
    description: 'API文件包含所有PDF相关接口'
  },
  {
    file: 'src/router/index.js',
    patterns: ['/pdf-system-manager', '/pdf-test', 'PDFSystemManager', 'PDFTestPage'],
    description: '路由配置包含PDF相关页面'
  },
  {
    file: 'src/components/Header.vue',
    patterns: ['系统管理', 'PDF系统管理', 'PDF测试', 'isAdmin'],
    description: 'Header组件包含管理员菜单'
  },
  {
    file: 'src/views/ResumeEditor.vue',
    patterns: ['PDFExportButton', 'onExportSuccess', 'onExportError'],
    description: 'ResumeEditor组件集成了PDFExportButton'
  }
];

let allChecksPass = true;

// 1. 检查文件是否存在
console.log('1. 检查必要文件是否存在...');
for (const file of requiredFiles) {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allChecksPass = false;
  }
}

console.log('');

// 2. 检查文件内容
console.log('2. 检查文件内容...');
for (const check of contentChecks) {
  const filePath = path.join(__dirname, check.file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${check.file} - 文件不存在，跳过内容检查`);
    continue;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let allPatternsFound = true;
    
    for (const pattern of check.patterns) {
      if (!content.includes(pattern)) {
        allPatternsFound = false;
        console.log(`❌ ${check.file} - 缺少内容: ${pattern}`);
      }
    }
    
    if (allPatternsFound) {
      console.log(`✅ ${check.description}`);
    } else {
      allChecksPass = false;
    }
  } catch (error) {
    console.log(`❌ ${check.file} - 读取文件失败: ${error.message}`);
    allChecksPass = false;
  }
}

console.log('');

// 3. 检查package.json依赖
console.log('3. 检查项目依赖...');
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    const requiredDeps = ['vue', 'element-plus', 'vue-router'];
    let depsOk = true;
    
    for (const dep of requiredDeps) {
      if (dependencies[dep]) {
        console.log(`✅ ${dep}: ${dependencies[dep]}`);
      } else {
        console.log(`❌ ${dep} - 依赖缺失`);
        depsOk = false;
      }
    }
    
    if (!depsOk) {
      allChecksPass = false;
    }
  } catch (error) {
    console.log(`❌ package.json 解析失败: ${error.message}`);
    allChecksPass = false;
  }
} else {
  console.log(`❌ package.json 不存在`);
  allChecksPass = false;
}

console.log('');

// 4. 总结
console.log('=== 验证结果 ===');
if (allChecksPass) {
  console.log('🎉 所有检查通过！PDF增强功能已正确配置。');
  console.log('');
  console.log('📋 功能清单:');
  console.log('   ✅ PDFExportButton组件 - PDF导出和预览功能');
  console.log('   ✅ PDFSystemManager组件 - PDF系统管理界面');
  console.log('   ✅ PDFTestPage页面 - PDF功能测试页面');
  console.log('   ✅ API接口更新 - 新增PDF相关接口');
  console.log('   ✅ 路由配置 - 新增管理员路由');
  console.log('   ✅ 导航菜单 - 管理员功能入口');
  console.log('');
  console.log('🚀 可以开始构建和部署了！');
  console.log('   运行: npm run build');
  console.log('   或使用: ./deploy-pdf-enhancement.bat (Windows)');
  console.log('   或使用: ./deploy-pdf-enhancement.sh (Linux/Mac)');
} else {
  console.log('❌ 部分检查未通过，请检查上述错误并修复。');
  process.exit(1);
}

console.log('');