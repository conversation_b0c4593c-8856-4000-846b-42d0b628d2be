package com.bimowu.resume.controller;

import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.vo.ResumeCategoryVo;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bimowu.resume.common.service.ResumeCategoryService;
import com.bimowu.resume.entity.ResumeCategory;

import java.util.ArrayList;
import java.util.List;

/**
 * 职位类别表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/category")
public class ResumeCategoryController {

    @Autowired
    private ResumeCategoryService resumeCategoryService;

    /**
     * 获取所有启用的职位类别列表
     */
    @GetMapping("/list")
    public BaseResponse list() {
        List<ResumeCategoryVo> voList = new ArrayList<>();
        QueryWrapper<ResumeCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1).eq("is_delete", 0).orderByAsc("sort_order");
        List<ResumeCategory> list = resumeCategoryService.list(queryWrapper);
        for (ResumeCategory resumeCategory : list) {
            ResumeCategoryVo resumeCategoryVo = new ResumeCategoryVo();
            BeanUtils.copyProperties(resumeCategory,resumeCategoryVo);
            voList.add(resumeCategoryVo);
        }
        return BaseResponse.ok(voList);
    }
}

