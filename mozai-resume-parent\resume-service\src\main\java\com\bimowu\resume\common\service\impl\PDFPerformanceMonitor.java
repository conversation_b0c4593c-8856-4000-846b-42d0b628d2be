package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.config.PDFConfig;
import com.bimowu.resume.config.PDFPerformanceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * PDF性能监控服务
 */
@Service
@Slf4j
public class PDFPerformanceMonitor {
    
    @Autowired
    private PDFConfig pdfConfig;
    @Autowired
    private PDFPerformanceConfig performanceConfig;
    // 性能统计数据
    private final AtomicInteger totalRequests = new AtomicInteger(0);
    private final AtomicInteger successfulRequests = new AtomicInteger(0);
    private final AtomicInteger failedRequests = new AtomicInteger(0);
    private final AtomicLong totalDuration = new AtomicLong(0);
    private final AtomicLong maxDuration = new AtomicLong(0);
    private final AtomicLong minDuration = new AtomicLong(Long.MAX_VALUE);
    
    // 按模板ID统计
    private final ConcurrentHashMap<String, TemplateStats> templateStats = new ConcurrentHashMap<>();
    
    // 当前并发数
    private final AtomicInteger currentConcurrency = new AtomicInteger(0);
    private final AtomicInteger maxConcurrency = new AtomicInteger(0);
    
    /**
     * 模板统计信息
     */
    private static class TemplateStats {
        private final AtomicInteger requests = new AtomicInteger(0);
        private final AtomicInteger successes = new AtomicInteger(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        
        public void recordRequest(boolean success, long duration) {
            requests.incrementAndGet();
            if (success) {
                successes.incrementAndGet();
            }
            totalDuration.addAndGet(duration);
        }
        
        public double getSuccessRate() {
            int total = requests.get();
            return total > 0 ? (double) successes.get() / total * 100 : 0;
        }
        
        public double getAverageDuration() {
            int total = requests.get();
            return total > 0 ? (double) totalDuration.get() / total : 0;
        }
    }
    
    /**
     * 开始监控一个PDF生成请求
     * 
     * @param templateId 模板ID
     * @param resumeId 简历ID
     * @return 监控上下文
     */
    public MonitorContext startMonitoring(String templateId, String resumeId) {
        if (pdfConfig == null || !pdfConfig.getPerformance().isEnableMonitoring()) {
            return new MonitorContext(templateId, resumeId, false);
        }
        
        totalRequests.incrementAndGet();
        int current = currentConcurrency.incrementAndGet();
        
        // 更新最大并发数
        maxConcurrency.updateAndGet(max -> Math.max(max, current));
        
        // 检查并发限制
        if (current > performanceConfig.getMaxConcurrentGenerations()) {
            log.warn("PDF生成并发数超过限制: 当前={}, 限制={}", current, 
                performanceConfig.getMaxConcurrentGenerations());
        }
        
        return new MonitorContext(templateId, resumeId, true);
    }
    
    /**
     * 结束监控
     * 
     * @param context 监控上下文
     * @param success 是否成功
     * @param error 错误信息（如果失败）
     */
    public void endMonitoring(MonitorContext context, boolean success, String error) {
        if (!context.isEnabled()) {
            return;
        }
        
        long duration = context.getDuration();
        currentConcurrency.decrementAndGet();
        
        // 更新全局统计
        if (success) {
            successfulRequests.incrementAndGet();
        } else {
            failedRequests.incrementAndGet();
        }
        
        totalDuration.addAndGet(duration);
        maxDuration.updateAndGet(max -> Math.max(max, duration));
        minDuration.updateAndGet(min -> Math.min(min, duration));
        
        // 更新模板统计
        templateStats.computeIfAbsent(context.getTemplateId(), k -> new TemplateStats())
            .recordRequest(success, duration);
        
        // 检查慢查询
        long slowThreshold = pdfConfig.getPerformance().getSlowQueryThresholdMs();
        if (duration > slowThreshold) {
            log.warn("检测到慢PDF生成 - 模板ID: {}, 简历ID: {}, 耗时: {}ms, 成功: {}", 
                context.getTemplateId(), context.getResumeId(), duration, success);
        }
        
        // 记录性能日志
        if (log.isDebugEnabled()) {
            log.debug("PDF生成完成 - 模板ID: {}, 简历ID: {}, 耗时: {}ms, 成功: {}, 当前并发: {}", 
                context.getTemplateId(), context.getResumeId(), duration, success, 
                currentConcurrency.get());
        }
    }
    
    /**
     * 获取性能统计报告
     */
    public String getPerformanceReport() {
        if (pdfConfig == null || !pdfConfig.getPerformance().isEnableMonitoring()) {
            return "性能监控未启用";
        }
        
        int total = totalRequests.get();
        int successful = successfulRequests.get();
        int failed = failedRequests.get();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        double avgDuration = total > 0 ? (double) totalDuration.get() / total : 0;
        
        StringBuilder report = new StringBuilder();
        report.append("=== PDF生成性能报告 ===\n");
        report.append(String.format("总请求数: %d\n", total));
        report.append(String.format("成功数: %d\n", successful));
        report.append(String.format("失败数: %d\n", failed));
        report.append(String.format("成功率: %.2f%%\n", successRate));
        report.append(String.format("平均耗时: %.2f ms\n", avgDuration));
        report.append(String.format("最大耗时: %d ms\n", maxDuration.get()));
        report.append(String.format("最小耗时: %d ms\n", minDuration.get() == Long.MAX_VALUE ? 0 : minDuration.get()));
        report.append(String.format("当前并发: %d\n", currentConcurrency.get()));
        report.append(String.format("最大并发: %d\n", maxConcurrency.get()));
        
        // 按模板统计
        if (!templateStats.isEmpty()) {
            report.append("\n=== 按模板统计 ===\n");
            templateStats.forEach((templateId, stats) -> {
                report.append(String.format("模板 %s: 请求=%d, 成功率=%.2f%%, 平均耗时=%.2f ms\n",
                    templateId, stats.requests.get(), stats.getSuccessRate(), stats.getAverageDuration()));
            });
        }
        
        return report.toString();
    }
    
    /**
     * 重置统计数据
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalDuration.set(0);
        maxDuration.set(0);
        minDuration.set(Long.MAX_VALUE);
        maxConcurrency.set(0);
        templateStats.clear();
        
        log.info("性能统计数据已重置");
    }
    
    /**
     * 监控上下文
     */
    public static class MonitorContext {
        private final String templateId;
        private final String resumeId;
        private final long startTime;
        private final boolean enabled;
        
        public MonitorContext(String templateId, String resumeId, boolean enabled) {
            this.templateId = templateId;
            this.resumeId = resumeId;
            this.startTime = System.currentTimeMillis();
            this.enabled = enabled;
        }
        
        public String getTemplateId() {
            return templateId;
        }
        
        public String getResumeId() {
            return resumeId;
        }
        
        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }
        
        public boolean isEnabled() {
            return enabled;
        }
    }
}