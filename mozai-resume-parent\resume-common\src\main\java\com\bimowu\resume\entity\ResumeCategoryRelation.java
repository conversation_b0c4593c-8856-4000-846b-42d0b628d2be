package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
/**
 * 简历与职位类别关联表
 * <AUTHOR>
 * */
@Data
@TableName("resume_category_relation")
public class ResumeCategoryRelation {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private Long resumeId;

    private Long catId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标识(0-未删除,1-已删除)
     */
    @TableLogic
    private Integer isDelete;
}    