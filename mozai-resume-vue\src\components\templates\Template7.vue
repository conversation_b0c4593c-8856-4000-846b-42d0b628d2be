<template>
  <div class="resume-template template-7">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <h1 class="name">{{ resume.modules.basic?.name || '未填写' }}</h1>
        <div class="contact-info">
          <div class="contact-item" v-if="resume.modules.basic?.age">
            <i class="el-icon-user"></i>
            <span>{{ resume.modules.basic.age }}岁</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.phone">
            <i class="el-icon-phone"></i>
            <span>{{ resume.modules.basic?.phone }}</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.email">
            <i class="el-icon-message"></i>
            <span>{{ resume.modules.basic?.email }}</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.address">
            <i class="el-icon-location"></i>
            <span>{{ resume.modules.basic?.address }}</span>
          </div>
        </div>
        <div class="job-objective" v-if="resume.modules.basic?.jobObjective">
          <i class="el-icon-aim"></i>
          <span>{{ resume.modules.basic.jobObjective }}</span>
        </div>
      </div>

      <!-- 教育背景 -->
      <div v-if="hasEducation" class="section">
        <div class="section-header">
          <h2>教育背景</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="item-date">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
            <div class="item-content">
              <div class="item-school">{{ edu.school }}</div>
              <div class="item-major">{{ edu.major }} ({{ edu.degree }})</div>
              <div class="item-description" v-if="edu.courses">
                <ul>
                  <li v-for="(item, i) in formatListItems(edu.courses)" :key="i">{{ item }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业技能 -->
      <div v-if="hasSkills" class="section">
        <div class="section-header">
          <h2>专业技能</h2>
        </div>
        <div class="section-content">
          <ul class="skills-list">
            <li v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
              <span v-html="formatContent(skill.description || skill.name)"></span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 项目经验 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <div class="project-date">{{ project.time[0] }} - {{ project.time[1] || '至今' }}</div>
            </div>
            <div class="project-role">{{ project.role }}</div>
            <div class="project-description">
              <div v-html="formatContent(project.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ practice.name }}</div>
              <div class="project-date">{{ practice.time[0] }} - {{ practice.time[1] || '至今' }}</div>
            </div>
            <div class="project-role">{{ practice.role }}</div>
            <div class="project-description">
              <div v-html="formatContent(practice.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作经验 -->
      <div v-if="hasWork" class="section">
        <div class="section-header">
          <h2>工作经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="work-header">
              <div class="work-title">
                <span class="work-company">{{ work.company }}</span>
                <span class="work-position">{{ work.position }}</span>
              </div>
              <div class="work-date">{{ work.time[0] }} - {{ work.time[1] || '至今' }}</div>
            </div>
            <div class="work-description">
              <div v-html="formatContent(work.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实习经历 -->
      <div v-if="hasInternship" class="section">
        <div class="section-header">
          <h2>实习经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(internship, index) in resume.modules.internship" :key="index" class="internship-item">
            <div class="internship-header">
              <div class="internship-title">
                <span class="internship-company">{{ internship.company }}</span>
                <span class="internship-position">{{ internship.position }}</span>
              </div>
              <div class="internship-date">{{ internship.startDate || formatDate(internship.time?.[0]) }} - {{ internship.endDate || formatDate(internship.time?.[1]) }}</div>
            </div>
            <div class="internship-description">
              <div v-html="formatContent(internship.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 自我评价 -->
      <div v-if="hasEvaluation" class="section">
        <div class="section-header">
          <h2>自我评价</h2>
        </div>
        <div class="section-content">
          <div class="evaluation-content">
            <div v-html="formatContent(resume.modules.evaluation)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      // If not a valid Date object, try to parse as YYYY-MM-DD or YYYY/MM/DD etc.
      const match = date.match(/^(\d{4})[-\.\/年]?(\d{1,2})/);
      if (match && match[1] && match[2]) {
        return `${match[1]}-${match[2].padStart(2, '0')}`; // Return YYYY-MM from string
      }
      return date; // Return original if parsing fails
    }
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && Array.isArray(props.resume.modules.education) && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && Array.isArray(props.resume.modules.work) && props.resume.modules.work.length > 0);
const hasInternship = computed(() => props.resume.modules && Array.isArray(props.resume.modules.internship) && props.resume.modules.internship.length > 0);
const hasProjects = computed(() => props.resume.modules && Array.isArray(props.resume.modules.projects) && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && Array.isArray(props.resume.modules.skills) && props.resume.modules.skills.length > 0 && props.resume.modules.skills.some(s => s.description && s.description.trim() !== ''));
const hasEvaluation = computed(() => props.resume.modules && typeof props.resume.modules.evaluation === 'string' && props.resume.modules.evaluation.trim() !== '');
const hasPractices = computed(() => props.resume.modules && Array.isArray(props.resume.modules.practices) && props.resume.modules.practices.length > 0);

// 格式化列表项
const formatListItems = (text) => {
  if (!text) return [];
  return text.split('\n')
      .filter(item => item.trim() !== '')
      .map(item => item.trim().replace(/^[•·-]\s*/, ''));
};

// 格式化文本内容，支持Markdown格式
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 先处理加粗和斜体，避免被其他处理干扰
  let html = content;

  // 使用非贪婪模式匹配加粗文本，确保正确处理多个加粗文本
  html = html.replace(/\*\*([\s\S]*?)\*\*/g, '<strong>$1</strong>');

  // 处理斜体
  html = html.replace(/\*([\s\S]*?)\*/g, '<em>$1</em>');

  // 处理标题
  html = html
      .replace(/^###\s+(.*?)$/gm, '<h3>$1</h3>')
      .replace(/^##\s+(.*?)$/gm, '<h2>$1</h2>')
      .replace(/^#\s+(.*?)$/gm, '<h1>$1</h1>');

  // 处理列表
  let lines = html.split('\n');
  let inList = false;
  let listContent = '';
  let result = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('-') || line.startsWith('*')) {
      if (!inList) {
        inList = true;
        listContent = '<ul>';
      }
      listContent += `<li>${line.substring(1).trim()}</li>`;
    } else {
      if (inList) {
        inList = false;
        listContent += '</ul>';
        result += listContent;
        listContent = '';
      }

      if (line) {
        result += `<p>${line}</p>`;
      }
    }
  }

  if (inList) {
    listContent += '</ul>';
    result += listContent;
  }

  return result || '<p>' + html + '</p>';
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  color: #333;
  background-color: #fff;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 头部样式 */
.header-section {
  margin-bottom: 20px;
  text-align: center;
}

.name {
  font-size: 28px;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  margin: 0 15px;
}

.contact-item i {
  margin-right: 5px;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 0;
}
.section:not(:last-child) {
  margin-bottom: 8px; /* 只给有内容的模块加小间距 */
}
.section-header {
  margin-bottom: 15px;
  border-bottom: none;
  position: relative;
}

.section-header h2 {
  display: inline-block;
  margin: 0;
  padding: 5px 15px;
  font-size: 18px;
  font-weight: bold;
  color: white;
  background-color: #e74c3c;
  border-radius: 0;
}

.section-header h2::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background-color: #e74c3c;
}

.section-content {
  padding: 0 5px 0 5px; /* 去掉底部padding，靠内容撑开 */
  min-height: 0;
}

/* 教育经历样式 */
.education-item {
  display: flex;
  margin-bottom: 8px; /* 原15px，减少 */
}

.item-date {
  width: 120px;
  font-weight: bold;
  margin-right: 20px;
}

.item-content {
  flex: 1;
}

.item-school {
  font-weight: bold;
  margin-bottom: 5px;
}

.item-major {
  color: #666;
  margin-bottom: 5px;
}

.item-description ul {
  margin: 5px 0 0 0;
  padding-left: 20px;
}

.item-description li {
  margin-bottom: 3px;
}

/* 技能样式 */
.skills-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.skill-item {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
}

.skill-name {
  font-weight: bold;
  margin-right: 10px;
  white-space: nowrap;
}

.skill-description {
  color: #666;
}

/* 项目经验样式 */
.project-item, .work-item, .internship-item, .practice-item {
  margin-bottom: 10px; /* 原20px，减少 */
}

.project-header, .work-header, .internship-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px; /* 原10px，减少 */
}

.project-title, .work-title, .internship-title {
  display: flex;
  flex-direction: column;
}

.project-name, .work-company, .internship-company {
  font-weight: bold;
  margin-bottom: 3px;
}

.project-role, .work-position, .internship-position {
  color: #666;
  font-style: italic;
}

.project-date, .work-date, .internship-date {
  color: #666;
  white-space: nowrap;
}

.project-tech {
  margin-bottom: 8px;
  color: #666;
  font-style: italic;
}

.description-content {
  margin-top: 5px;
}

.description-item {
  margin-bottom: 5px;
  line-height: 1.7;
}

/* 自我评价样式 */
.evaluation-content {
  line-height: 1.7;
}

.evaluation-paragraph {
  margin-bottom: 8px;
}
</style> 