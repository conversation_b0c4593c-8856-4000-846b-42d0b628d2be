package com.bimowu.resume.exception;

/**
 * PDF生成异常
 */
public class PDFGenerationException extends RuntimeException {
    
    private String errorCode;
    private String templateId;
    private String resumeId;
    
    public PDFGenerationException(String message) {
        super(message);
    }
    
    public PDFGenerationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public PDFGenerationException(String errorCode, String message, String templateId, String resumeId) {
        super(message);
        this.errorCode = errorCode;
        this.templateId = templateId;
        this.resumeId = resumeId;
    }
    
    public PDFGenerationException(String errorCode, String message, Throwable cause, String templateId, String resumeId) {
        super(message, cause);
        this.errorCode = errorCode;
        this.templateId = templateId;
        this.resumeId = resumeId;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getTemplateId() {
        return templateId;
    }
    
    public String getResumeId() {
        return resumeId;
    }
    
    @Override
    public String toString() {
        return String.format("PDFGenerationException{errorCode='%s', templateId='%s', resumeId='%s', message='%s'}", 
            errorCode, templateId, resumeId, getMessage());
    }
}