<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
            }
        }
        
        .resume-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 2px solid #2ecc71;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            color: #2ecc71;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .contact-item {
            margin: 0 15px;
            font-size: 14px;
        }
        
        .section {
            margin-bottom: 25px;
            padding: 0 20px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2ecc71;
            position: relative;
        }
        
        .section-title::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: -5px;
            width: 50px;
            height: 3px;
            background-color: #2ecc71;
        }
        
        .item {
            margin-bottom: 20px;
        }
        
        .item-header {
            margin-bottom: 8px;
        }
        
        .item-title {
            font-weight: bold;
            font-size: 16px;
        }
        
        .item-subtitle {
            color: #555;
            font-size: 14px;
            margin: 5px 0;
        }
        
        .item-date {
            color: #777;
            font-size: 14px;
            font-style: italic;
            margin-top: 3px;
        }
        
        .item-content {
            text-align: justify;
            font-size: 14px;
            padding-left: 10px;
            border-left: 3px solid #e0e0e0;
        }
        
        ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .skill-item {
            margin-bottom: 12px;
        }
        
        .skill-name {
            font-weight: bold;
            color: #2ecc71;
        }
        
        .certificate-list,
        .campus-experience,
        .interests,
        .self-evaluation {
            text-align: justify;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 页眉部分 -->
        <div class="header">
            <h1>${name}</h1>
            <div class="contact-info">
                ${contactInfo}
            </div>
        </div>
        
        <!-- 求职意向 -->
        ${jobObjective}
        
        <!-- 教育经历 -->
        ${education}
        
        <!-- 工作经验 -->
        ${work}
        
        <!-- 项目经验 -->
        ${projects}
        
        <!-- 技能特长 -->
        ${skills}
        
        <!-- 证书奖项 -->
        ${certificates}
        
        <!-- 校园经历 -->
        ${campus}
        
        <!-- 兴趣爱好 -->
        ${interests}
        
        <!-- 自我评价 -->
        ${selfEvaluation}
    </div>
</body>
</html> 