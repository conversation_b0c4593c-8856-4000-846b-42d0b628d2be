package com.bimowu.resume.utils;

import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简历数据映射工具类
 * 专门用于将简历数据映射为HTML模板所需的格式
 */
@Component
@Slf4j
public class ResumeDataMapper {
    
    @Autowired
    private PDFGenerationLogger pdfLogger;
    
    /**
     * 映射所有模块数据
     */
    public Map<String, String> mapAllModules(ResumeFullSaveDto resume) {
        String resumeId = resume != null && resume.getResumeVo() != null ?
            String.valueOf(resume.getResumeVo().getResumeId()) : "unknown";

        log.info("开始映射所有模块数据 - resumeId: {}, talentList: {}", resumeId,
            resume != null && resume.getTalentList() != null ? resume.getTalentList().size() : "null");
        Map<String, String> templateData = new HashMap<>();
        
        // 基本信息映射
        mapBasicInfo(resume, templateData, resumeId);
        
        // 教育经历映射
        mapEducation(resume, templateData, resumeId);
        
        // 工作经验映射
        mapWorkExperience(resume, templateData, resumeId);
        
        // 项目经验映射
        mapProjects(resume, templateData, resumeId);
        
        // 练手项目映射
        mapPractices(resume, templateData, resumeId);
        
        // 技能特长映射
        log.error("准备调用mapSkills - talentList: {}", resume.getTalentList() != null ? resume.getTalentList().size() : "null");
        mapSkills(resume, templateData, resumeId);
        log.error("mapSkills调用完成");
        
        // 证书奖项映射
        mapCertificates(resume, templateData, resumeId);
        
        // 校园经历映射
        mapCampus(resume, templateData, resumeId);
        
        // 兴趣爱好映射
        mapInterests(resume, templateData, resumeId);
        
        // 自我评价映射
        mapSelfEvaluation(resume, templateData, resumeId);
        
        log.info("所有模块数据映射完成，共生成 {} 个模板变量 - resumeId: {}", templateData.size(), resumeId);
        return templateData;
    }
    
    /**
     * 基本信息映射
     */
    private void mapBasicInfo(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        if (resume.getInformation() != null) {
            ResumeInformationVo info = resume.getInformation();
            
            pdfLogger.logDataMappingTrace(resumeId, "基本信息", "开始映射", info, null);
            
            templateData.put("name", escapeHtml(info.getName() != null ? info.getName() : "求职者"));
            templateData.put("age", info.getAge() != null ? info.getAge().toString() : "");
            templateData.put("gender", escapeHtml(info.getGender() != null ? info.getGender() : ""));
            templateData.put("phone", escapeHtml(info.getPhone() != null ? info.getPhone() : ""));
            templateData.put("email", escapeHtml(info.getEmail() != null ? info.getEmail() : ""));
            templateData.put("hometown", escapeHtml(info.getHometown() != null ? info.getHometown() : ""));
            templateData.put("jobObjective", escapeHtml(info.getJobObjective() != null ? info.getJobObjective() : ""));
            
            // 头像处理
            if (info.getAvatar() != null && !info.getAvatar().isEmpty()) {
                templateData.put("avatar", "<img src=\"" + info.getAvatar() + "\" alt=\"头像\" class=\"photo\" />");
            } else {
                templateData.put("avatar", "");
            }
            
            pdfLogger.logConditionalDisplay(resumeId, "基本信息", true, "包含完整基本信息");
            log.info("基本信息映射完成 - 姓名: {}, 电话: {}", info.getName(), info.getPhone());
        } else {
            // 设置默认值
            templateData.put("name", "求职者");
            templateData.put("age", "");
            templateData.put("gender", "");
            templateData.put("phone", "");
            templateData.put("email", "");
            templateData.put("hometown", "");
            templateData.put("jobObjective", "");
            templateData.put("avatar", "");
            
            pdfLogger.logConditionalDisplay(resumeId, "基本信息", false, "基本信息为空，使用默认值");
            log.info("基本信息为空，使用默认值");
        }
    }
    
    /**
     * 教育经历映射
     */
    private void mapEducation(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeEducationalVo> educationList = resume.getEducationList();
        if (educationList != null && !educationList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "教育经历", "开始映射", educationList, null);
            StringBuilder educationHtml = new StringBuilder();
            educationHtml.append("<div class=\"resume-section\">");
            educationHtml.append("<div class=\"section-header\">");
            educationHtml.append("<h2>教育经历</h2>");
            educationHtml.append("</div>");
            educationHtml.append("<div class=\"education-content\">");
            
            for (ResumeEducationalVo edu : educationList) {
                educationHtml.append("<div class=\"education-item\">");
                educationHtml.append("<div class=\"edu-header\">");
                
                // 时间
                educationHtml.append("<span class=\"edu-date\">")
                    .append(escapeHtml(edu.getTimePeriod() != null ? edu.getTimePeriod() : ""))
                    .append("</span>");

                // 学校
                educationHtml.append("<span class=\"edu-school\">")
                    .append(escapeHtml(edu.getSchool() != null ? edu.getSchool() : ""))
                    .append("</span>");

                // 学历和专业
                String degreeAndMajor = "";
                if (edu.getEducation() != null && !edu.getEducation().isEmpty()) {
                    degreeAndMajor += escapeHtml(edu.getEducation());
                }
                if (edu.getMajor() != null && !edu.getMajor().isEmpty()) {
                    if (!degreeAndMajor.isEmpty()) {
                        degreeAndMajor += "，";
                    }
                    degreeAndMajor += escapeHtml(edu.getMajor());
                }
                educationHtml.append("<span class=\"edu-degree\">").append(degreeAndMajor).append("</span>");
                
                educationHtml.append("</div>"); // End of edu-header
                
                // 主修课程
                if (edu.getMainCourses() != null && !edu.getMainCourses().trim().isEmpty()) {
                    educationHtml.append("<div class=\"edu-info\">");
                    educationHtml.append("<div class=\"edu-courses\">");
                    educationHtml.append("<span class=\"courses-label\">主修课程：</span>");
                    educationHtml.append(formatContent(edu.getMainCourses(), resumeId));
                    educationHtml.append("</div>");
                    educationHtml.append("</div>");
                }
                
                educationHtml.append("</div>"); // End of education-item
            }
            
            educationHtml.append("</div>"); // End of education-content
            educationHtml.append("</div>"); // End of resume-section
            
            String finalHtml = educationHtml.toString();
            templateData.put("education", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "教育经历", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "教育经历", true, educationList.size() + "条记录");
            log.debug("教育经历映射完成 - {} 条记录", educationList.size());
        } else {
            templateData.put("education", "");
            pdfLogger.logConditionalDisplay(resumeId, "教育经历", false, "教育经历列表为空");
            log.debug("教育经历为空");
        }
    }
    
    /**
     * 工作经验映射
     */
    private void mapWorkExperience(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeWorkVo> workList = resume.getWorkList();
        log.info("mapWorkExperience被调用，workList: {}", workList != null ? workList.size() + "条记录" : "null");
        if (workList != null && !workList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "工作经验", "开始映射", workList, null);
            StringBuilder workHtml = new StringBuilder();
            workHtml.append("<div class=\"resume-section\">");
            workHtml.append("<div class=\"section-header\">");
            workHtml.append("<h2>工作经验</h2>");
            workHtml.append("</div>");
            workHtml.append("<div class=\"work-content\">");
            
            for (ResumeWorkVo work : workList) {
                workHtml.append("<div class=\"work-item\">");
                workHtml.append("<div class=\"work-header\">");
                
                // 时间
                workHtml.append("<div class=\"work-time\">")
                    .append(escapeHtml(work.getTimePeriod() != null ? work.getTimePeriod() : ""))
                    .append("</div>");
                
                // 公司
                workHtml.append("<div class=\"work-company\">")
                    .append(escapeHtml(work.getCompany() != null ? work.getCompany() : ""))
                    .append("</div>");
                
                // 职位
                workHtml.append("<div class=\"work-position\">")
                    .append(escapeHtml(work.getPosition() != null ? work.getPosition() : ""))
                    .append("</div>");
                
                workHtml.append("</div>"); // End of work-header
                
                // 工作描述
                if (work.getWorkDescription() != null && !work.getWorkDescription().trim().isEmpty()) {
                    log.info("处理工作描述: {}", work.getWorkDescription());
                    workHtml.append("<div class=\"work-description\">");
                    workHtml.append(formatContent(work.getWorkDescription(), resumeId));
                    workHtml.append("</div>");
                } else {
                    log.info("工作描述为空或null");
                }
                
                workHtml.append("</div>"); // End of work-item
            }
            
            workHtml.append("</div>"); // End of work-content
            workHtml.append("</div>"); // End of resume-section
            
            String finalHtml = workHtml.toString();
            templateData.put("work", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "工作经验", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "工作经验", true, workList.size() + "条记录");
            log.debug("工作经验映射完成 - {} 条记录", workList.size());
        } else {
            templateData.put("work", "");
            pdfLogger.logConditionalDisplay(resumeId, "工作经验", false, "工作经验列表为空");
            log.debug("工作经验为空");
        }
    }
    
    /**
     * 项目经验映射
     */
    private void mapProjects(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeProjectExperienceVo> projectList = resume.getProjectList();
        if (projectList != null && !projectList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "项目经验", "开始映射", projectList, null);
            StringBuilder projectHtml = new StringBuilder();
            projectHtml.append("<div class=\"resume-section\">");
            projectHtml.append("<div class=\"section-header\">");
            projectHtml.append("<h2>项目经验</h2>");
            projectHtml.append("</div>");
            projectHtml.append("<div class=\"work-content\">");
            
            for (ResumeProjectExperienceVo project : projectList) {
                projectHtml.append("<div class=\"project-item\">");
                projectHtml.append("<div class=\"work-header\">");
                
                // 时间
                projectHtml.append("<div class=\"project-date\">")
                    .append(escapeHtml(project.getTimePeriod() != null ? project.getTimePeriod() : ""))
                    .append("</div>");
                
                // 项目名称
                projectHtml.append("<div class=\"project-name\">")
                    .append(escapeHtml(project.getProjectName() != null ? project.getProjectName() : ""))
                    .append("</div>");
                
                // 角色
                projectHtml.append("<div class=\"project-role\">")
                    .append(escapeHtml(project.getRole() != null ? project.getRole() : ""))
                    .append("</div>");
                
                projectHtml.append("</div>"); // End of work-header
                
                // 项目描述
                if (project.getProjectDescription() != null && !project.getProjectDescription().trim().isEmpty()) {
                    projectHtml.append("<div class=\"project-description\">");
                    projectHtml.append(formatContent(project.getProjectDescription(), resumeId));
                    projectHtml.append("</div>");
                }
                
                projectHtml.append("</div>"); // End of project-item
            }
            
            projectHtml.append("</div>"); // End of work-content
            projectHtml.append("</div>"); // End of resume-section
            
            String finalHtml = projectHtml.toString();
            templateData.put("projects", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "项目经验", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "项目经验", true, projectList.size() + "条记录");
            log.debug("项目经验映射完成 - {} 条记录", projectList.size());
        } else {
            templateData.put("projects", "");
            pdfLogger.logConditionalDisplay(resumeId, "项目经验", false, "项目经验列表为空");
            log.debug("项目经验为空");
        }
    }
    
    /**
     * 练手项目映射
     */
    private void mapPractices(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumePracticeVo> practiceList = resume.getPracticeList();
        if (practiceList != null && !practiceList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "练手项目", "开始映射", practiceList, null);
            StringBuilder practiceHtml = new StringBuilder();
            practiceHtml.append("<div class=\"resume-section\">");
            practiceHtml.append("<div class=\"section-header\">");
            practiceHtml.append("<h2>练手项目</h2>");
            practiceHtml.append("</div>");
            practiceHtml.append("<div class=\"work-content\">");
            
            for (ResumePracticeVo practice : practiceList) {
                practiceHtml.append("<div class=\"project-item\">");
                practiceHtml.append("<div class=\"work-header\">");
                
                // 时间
                practiceHtml.append("<div class=\"project-date\">")
                    .append(escapeHtml(practice.getTimePeriod() != null ? practice.getTimePeriod() : ""))
                    .append("</div>");
                
                // 项目名称
                practiceHtml.append("<div class=\"project-name\">")
                    .append(escapeHtml(practice.getProjectName() != null ? practice.getProjectName() : ""))
                    .append("</div>");
                
                // 角色
                practiceHtml.append("<div class=\"project-role\">")
                    .append(escapeHtml(practice.getRole() != null ? practice.getRole() : ""))
                    .append("</div>");
                
                practiceHtml.append("</div>"); // End of work-header
                
                // 项目URL
                if (practice.getProjectUrl() != null && !practice.getProjectUrl().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-url\">项目地址：")
                        .append(escapeHtml(practice.getProjectUrl()))
                        .append("</div>");
                }
                
                // 项目描述
                if (practice.getProjectDescription() != null && !practice.getProjectDescription().trim().isEmpty()) {
                    practiceHtml.append("<div class=\"project-description\">");
                    practiceHtml.append(formatContent(practice.getProjectDescription(), resumeId));
                    practiceHtml.append("</div>");
                }
                
                practiceHtml.append("</div>"); // End of project-item
            }
            
            practiceHtml.append("</div>"); // End of work-content
            practiceHtml.append("</div>"); // End of resume-section
            
            String finalHtml = practiceHtml.toString();
            templateData.put("practices", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "练手项目", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "练手项目", true, practiceList.size() + "条记录");
            log.debug("练手项目映射完成 - {} 条记录", practiceList.size());
        } else {
            templateData.put("practices", "");
            pdfLogger.logConditionalDisplay(resumeId, "练手项目", false, "练手项目列表为空");
            log.debug("练手项目为空");
        }
    }
    
    /**
     * 技能特长映射
     */
    private void mapSkills(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        List<ResumeTalentVo> skillList = resume.getTalentList();
        log.error("mapSkills被调用 - skillList: {}", skillList != null ? skillList.size() : "null");
        if (skillList != null && !skillList.isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "技能特长", "开始映射", skillList, null);
            StringBuilder skillHtml = new StringBuilder();
            skillHtml.append("<div class=\"resume-section\">");
            skillHtml.append("<div class=\"section-header\">");
            skillHtml.append("<h2>技能特长</h2>");
            skillHtml.append("</div>");
            skillHtml.append("<div class=\"skills-content\">");
            skillHtml.append("<div class=\"skills-description\">");
            
            for (ResumeTalentVo skill : skillList) {
                if (skill.getSkillDescription() != null && !skill.getSkillDescription().trim().isEmpty()) {
                    skillHtml.append("<div class=\"skill-description-item\">");
                    skillHtml.append("<div class=\"skill-description-body\">");
                    skillHtml.append(formatContent(skill.getSkillDescription(), resumeId));
                    skillHtml.append("</div>");
                    skillHtml.append("</div>");
                }
            }
            
            skillHtml.append("</div>"); // End of skills-description
            skillHtml.append("</div>"); // End of skills-content
            skillHtml.append("</div>"); // End of resume-section
            
            String finalHtml = skillHtml.toString();
            templateData.put("skills", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "技能特长", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "技能特长", true, skillList.size() + "条记录");
            log.debug("技能特长映射完成 - {} 条记录", skillList.size());
        } else {
            templateData.put("skills", "");
            pdfLogger.logConditionalDisplay(resumeId, "技能特长", false, "技能特长列表为空");
            log.debug("技能特长为空");
        }
    }
    
    /**
     * 证书奖项映射
     */
    private void mapCertificates(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCertificateVo certificate = resume.getCertificate();
        if (certificate != null && certificate.getCertificateName() != null && !certificate.getCertificateName().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "证书奖项", "开始映射", certificate, null);
            StringBuilder certHtml = new StringBuilder();
            certHtml.append("<div class=\"resume-section\">");
            certHtml.append("<div class=\"section-header\">");
            certHtml.append("<h2>荣誉证书</h2>");
            certHtml.append("</div>");
            certHtml.append("<div class=\"certificate-content\">");
            certHtml.append(formatContent(certificate.getCertificateName(), resumeId));
            certHtml.append("</div>"); // End of certificate-content
            certHtml.append("</div>"); // End of resume-section
            
            String finalHtml = certHtml.toString();
            templateData.put("certificates", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "证书奖项", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项", true, "包含证书信息");
            log.debug("证书奖项映射完成");
        } else {
            templateData.put("certificates", "");
            pdfLogger.logConditionalDisplay(resumeId, "证书奖项", false, "证书奖项为空");
            log.debug("证书奖项为空");
        }
    }
    
    /**
     * 校园经历映射
     */
    private void mapCampus(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeCampusVo campus = resume.getCampus();
        if (campus != null && campus.getCampusExperience() != null && !campus.getCampusExperience().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "校园经历", "开始映射", campus, null);
            StringBuilder campusHtml = new StringBuilder();
            campusHtml.append("<div class=\"resume-section\">");
            campusHtml.append("<div class=\"section-header\">");
            campusHtml.append("<h2>校园经历</h2>");
            campusHtml.append("</div>");
            campusHtml.append("<div class=\"campus-content\">");
            campusHtml.append(formatContent(campus.getCampusExperience(), resumeId));
            campusHtml.append("</div>"); // End of campus-content
            campusHtml.append("</div>"); // End of resume-section
            
            String finalHtml = campusHtml.toString();
            templateData.put("campus", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "校园经历", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "校园经历", true, "包含校园经历信息");
            log.debug("校园经历映射完成");
        } else {
            templateData.put("campus", "");
            pdfLogger.logConditionalDisplay(resumeId, "校园经历", false, "校园经历为空");
            log.debug("校园经历为空");
        }
    }
    
    /**
     * 兴趣爱好映射
     */
    private void mapInterests(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeInterestVo interest = resume.getInterest();
        if (interest != null && interest.getInterest() != null && !interest.getInterest().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "兴趣爱好", "开始映射", interest, null);
            StringBuilder interestsHtml = new StringBuilder();
            interestsHtml.append("<div class=\"resume-section\">");
            interestsHtml.append("<div class=\"section-header\">");
            interestsHtml.append("<h2>兴趣爱好</h2>");
            interestsHtml.append("</div>");
            interestsHtml.append("<div class=\"interests-content\">");
            interestsHtml.append(formatContent(interest.getInterest(), resumeId));
            interestsHtml.append("</div>"); // End of interests-content
            interestsHtml.append("</div>"); // End of resume-section
            
            String finalHtml = interestsHtml.toString();
            templateData.put("interests", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "兴趣爱好", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好", true, "包含兴趣爱好信息");
            log.debug("兴趣爱好映射完成");
        } else {
            templateData.put("interests", "");
            pdfLogger.logConditionalDisplay(resumeId, "兴趣爱好", false, "兴趣爱好为空");
            log.debug("兴趣爱好为空");
        }
    }
    
    /**
     * 自我评价映射
     */
    private void mapSelfEvaluation(ResumeFullSaveDto resume, Map<String, String> templateData, String resumeId) {
        ResumeEvaluateVo evaluate = resume.getEvaluate();
        if (evaluate != null && evaluate.getSelfEvaluation() != null && !evaluate.getSelfEvaluation().trim().isEmpty()) {
            pdfLogger.logDataMappingTrace(resumeId, "自我评价", "开始映射", evaluate, null);
            StringBuilder evalHtml = new StringBuilder();
            evalHtml.append("<div class=\"resume-section\">");
            evalHtml.append("<div class=\"section-header\">");
            evalHtml.append("<h2>个人评价</h2>");
            evalHtml.append("</div>");
            evalHtml.append("<div class=\"evaluation-content\">");
            evalHtml.append(formatContent(evaluate.getSelfEvaluation(), resumeId));
            evalHtml.append("</div>"); // End of evaluation-content
            evalHtml.append("</div>"); // End of resume-section
            
            String finalHtml = evalHtml.toString();
            templateData.put("selfEvaluation", finalHtml);
            pdfLogger.logHtmlContentGeneration(resumeId, "自我评价", finalHtml);
            pdfLogger.logConditionalDisplay(resumeId, "自我评价", true, "包含自我评价信息");
            log.debug("自我评价映射完成");
        } else {
            templateData.put("selfEvaluation", "");
            pdfLogger.logConditionalDisplay(resumeId, "自我评价", false, "自我评价为空");
            log.debug("自我评价为空");
        }
    }
    
    /**
     * 内容格式化方法
     */
    private String formatContent(String content) {
        return formatContent(content, "unknown");
    }
    
    /**
     * 内容格式化方法（带调试日志）
     */
    private String formatContent(String content, String resumeId) {
        log.info("=== formatContent方法被调用 ===");
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        String originalContent = content;
        System.out.println("formatContent处理前: " + content);

        // 首先处理Markdown格式
        // 处理加粗 **text** -> <strong>text</strong>
        content = content.replaceAll("\\*\\*(.*?)\\*\\*", "<strong>$1</strong>");
        log.info("加粗处理后: " + content);

        // 处理斜体 *text* -> <em>text</em>
        content = content.replaceAll("\\*(.*?)\\*", "<em>$1</em>");

        // 处理换行符，转换为HTML格式
        content = content.replace("\n", "<br>");
        
        // 处理列表项
        if (content.contains("•") || content.contains("-")) {
            String[] lines = content.split("<br>");
            StringBuilder formatted = new StringBuilder();
            boolean inList = false;
            
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("•") || line.startsWith("-")) {
                    if (!inList) {
                        formatted.append("<ul>");
                        inList = true;
                    }
                    formatted.append("<li>").append(line.substring(1).trim()).append("</li>");
                } else {
                    if (inList) {
                        formatted.append("</ul>");
                        inList = false;
                    }
                    if (!line.isEmpty()) {
                        formatted.append("<p>").append(line).append("</p>");
                    }
                }
            }
            
            if (inList) {
                formatted.append("</ul>");
            }
            
            return formatted.toString();
        }
        
        String formattedContent = "<p>" + content + "</p>";
        log.info("formatContent最终结果: {}", formattedContent);

        // 记录内容格式化过程
        if (pdfLogger != null) {
            pdfLogger.logContentFormatting(resumeId, "内容格式化", originalContent, formattedContent);
        }

        return formattedContent;
    }
    
    /**
     * 转义HTML特殊字符
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }
    
    /**
     * 日期格式化
     */
    private String formatDate(Date date) {
        if (date == null) return "至今";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }
}