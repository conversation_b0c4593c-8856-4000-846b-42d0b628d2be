package com.bimowu.resume.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.common.service.ResumeSkillSegmentService;
import com.bimowu.resume.entity.ResumeSkillSegment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 技能段落表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/skill-segment")
@Slf4j
public class ResumeSkillSegmentController {

    @Autowired
    private ResumeSkillSegmentService resumeSkillSegmentService;

    /**
     * 根据技能ID和熟练度获取技能段落
     * @param skillId 技能ID
     * @param proficiency 熟练度
     * @return 技能段落列表
     */
    @GetMapping("/list")
    public BaseResponse getSkillSegments(
            @RequestParam("skillId") Long skillId,
            @RequestParam("proficiency") String proficiency) {
        log.info("获取技能段落, skillId={}, proficiency={}", skillId, proficiency);
        QueryWrapper<ResumeSkillSegment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("skill_id", skillId)
                .eq("proficiency", proficiency);
        List<ResumeSkillSegment> segments = resumeSkillSegmentService.list(queryWrapper);
        log.info("找到技能段落: {}", segments.size());
        return BaseResponse.ok(segments);
    }
}

