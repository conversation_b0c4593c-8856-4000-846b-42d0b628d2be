package com.bimowu;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;

@SpringBootApplication
@MapperScan(value = "com.bimowu.resume.common.dao")
@ServletComponentScan(basePackages = "com.bimowu.resume.common.filter")
public class ResumeWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(ResumeWebApplication.class, args);
    }
}