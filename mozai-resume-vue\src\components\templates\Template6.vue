<template>
  <div class="resume-template template-6">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <div class="personal-info">
          <div class="info-row">
            <div class="info-item"><span class="label">姓名：</span>{{ resume.modules.basic?.name || '未填写' }}</div>
            <div class="info-item"><span class="label">性别：</span>{{ resume.modules.basic?.gender || '未填写' }}</div>
          </div>
          <div class="info-row">
            <div class="info-item"><span class="label">年龄：</span>{{ resume.modules.basic?.age || '未填写' }}</div>
            <div class="info-item"><span class="label">电话：</span>{{ resume.modules.basic?.phone || '未填写' }}</div>
          </div>
          <div class="info-row">
            <div class="info-item"><span class="label">邮箱：</span>{{ resume.modules.basic?.email || '未填写' }}</div>
          </div>
          <div class="info-row">
            <div class="info-item full-width"><span class="label">求职意向：</span>{{ resume.modules.basic?.jobObjective || '未填写' }}</div>
          </div>
        </div>
        <div class="avatar-container">
          <img v-if="resume.modules.basic?.avatar" :src="resume.modules.basic.avatar" alt="头像" class="avatar" />
          <img v-else src="/images/default-avatar.svg" alt="默认照片" class="avatar"/>
        </div>
      </div>

      <div class="content-section">
        <!-- 左侧栏 -->
        <div class="left-column">
          <!-- 教育经历 -->
          <div v-if="hasEducation" class="section">
            <div class="section-header">
              <h2>教育经历 / EDUCATIONAL EXPERIENCE</h2>
            </div>
            <div class="section-content">
              <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
                <div class="item-header">
                  <div class="item-school">{{ edu.school }}</div>
                  <div class="item-date">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</div>
                </div>
                <div class="item-details">
                  <div class="item-major">专业：{{ edu.major }}</div>
                  <div class="item-degree">学历：{{ edu.degree }}</div>
                  <div v-if="edu.courses" class="item-description">
                    <div v-for="(line, i) in formatListItems(edu.courses)" :key="i" class="description-line">
                      • {{ line }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 专业技能 -->
          <div v-if="hasSkills" class="section">
            <div class="section-header">
              <h2>专业技能 / PROFESSIONAL SKILLS</h2>
            </div>
            <div class="section-content">
              <ul class="skills-list">
                <li v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
                  • <span v-html="formatContent(skill.description || skill.name)"></span>
                </li>
              </ul>
            </div>
          </div>

          <!-- 实习经历 -->
          <div v-if="hasWork" class="section">
            <div class="section-header">
              <h2>实习经历 / INTERNSHIP EXPERIENCE</h2>
            </div>
            <div class="section-content">
              <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
                <div class="item-header">
                  <div class="item-company">{{ work.company }}</div>
                  <div class="item-date">{{ work.time[0] }} - {{ work.time[1] || '至今' }}</div>
                </div>
                <div class="item-position">{{ work.position }}</div>
                <div class="item-description">
                  <div class="work-description">
                    <div v-html="formatContent(work.description)"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧栏 -->
        <div class="right-column">
          <!-- 项目经验 -->
          <div v-if="hasProjects" class="section">
            <div class="section-header">
              <h2>项目经验</h2>
            </div>
            <div class="section-content">
              <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
                <div class="project-header">
                  <div class="project-title">{{ project.name }}</div>
                  <div class="project-date">{{ project.time[0] }} - {{ project.time[1] || '至今' }}</div>
                </div>
                <div class="project-role">{{ project.role }}</div>
                <div class="project-description">
                  <div v-html="formatContent(project.description)"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 练手项目 -->
          <div v-if="hasPractices" class="section">
            <div class="section-header">
              <h2>练手项目</h2>
            </div>
            <div class="section-content">
              <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
                <div class="project-header">
                  <div class="project-title">{{ practice.name }}</div>
                  <div class="project-date">{{ practice.time[0] }} - {{ practice.time[1] || '至今' }}</div>
                </div>
                <div class="project-role">{{ practice.role }}</div>
                <div class="project-description">
                  <div v-html="formatContent(practice.description)"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 校园经历 -->
          <div v-if="hasCampus" class="section">
            <div class="section-header">
              <h2>校园经历 / CAMPUS EXPERIENCE</h2>
            </div>
            <div class="section-content">
              <div class="campus-content">
                <div v-html="formatContent(resume.modules.campus)"></div>
              </div>
            </div>
          </div>

          <!-- 自我评价 -->
          <div v-if="hasEvaluation" class="section">
            <div class="section-header">
              <h2>自我评价 / SELF ASSESSMENT</h2>
            </div>
            <div class="section-content">
              <div class="evaluation-content">
                <div v-html="formatContent(resume.modules.evaluation)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      // If not a valid Date object, try to parse as YYYY-MM-DD or YYYY/MM/DD etc.
      const match = date.match(/^(\d{4})[-\.\/年]?(\d{1,2})/);
      if (match && match[1] && match[2]) {
        return `${match[1]}-${match[2].padStart(2, '0')}`; // Return YYYY-MM from string
      }
      return date; // Return original if parsing fails
    }
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasEvaluation = computed(() => props.resume.modules && props.resume.modules.evaluation && typeof props.resume.modules.evaluation === 'string' && props.resume.modules.evaluation.trim() !== '');
const hasCampus = computed(() => props.resume.modules && props.resume.modules.campus && typeof props.resume.modules.campus === 'string' && props.resume.modules.campus.trim() !== '');
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化列表项
const formatListItems = (text) => {
  if (!text) return [];
  return text.split('\n')
      .filter(item => item.trim() !== '')
      .map(item => item.trim().replace(/^[•·-]\s*/, ''));
};

// 格式化文本内容，支持Markdown格式
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // 先处理加粗和斜体，避免被其他处理干扰
  let html = content;

  // 使用非贪婪模式匹配加粗文本，确保正确处理多个加粗文本
  html = html.replace(/\*\*([\s\S]*?)\*\*/g, '<strong>$1</strong>');

  // 处理斜体
  html = html.replace(/\*([\s\S]*?)\*/g, '<em>$1</em>');

  // 处理标题
  html = html
      .replace(/^###\s+(.*?)$/gm, '<h3>$1</h3>')
      .replace(/^##\s+(.*?)$/gm, '<h2>$1</h2>')
      .replace(/^#\s+(.*?)$/gm, '<h1>$1</h1>');

  // 处理列表
  let lines = html.split('\n');
  let inList = false;
  let listContent = '';
  let result = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('-') || line.startsWith('*')) {
      if (!inList) {
        inList = true;
        listContent = '<ul>';
      }
      listContent += `<li>${line.substring(1).trim()}</li>`;
    } else {
      if (inList) {
        inList = false;
        listContent += '</ul>';
        result += listContent;
        listContent = '';
      }

      if (line) {
        result += `<p>${line}</p>`;
      }
    }
  }

  if (inList) {
    listContent += '</ul>';
    result += listContent;
  }

  return result || '<p>' + html + '</p>';
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  color: #333;
  background-color: #fff;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 头部个人信息样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #2e8b57;
}

.personal-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-item {
  flex: 1;
  min-width: 0;
}

.label {
  font-weight: bold;
  color: #2e8b57;
}

.avatar-container {
  width: 120px;
  height: 160px;
  margin-left: 20px;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #ccc;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
}

/* 内容区域布局 */
.content-section {
  display: flex;
  gap: 20px;
}

.left-column {
  flex: 1;
  padding-right: 10px;
}

.right-column {
  flex: 1;
  padding-left: 10px;
  border-left: 1px solid #ddd;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 25px;
}

.section-header {
  margin-bottom: 15px;
  border-bottom: 2px solid #2e8b57;
}

.section-header h2 {
  display: inline-block;
  margin: 0;
  padding: 5px 0;
  font-size: 16px;
  font-weight: bold;
  color: #2e8b57;
}

.section-content {
  padding: 0 5px;
}

/* 教育经历样式 */
.education-item {
  margin-bottom: 15px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-school, .item-company, .item-name {
  font-weight: bold;
}

.item-date {
  color: #666;
}

.item-details {
  margin-left: 10px;
}

.item-major, .item-degree {
  margin-bottom: 5px;
}

/* 技能列表样式 */
.skills-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.skill-item {
  margin-bottom: 8px;
  line-height: 1.7;
}

/* 工作经历样式 */
.work-item {
  margin-bottom: 20px;
}

.item-position {
  margin: 5px 0 10px 0;
  font-weight: bold;
  color: #555;
}

/* 项目经验样式 */
.project-item {
  margin-bottom: 20px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.project-title {
  font-weight: bold;
}

.project-date {
  color: #666;
}

.project-role {
  margin: 5px 0 10px 0;
  font-style: italic;
  color: #555;
}

/* 描述内容样式 */
.project-description {
  margin-top: 8px;
}

.description-line {
  margin-bottom: 5px;
  line-height: 1.7;
}

/* 校园经历和自我评价样式 */
.campus-content, .evaluation-content {
  line-height: 1.7;
}

.text-line {
  margin-bottom: 8px;
}

/* 添加 MdPreview 相关样式 */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 20px;
}

:deep(.md-preview-html li) {
  margin: 0;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}
</style> 