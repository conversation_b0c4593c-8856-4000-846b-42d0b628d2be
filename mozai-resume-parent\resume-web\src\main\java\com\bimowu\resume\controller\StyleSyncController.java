package com.bimowu.resume.controller;

import com.bimowu.resume.dto.ExtractedStyles;
import com.bimowu.resume.entity.StyleSyncRecord;
import com.bimowu.resume.common.service.AutoStyleUpdateService;
import com.bimowu.resume.common.service.StyleSyncService;
import com.bimowu.resume.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 样式同步控制器
 * 提供前端调用的样式同步接口
 */
@Slf4j
@RestController
@RequestMapping("/api/style-sync")
// @Api(tags = "样式同步管理")
public class StyleSyncController {
    
    @Autowired
    private StyleSyncService styleSyncService;
    
    @Autowired
    private AutoStyleUpdateService autoStyleUpdateService;
    
    /**
     * 同步模板样式
     */
    @PostMapping("/sync/{templateId}")
    // @ApiOperation("同步指定模板的样式")
    public Result<StyleSyncRecord> syncTemplateStyles(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId,
            /* @ApiParam("提取的样式信息") */ @RequestBody ExtractedStyles extractedStyles) {
        
        try {
            log.info("接收到模板 {} 的样式同步请求", templateId);
            
            StyleSyncRecord syncRecord = styleSyncService.syncTemplateStyles(templateId, extractedStyles);
            
            if ("SUCCESS".equals(syncRecord.getStatus())) {
                return Result.success(syncRecord, "样式同步成功");
            } else {
                return Result.error("样式同步失败: " + syncRecord.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("同步模板 {} 样式时发生错误", templateId, e);
            return Result.error("样式同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板最新样式
     */
    @GetMapping("/latest/{templateId}")
    // @ApiOperation("获取指定模板的最新样式")
    public Result<ExtractedStyles> getLatestStyles(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId) {
        
        try {
            ExtractedStyles styles = styleSyncService.getLatestStyles(templateId);
            return Result.success(styles, "获取样式成功");
            
        } catch (Exception e) {
            log.error("获取模板 {} 最新样式时发生错误", templateId, e);
            return Result.error("获取样式失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查样式是否需要更新
     */
    @GetMapping("/check-update/{templateId}")
    // @ApiOperation("检查指定模板样式是否需要更新")
    public Result<Boolean> checkStyleUpdate(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId,
            /* @ApiParam("当前版本") */ @RequestParam(required = false) String currentVersion) {
        
        try {
            boolean needsUpdate = styleSyncService.needsStyleUpdate(templateId, currentVersion);
            return Result.success(needsUpdate, "检查完成");
            
        } catch (Exception e) {
            log.error("检查模板 {} 样式更新时发生错误", templateId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取同步历史记录
     */
    @GetMapping("/history/{templateId}")
    // @ApiOperation("获取指定模板的同步历史记录")
    public Result<List<StyleSyncRecord>> getSyncHistory(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId,
            /* @ApiParam("记录数量限制") */ @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<StyleSyncRecord> history = styleSyncService.getSyncHistory(templateId, limit);
            return Result.success(history, "获取历史记录成功");
            
        } catch (Exception e) {
            log.error("获取模板 {} 同步历史时发生错误", templateId, e);
            return Result.error("获取历史记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制全量同步
     */
    @PostMapping("/force-sync/{templateId}")
    // @ApiOperation("强制全量同步指定模板的样式")
    public Result<StyleSyncRecord> forceFullSync(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId) {
        
        try {
            log.info("接收到模板 {} 的强制全量同步请求", templateId);
            
            StyleSyncRecord syncRecord = styleSyncService.forceFullSync(templateId);
            
            if ("SUCCESS".equals(syncRecord.getStatus())) {
                return Result.success(syncRecord, "强制同步成功");
            } else {
                return Result.error("强制同步失败: " + syncRecord.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("强制同步模板 {} 样式时发生错误", templateId, e);
            return Result.error("强制同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量同步所有模板
     */
    @PostMapping("/batch-sync")
    // @ApiOperation("批量同步所有模板的样式")
    public Result<StyleSyncService.SyncResult> batchSyncAllTemplates() {
        
        try {
            log.info("接收到批量同步所有模板的请求");
            
            StyleSyncService.SyncResult result = styleSyncService.batchSyncAllTemplates();
            
            if (result.getFailureCount() == 0) {
                return Result.success(result, "批量同步成功");
            } else {
                return Result.success(result, String.format("批量同步完成，成功: %d, 失败: %d", 
                                                           result.getSuccessCount(), result.getFailureCount()));
            }
            
        } catch (Exception e) {
            log.error("批量同步所有模板时发生错误", e);
            return Result.error("批量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证样式一致性
     */
    @GetMapping("/validate/{templateId}")
    // @ApiOperation("验证指定模板的样式一致性")
    public Result<StyleSyncService.StyleConsistencyResult> validateStyleConsistency(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId) {
        
        try {
            StyleSyncService.StyleConsistencyResult result = styleSyncService.validateStyleConsistency(templateId);
            return Result.success(result, "一致性验证完成");
            
        } catch (Exception e) {
            log.error("验证模板 {} 样式一致性时发生错误", templateId, e);
            return Result.error("一致性验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动自动更新服务
     */
    @PostMapping("/auto-update/start")
    // @ApiOperation("启动自动样式更新服务")
    public Result<Void> startAutoUpdate() {
        
        try {
            autoStyleUpdateService.startAutoUpdate();
            return Result.success(null, "自动更新服务已启动");
            
        } catch (Exception e) {
            log.error("启动自动更新服务时发生错误", e);
            return Result.error("启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止自动更新服务
     */
    @PostMapping("/auto-update/stop")
    // @ApiOperation("停止自动样式更新服务")
    public Result<Void> stopAutoUpdate() {
        
        try {
            autoStyleUpdateService.stopAutoUpdate();
            return Result.success(null, "自动更新服务已停止");
            
        } catch (Exception e) {
            log.error("停止自动更新服务时发生错误", e);
            return Result.error("停止失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取自动更新服务状态
     */
    @GetMapping("/auto-update/status")
    // @ApiOperation("获取自动样式更新服务状态")
    public Result<Boolean> getAutoUpdateStatus() {
        
        try {
            boolean running = autoStyleUpdateService.isRunning();
            return Result.success(running, "获取状态成功");
            
        } catch (Exception e) {
            log.error("获取自动更新服务状态时发生错误", e);
            return Result.error("获取状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发模板检查更新
     */
    @PostMapping("/check-update/{templateId}")
    // @ApiOperation("手动触发指定模板的样式检查更新")
    public Result<Void> triggerTemplateUpdate(
            /* @ApiParam("模板ID") */ @PathVariable Integer templateId) {
        
        try {
            autoStyleUpdateService.checkAndUpdateTemplate(templateId);
            return Result.success(null, "检查更新已触发");
            
        } catch (Exception e) {
            log.error("触发模板 {} 检查更新时发生错误", templateId, e);
            return Result.error("触发失败: " + e.getMessage());
        }
    }
}