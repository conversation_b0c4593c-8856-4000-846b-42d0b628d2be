# Design Document

## Overview

本设计文档旨在解决简历模板1生成PDF时基本信息丢失的问题。通过分析现有代码，发现问题的根本原因在于模板1的检测逻辑存在缺陷，导致系统无法正确识别模板1，从而使用了错误的数据映射逻辑。

## Architecture

### 问题分析

1. **模板检测问题**: 当前的模板1检测逻辑通过检查HTML内容中是否包含特定的CSS渐变样式来判断是否为模板1，但这个检测可能不够可靠。

2. **数据映射路径**: 
   - 正确路径：模板1 → `fillTemplate1()` → 专用基本信息映射
   - 错误路径：模板1 → `dataMapper.mapAllModules()` → 通用数据映射

3. **占位符替换**: 模板1的基本信息直接在HTML header中通过占位符显示，如果数据映射不正确，这些占位符就不会被替换。

### 解决方案架构

```
PDF生成请求
    ↓
模板ID识别 (templateId = 1)
    ↓
模板HTML读取 (template1.html)
    ↓
模板类型检测 (改进的检测逻辑)
    ↓
数据映射选择
    ├─ 模板1 → 专用映射逻辑
    └─ 其他模板 → 通用映射逻辑
    ↓
占位符替换
    ↓
PDF生成
```

## Components and Interfaces

### 1. 模板检测组件 (TemplateDetector)

**职责**: 准确识别模板类型

**接口**:
```java
public class TemplateDetector {
    public static boolean isTemplate1(String templateHtml, Long templateId);
    public static TemplateType detectTemplateType(String templateHtml, Long templateId);
}
```

**改进点**:
- 结合templateId和HTML内容进行双重检测
- 增加多个特征点检测，提高准确性
- 添加详细的检测日志

### 2. 模板1数据映射器 (Template1DataMapper)

**职责**: 专门处理模板1的数据映射

**接口**:
```java
public class Template1DataMapper {
    public static Map<String, String> mapTemplate1Data(ResumeFullSaveDto resume, String resumeId);
    private static void mapBasicInfoForTemplate1(ResumeInformationVo info, Map<String, String> replacements);
    private static void validateTemplate1Data(Map<String, String> replacements, String resumeId);
}
```

**特点**:
- 专门针对模板1的基本信息字段映射
- 包含详细的验证和日志记录
- 处理空值和默认值逻辑

### 3. 占位符验证器 (PlaceholderValidator)

**职责**: 验证占位符替换的完整性

**接口**:
```java
public class PlaceholderValidator {
    public static ValidationResult validatePlaceholders(String html, String resumeId);
    public static List<String> findUnreplacedPlaceholders(String html);
    public static void logPlaceholderStatus(String html, String resumeId);
}
```

### 4. 增强的HtmlTemplateUtil

**改进点**:
- 更可靠的模板1检测逻辑
- 独立的模板1数据映射流程
- 详细的错误处理和日志记录
- 占位符替换验证

## Data Models

### 模板检测结果
```java
public class TemplateDetectionResult {
    private boolean isTemplate1;
    private String detectionMethod;
    private List<String> detectionFeatures;
    private double confidence;
}
```

### 基本信息映射结果
```java
public class BasicInfoMappingResult {
    private Map<String, String> mappedFields;
    private List<String> missingFields;
    private List<String> emptyFields;
    private boolean isComplete;
}
```

### 占位符验证结果
```java
public class PlaceholderValidationResult {
    private List<String> unreplacedPlaceholders;
    private int totalPlaceholders;
    private int replacedPlaceholders;
    private boolean isComplete;
}
```

## Error Handling

### 1. 模板检测错误
- **错误类型**: TEMPLATE_DETECTION_FAILED
- **处理策略**: 记录详细日志，使用templateId作为备用检测方法
- **恢复机制**: 如果检测失败，根据templateId强制使用对应的映射逻辑

### 2. 基本信息映射错误
- **错误类型**: BASIC_INFO_MAPPING_FAILED
- **处理策略**: 使用默认值填充，记录缺失字段
- **恢复机制**: 确保至少有姓名字段，其他字段可以为空

### 3. 占位符替换错误
- **错误类型**: PLACEHOLDER_REPLACEMENT_FAILED
- **处理策略**: 记录未替换的占位符，继续生成PDF
- **恢复机制**: 对于关键占位符（如姓名），使用默认值替换

## Testing Strategy

### 1. 单元测试

**模板检测测试**:
```java
@Test
public void testTemplate1Detection() {
    // 测试正确的模板1检测
    // 测试错误的模板检测
    // 测试边界情况
}
```

**数据映射测试**:
```java
@Test
public void testTemplate1DataMapping() {
    // 测试完整基本信息映射
    // 测试部分信息缺失的情况
    // 测试空值处理
    // 测试特殊字符处理
}
```

**占位符替换测试**:
```java
@Test
public void testPlaceholderReplacement() {
    // 测试所有占位符正确替换
    // 测试部分占位符未替换的情况
    // 测试占位符验证逻辑
}
```

### 2. 集成测试

**端到端PDF生成测试**:
```java
@Test
public void testTemplate1PDFGeneration() {
    // 测试完整的模板1 PDF生成流程
    // 验证生成的PDF包含正确的基本信息
    // 测试不同数据组合的PDF生成
}
```

### 3. 性能测试

**内存使用测试**:
- 验证修复后的代码不会增加显著的内存开销
- 测试大量并发PDF生成的性能

**响应时间测试**:
- 确保修复不会显著影响PDF生成速度
- 测试各种数据量下的响应时间

### 4. 回归测试

**其他模板测试**:
- 确保修复不会影响模板2、模板3的正常功能
- 验证通用数据映射逻辑仍然正常工作

## Implementation Plan

### Phase 1: 核心修复
1. 修复模板1检测逻辑
2. 完善模板1专用数据映射
3. 增强占位符验证

### Phase 2: 增强功能
1. 添加详细的调试日志
2. 实现错误恢复机制
3. 优化性能

### Phase 3: 测试和验证
1. 编写全面的单元测试
2. 进行集成测试
3. 性能测试和优化

## Monitoring and Logging

### 关键日志点
1. 模板检测结果
2. 基本信息映射过程
3. 占位符替换状态
4. 错误和异常情况

### 监控指标
1. 模板1 PDF生成成功率
2. 基本信息完整性比例
3. 占位符替换成功率
4. 错误类型分布

## Security Considerations

1. **输入验证**: 确保基本信息字段经过适当的HTML转义
2. **XSS防护**: 防止恶意内容注入到PDF中
3. **数据隐私**: 确保敏感信息在日志中被适当脱敏