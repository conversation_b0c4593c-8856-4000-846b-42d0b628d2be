package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.AdvancedPDFGenerator;
import com.bimowu.resume.common.service.EnhancedTemplateGenerator;
import com.bimowu.resume.common.service.FontManager;
import com.bimowu.resume.common.service.StyleSyncService;
import com.bimowu.resume.config.PDFGenerationConfig;
import com.bimowu.resume.dto.ExtractedStyles;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.BaseFont;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Flying Saucer PDF生成器实现
 */
@Slf4j
@Service
public class FlyingSaucerPDFGeneratorImpl implements AdvancedPDFGenerator {
    
    @Autowired
    private EnhancedTemplateGenerator templateGenerator;
    
    @Autowired
    private StyleSyncService styleSyncService;
    
    @Autowired
    private FontManager fontManagerService;
    
    @Autowired
    private PDFGenerationConfig pdfConfig;

    
    @Override
    public boolean generatePDFFromHTML(String htmlContent, OutputStream outputStream) {
        try {
            log.info("开始生成PDF，HTML内容长度: {}", htmlContent.length());
            log.debug("HTML内容预览: {}", htmlContent.substring(0, Math.min(200, htmlContent.length())));
            
            ITextRenderer renderer = new ITextRenderer();
            
            // 配置字体
            ITextFontResolver fontResolver = renderer.getFontResolver();
            configureFonts(fontResolver);
            
            // 设置HTML内容
            renderer.setDocumentFromString(htmlContent);
            renderer.layout();
            renderer.createPDF(outputStream);
            
            log.info("PDF生成成功");
            return true;
        } catch (Exception e) {
            log.error("PDF生成失败", e);
            return false;
        }
    }
    
    @Override
    public boolean generatePDFFromHTMLAndCSS(String htmlContent, String cssContent, OutputStream outputStream) {
        try {
            String finalHtml = embedCSSIntoHTML(htmlContent, cssContent);
            return generatePDFFromHTML(finalHtml, outputStream);
        } catch (Exception e) {
            log.error("从HTML和CSS生成PDF失败", e);
            return false;
        }
    }
    
    @Override
    public ValidationResult validateHTML(String htmlContent) {
        ValidationResult result = new ValidationResult(true);
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        try {
            if (!StringUtils.hasText(htmlContent)) {
                errors.add("HTML内容为空");
                result.setValid(false);
            } else {
                // 基本的HTML验证
                if (!htmlContent.contains("<html")) {
                    warnings.add("缺少<html>标签");
                }
                
                if (!htmlContent.contains("<body")) {
                    warnings.add("缺少<body>标签");
                }
                
                // 检查是否包含Flying Saucer不支持的元素
                if (htmlContent.contains("<script")) {
                    warnings.add("包含<script>标签，可能不会被渲染");
                }
                
                if (htmlContent.contains("javascript:")) {
                    warnings.add("包含JavaScript代码，不会被执行");
                }
                
                // 尝试创建渲染器进行验证
                try {
                    ITextRenderer renderer = new ITextRenderer();
                    renderer.setDocumentFromString(htmlContent);
                    renderer.layout();
                } catch (Exception e) {
                    errors.add("HTML渲染验证失败: " + e.getMessage());
                    result.setValid(false);
                }
            }
            
        } catch (Exception e) {
            errors.add("验证过程出错: " + e.getMessage());
            result.setValid(false);
        }
        
        result.setErrors(errors.toArray(new String[0]));
        result.setWarnings(warnings.toArray(new String[0]));
        
        return result;
    }
    
    @Override
    public GeneratorInfo getGeneratorInfo() {
        GeneratorInfo info = new GeneratorInfo();
        info.setName("Flying Saucer PDF Generator");
        info.setVersion("1.0");
        info.setSupportedFeatures(new String[]{"HTML to PDF", "CSS support", "Font embedding"});
        return info;
    }
    
    /**
     * 配置字体
     */
    private void configureFonts(ITextFontResolver fontResolver) throws DocumentException, IOException {
        try {
            log.info("开始配置Flying Saucer字体");
            
            // 使用classpath加载字体文件
            ClassPathResource simsunResource = new ClassPathResource("fonts/simsun.ttf");
            ClassPathResource simheiResource = new ClassPathResource("fonts/simhei.ttf");
            
            // 加载SimSun字体
            if (simsunResource.exists()) {
                try {
                    // 创建临时文件来解决JAR包中字体文件访问问题
                    java.io.InputStream inputStream = simsunResource.getInputStream();
                    java.io.File tempFile = java.io.File.createTempFile("simsun", ".ttf");
                    tempFile.deleteOnExit();
                    
                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            fos.write(buffer, 0, bytesRead);
                        }
                    }
                    inputStream.close();
                    
                    // 添加字体，使用正确的字体名称
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimSun", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, null);
                    log.info("成功加载SimSun字体: {}", tempFile.getAbsolutePath());
                } catch (Exception e) {
                    log.error("加载SimSun字体失败", e);
                }
            } else {
                log.warn("SimSun.ttf字体文件不存在");
            }
            
            // 加载SimHei字体
            if (simheiResource.exists()) {
                try {
                    // 创建临时文件来解决JAR包中字体文件访问问题
                    java.io.InputStream inputStream = simheiResource.getInputStream();
                    java.io.File tempFile = java.io.File.createTempFile("simhei", ".ttf");
                    tempFile.deleteOnExit();
                    
                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            fos.write(buffer, 0, bytesRead);
                        }
                    }
                    inputStream.close();
                    
                    // 添加字体，使用正确的字体名称
                    fontResolver.addFont(tempFile.getAbsolutePath(), "SimHei", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, null);
                    log.info("成功加载SimHei字体: {}", tempFile.getAbsolutePath());
                } catch (Exception e) {
                    log.error("加载SimHei字体失败", e);
                }
            } else {
                log.warn("SimHei.ttf字体文件不存在");
            }
            
            log.info("字体配置完成");
            
        } catch (Exception e) {
            log.error("字体配置失败", e);
            // 不抛出异常，允许PDF生成继续进行
        }
    }
    
    /**
     * 配置字体（兼容旧版方法）
     */
    @Deprecated
    private void configureFont(ITextRenderer renderer) throws DocumentException, IOException {
        configureFonts(renderer.getFontResolver());
    }
    
    /**
     * 将CSS嵌入到HTML中
     */
    private String embedCSSIntoHTML(String htmlContent, String cssContent) {
        if (StringUtils.isEmpty(cssContent)) {
            return htmlContent;
        }
        
        // 创建<style>标签
        String styleTag = "<style>\n" + cssContent + "\n</style>";
        
        // 将<style>标签插入到<head>部分
        if (htmlContent.contains("</head>")) {
            return htmlContent.replace("</head>", styleTag + "</head>");
        } else {
            // 如果没有<head>标签，则创建一个
            return htmlContent.replace("<html", "<html><head>" + styleTag + "</head>");
        }
    }
}
