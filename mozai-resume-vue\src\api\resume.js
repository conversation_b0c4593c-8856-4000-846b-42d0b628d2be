import request from '../utils/request.js'

// 获取项目列表
export function getProjectListByCategory(catId) {
  return request({
    url: '/resume/project/listByCategory',
    method: 'get',
    params: { catId }
  })
}

// 获取技能描述
export function getSkillSegmentList(skillId, proficiency) {
  return request({
    url: '/skill-segment/list',
    method: 'get',
    params: {
      skillId,
      proficiency
    }
  })
}

// AI润色项目经验
export function polishProjectExperience(content) {
  return request({
    url: '/resume/polish/project',
    method: 'post',
    data: { content }
  })
}

// AI润色技能特长
export function polishSkill(content) {
  return request({
    url: '/resume/polish/skill',
    method: 'post',
    data: { content }
  })
}

// AI润色证书奖项
export function polishCertificate(content) {
  return request({
    url: '/resume/polish/certificate',
    method: 'post',
    data: { content }
  })
}

// AI润色校园经历
export function polishCampus(content) {
  return request({
    url: '/resume/polish/campus',
    method: 'post',
    data: { content }
  })
}

// AI润色兴趣爱好
export function polishInterest(content) {
  return request({
    url: '/resume/polish/interest',
    method: 'post',
    data: { content }
  })
}

// AI润色工作经验
export function polishWork(content) {
  return request({
    url: '/resume/polish/work',
    method: 'post',
    data: { content }
  })
}

// PDF导出相关接口

// 导出PDF简历
export function exportResumeToPdf(resumeId) {
  return request({
    url: `/resume/export/pdf/${resumeId}`,
    method: 'get',
    responseType: 'blob', // 重要：设置响应类型为blob
    headers: {
      'Accept': 'application/pdf'
    }
  })
}

// PDF预览
export function previewResumePdf(resumeId) {
  return request({
    url: `/resume/preview/pdf/${resumeId}`,
    method: 'get',
    responseType: 'blob',
    headers: {
      'Accept': 'application/pdf'
    }
  })
}

// 获取PDF系统状态
export function getPdfSystemStatus() {
  return request({
    url: '/pdf-system/status',
    method: 'get'
  })
}

// 切换PDF系统
export function switchPdfSystem(systemType) {
  const endpoint = systemType === 'new' ? '/pdf-system/switch-to-new' : '/pdf-system/switch-to-legacy'
  return request({
    url: endpoint,
    method: 'post'
  })
}

// 切换到新PDF系统
export function switchToNewPdfSystem() {
  return request({
    url: '/pdf-system/switch-to-new',
    method: 'post'
  })
}

// 切换到旧PDF系统
export function switchToLegacyPdfSystem() {
  return request({
    url: '/pdf-system/switch-to-legacy',
    method: 'post'
  })
}

// 获取PDF系统健康状态
export function getPdfSystemHealth() {
  return request({
    url: '/pdf-system/health',
    method: 'get'
  })
}

// Word导出相关接口

// 导出Word简历
export function exportResumeToWord(resumeId) {
  return request({
    url: `/resume/export/word/${resumeId}`,
    method: 'get',
    responseType: 'blob', // 重要：设置响应类型为blob
    headers: {
      'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
  })
}