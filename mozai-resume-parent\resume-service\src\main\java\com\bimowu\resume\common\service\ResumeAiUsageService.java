package com.bimowu.resume.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.resume.entity.ResumeAiUsage;

/**
 * 用户AI使用次数记录表 服务接口
 */
public interface ResumeAiUsageService extends IService<ResumeAiUsage> {
    
    /**
     * 检查用户是否有可用的AI次数
     * @param userId 用户ID
     * @return 是否有可用次数
     */
    boolean hasAvailableCount(Long userId);
    
    /**
     * 减少用户可用AI次数
     * @param userId 用户ID
     * @return 是否成功减少
     */
    boolean decreaseCount(Long userId);
    
    /**
     * 获取用户剩余可用AI次数
     * @param userId 用户ID
     * @return 剩余次数
     */
    int getRemainingCount(Long userId);
    
    /**
     * 初始化用户AI使用次数
     * @param userId 用户ID
     * @param initialCount 初始次数
     * @return 是否成功初始化
     */
    boolean initUserAiCount(Long userId, Integer initialCount);
} 