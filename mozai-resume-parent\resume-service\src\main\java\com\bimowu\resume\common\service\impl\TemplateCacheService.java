package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.config.PDFPerformanceConfig;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 模板缓存服务
 * 用于缓存HTML模板内容，提高PDF生成性能
 */
@Service
@Slf4j
public class TemplateCacheService {
    
    @Autowired
    private PDFPerformanceConfig performanceConfig;
    
    // 模板内容缓存
    private final ConcurrentHashMap<Long, CacheEntry> templateCache = new ConcurrentHashMap<>();
    
    // 定时清理器
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final String content;
        private final long timestamp;
        
        public CacheEntry(String content) {
            this.content = content;
            this.timestamp = System.currentTimeMillis();
        }
        
        public String getContent() {
            return content;
        }
        
        public boolean isExpired(long expireTimeMs) {
            return System.currentTimeMillis() - timestamp > expireTimeMs;
        }
    }
    
    /**
     * 初始化缓存服务
     */
    public void init() {
        if (performanceConfig.isEnableTemplateCache()) {
            // 启动定时清理任务
            cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredEntries, 
                5, 5, TimeUnit.MINUTES);
            log.info("模板缓存服务已启动，缓存过期时间: {} 分钟", 
                performanceConfig.getTemplateCacheExpireMinutes());
        }
    }
    
    /**
     * 获取模板内容
     * 
     * @param templateId 模板ID
     * @return 模板内容
     */
    public String getTemplate(Long templateId) {
        if (!performanceConfig.isEnableTemplateCache()) {
            // 缓存未启用，直接读取
            return HtmlTemplateUtil.readTemplate(templateId);
        }
        
        CacheEntry entry = templateCache.get(templateId);
        long expireTimeMs = performanceConfig.getTemplateCacheExpireMinutes() * 60 * 1000L;
        
        if (entry != null && !entry.isExpired(expireTimeMs)) {
            log.debug("从缓存获取模板 {} 成功", templateId);
            return entry.getContent();
        }
        
        // 缓存未命中或已过期，重新加载
        String content = loadAndCacheTemplate(templateId);
        return content;
    }
    
    /**
     * 加载并缓存模板
     * 
     * @param templateId 模板ID
     * @return 模板内容
     */
    private synchronized String loadAndCacheTemplate(Long templateId) {
        // 双重检查，避免重复加载
        CacheEntry entry = templateCache.get(templateId);
        long expireTimeMs = performanceConfig.getTemplateCacheExpireMinutes() * 60 * 1000L;
        
        if (entry != null && !entry.isExpired(expireTimeMs)) {
            return entry.getContent();
        }
        
        try {
            log.debug("加载模板 {} 到缓存", templateId);
            String content = HtmlTemplateUtil.readTemplate(templateId);
            templateCache.put(templateId, new CacheEntry(content));
            log.info("模板 {} 已缓存，内容长度: {}", templateId, content.length());
            return content;
        } catch (Exception e) {
            log.error("加载模板 {} 失败", templateId, e);
            throw e;
        }
    }
    
    /**
     * 清理过期的缓存条目
     */
    private void cleanupExpiredEntries() {
        if (!performanceConfig.isEnableTemplateCache()) {
            return;
        }
        
        long expireTimeMs = performanceConfig.getTemplateCacheExpireMinutes() * 60 * 1000L;
        int removedCount = 0;
        
        for (Long templateId : templateCache.keySet()) {
            CacheEntry entry = templateCache.get(templateId);
            if (entry != null && entry.isExpired(expireTimeMs)) {
                templateCache.remove(templateId);
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            log.info("清理过期模板缓存，移除 {} 个条目，当前缓存大小: {}", 
                removedCount, templateCache.size());
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clearCache() {
        templateCache.clear();
        log.info("模板缓存已清空");
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("模板缓存统计: 缓存条目数=%d, 启用状态=%s, 过期时间=%d分钟", 
            templateCache.size(), 
            performanceConfig.isEnableTemplateCache(),
            performanceConfig.getTemplateCacheExpireMinutes());
    }
    
    /**
     * 预热缓存
     * 预加载常用模板
     */
    public void warmupCache() {
        if (!performanceConfig.isEnableTemplateCache()) {
            return;
        }
        
        log.info("开始预热模板缓存");
        
        // 预加载模板1-10
        for (long i = 1; i <= 10; i++) {
            try {
                getTemplate(i);
                log.debug("预热模板 {} 完成", i);
            } catch (Exception e) {
                log.warn("预热模板 {} 失败: {}", i, e.getMessage());
            }
        }
        
        log.info("模板缓存预热完成，缓存条目数: {}", templateCache.size());
    }
    
    /**
     * 销毁服务
     */
    public void destroy() {
        cleanupExecutor.shutdown();
        templateCache.clear();
        log.info("模板缓存服务已关闭");
    }
}