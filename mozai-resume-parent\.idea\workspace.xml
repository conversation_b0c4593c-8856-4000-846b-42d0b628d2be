<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f640cd95-f3d7-478c-9a49-3c852073e38d" name="Changes" comment="augment优化代码----模板1优化-表头">
      <change beforePath="$PROJECT_DIR$/resume-common/src/main/java/com/bimowu/resume/utils/HtmlTemplateUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/resume-common/src/main/java/com/bimowu/resume/utils/HtmlTemplateUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/utils/ResumeExportUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/utils/ResumeExportUtil.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Pull.Settings">
    <option name="OPTIONS">
      <set>
        <option value="REBASE" />
      </set>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yUWtPN05p2ft7aTVleY7MuKMwy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.UserController.executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-api [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-resume-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ResumeWebApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/sso/resume/mozai-resume-parent/resume-common/src/main/resources/fonts&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDKs&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\fonts" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\templates\pdf" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-web\src\main\resources\mappers" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-service\src\main\java\com\bimowu\resume\common\service\impl" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-service\src\main\java\com\bimowu\resume\common\service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-common\src\main\resources\templates\pdf" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-web\src\main\resources\mappers" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bimowu.resume.common.dao" />
      <recent name="com.bimowu.resume.entity" />
      <recent name="com.bimowu.resume.config" />
      <recent name="com.bimowu.resume.utils" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.ResumeWebApplication">
    <configuration name="UserController" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bimowu.resume.controller.UserController" />
      <module name="resume-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bimowu.resume.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="mozai-resume-parent" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="mozai-resume-parent" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="InterviewWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="resume-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.InterviewWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ResumeWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="resume-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.ResumeWebApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bimowu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ResumeWebApplication" />
        <item itemvalue="Application.UserController" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f640cd95-f3d7-478c-9a49-3c852073e38d" name="Changes" comment="" />
      <created>1749889845876</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749889845876</updated>
      <workItem from="1749889847011" duration="2447000" />
      <workItem from="1750386817641" duration="3740000" />
      <workItem from="1750406478657" duration="3556000" />
      <workItem from="1750410497186" duration="3159000" />
      <workItem from="1750417603583" duration="18874000" />
      <workItem from="1750493193628" duration="188000" />
      <workItem from="1750493453385" duration="3342000" />
      <workItem from="1750643894031" duration="23722000" />
      <workItem from="1750728268645" duration="512000" />
      <workItem from="1750728994862" duration="2701000" />
      <workItem from="1750843860906" duration="6104000" />
      <workItem from="1750891153819" duration="2055000" />
      <workItem from="1751334793602" duration="8536000" />
      <workItem from="1751421947258" duration="8476000" />
      <workItem from="1751508625160" duration="5604000" />
      <workItem from="1751527525630" duration="2326000" />
      <workItem from="1751594198190" duration="598000" />
      <workItem from="1751612061908" duration="500000" />
      <workItem from="1751957941627" duration="44000" />
      <workItem from="1751958096039" duration="7621000" />
      <workItem from="1752026910192" duration="1672000" />
      <workItem from="1752042875032" duration="2134000" />
      <workItem from="1752281967488" duration="12628000" />
      <workItem from="1752457585229" duration="10441000" />
      <workItem from="1752477568948" duration="223000" />
      <workItem from="1752477928342" duration="16716000" />
      <workItem from="1752561871479" duration="14511000" />
      <workItem from="1752810827417" duration="4941000" />
      <workItem from="1752978727505" duration="4117000" />
      <workItem from="1753152028887" duration="2249000" />
      <workItem from="1753328907606" duration="18779000" />
      <workItem from="1753684132138" duration="13433000" />
      <workItem from="1753701491751" duration="70000" />
      <workItem from="1753701585126" duration="553000" />
      <workItem from="1753755340849" duration="18000" />
      <workItem from="1753768033581" duration="30247000" />
      <workItem from="1753869343694" duration="48290000" />
      <workItem from="1754273698434" duration="15343000" />
    </task>
    <task id="LOCAL-00122" summary="augment优化代码">
      <option name="closed" value="true" />
      <created>1753952128752</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1753952128753</updated>
    </task>
    <task id="LOCAL-00123" summary="augment优化代码">
      <option name="closed" value="true" />
      <created>1753952178896</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1753952178896</updated>
    </task>
    <task id="LOCAL-00124" summary="augment优化代码">
      <option name="closed" value="true" />
      <created>1753952460648</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1753952460648</updated>
    </task>
    <task id="LOCAL-00125" summary="augment优化代码----模板3优化">
      <option name="closed" value="true" />
      <created>1753952833575</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1753952833575</updated>
    </task>
    <task id="LOCAL-00126" summary="augment优化代码----模板3优化">
      <option name="closed" value="true" />
      <created>1753953054745</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1753953054745</updated>
    </task>
    <task id="LOCAL-00127" summary="augment优化代码----模板3优化">
      <option name="closed" value="true" />
      <created>1753955424491</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1753955424491</updated>
    </task>
    <task id="LOCAL-00128" summary="augment优化代码----模板3优化">
      <option name="closed" value="true" />
      <created>1753955779127</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1753955779127</updated>
    </task>
    <task id="LOCAL-00129" summary="augment优化代码----模板3优化--添加日志">
      <option name="closed" value="true" />
      <created>1753956843681</created>
      <option name="number" value="00129" />
      <option name="presentableId" value="LOCAL-00129" />
      <option name="project" value="LOCAL" />
      <updated>1753956843681</updated>
    </task>
    <task id="LOCAL-00130" summary="augment优化代码----模板3优化--粗体处理">
      <option name="closed" value="true" />
      <created>1753958212471</created>
      <option name="number" value="00130" />
      <option name="presentableId" value="LOCAL-00130" />
      <option name="project" value="LOCAL" />
      <updated>1753958212471</updated>
    </task>
    <task id="LOCAL-00131" summary="augment优化代码----模板3优化--粗体处理">
      <option name="closed" value="true" />
      <created>1754015484190</created>
      <option name="number" value="00131" />
      <option name="presentableId" value="LOCAL-00131" />
      <option name="project" value="LOCAL" />
      <updated>1754015484190</updated>
    </task>
    <task id="LOCAL-00132" summary="augment优化代码----模板3优化--粗体处理">
      <option name="closed" value="true" />
      <created>1754016778743</created>
      <option name="number" value="00132" />
      <option name="presentableId" value="LOCAL-00132" />
      <option name="project" value="LOCAL" />
      <updated>1754016778743</updated>
    </task>
    <task id="LOCAL-00133" summary="augment优化代码----模板3优化--粗体处理">
      <option name="closed" value="true" />
      <created>1754017686208</created>
      <option name="number" value="00133" />
      <option name="presentableId" value="LOCAL-00133" />
      <option name="project" value="LOCAL" />
      <updated>1754017686208</updated>
    </task>
    <task id="LOCAL-00134" summary="augment优化代码----模板3优化--粗体处理">
      <option name="closed" value="true" />
      <created>1754018182621</created>
      <option name="number" value="00134" />
      <option name="presentableId" value="LOCAL-00134" />
      <option name="project" value="LOCAL" />
      <updated>1754018182621</updated>
    </task>
    <task id="LOCAL-00135" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754018962106</created>
      <option name="number" value="00135" />
      <option name="presentableId" value="LOCAL-00135" />
      <option name="project" value="LOCAL" />
      <updated>1754018962106</updated>
    </task>
    <task id="LOCAL-00136" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754019288209</created>
      <option name="number" value="00136" />
      <option name="presentableId" value="LOCAL-00136" />
      <option name="project" value="LOCAL" />
      <updated>1754019288209</updated>
    </task>
    <task id="LOCAL-00137" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754019992692</created>
      <option name="number" value="00137" />
      <option name="presentableId" value="LOCAL-00137" />
      <option name="project" value="LOCAL" />
      <updated>1754019992692</updated>
    </task>
    <task id="LOCAL-00138" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754020460071</created>
      <option name="number" value="00138" />
      <option name="presentableId" value="LOCAL-00138" />
      <option name="project" value="LOCAL" />
      <updated>1754020460071</updated>
    </task>
    <task id="LOCAL-00139" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754020520123</created>
      <option name="number" value="00139" />
      <option name="presentableId" value="LOCAL-00139" />
      <option name="project" value="LOCAL" />
      <updated>1754020520123</updated>
    </task>
    <task id="LOCAL-00140" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754020725145</created>
      <option name="number" value="00140" />
      <option name="presentableId" value="LOCAL-00140" />
      <option name="project" value="LOCAL" />
      <updated>1754020725145</updated>
    </task>
    <task id="LOCAL-00141" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754020977766</created>
      <option name="number" value="00141" />
      <option name="presentableId" value="LOCAL-00141" />
      <option name="project" value="LOCAL" />
      <updated>1754020977766</updated>
    </task>
    <task id="LOCAL-00142" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754021397419</created>
      <option name="number" value="00142" />
      <option name="presentableId" value="LOCAL-00142" />
      <option name="project" value="LOCAL" />
      <updated>1754021397419</updated>
    </task>
    <task id="LOCAL-00143" summary="augment优化代码----模板3优化--布局优化">
      <option name="closed" value="true" />
      <created>1754021537199</created>
      <option name="number" value="00143" />
      <option name="presentableId" value="LOCAL-00143" />
      <option name="project" value="LOCAL" />
      <updated>1754021537200</updated>
    </task>
    <task id="LOCAL-00144" summary="augment优化代码----模板3优化--布局优化--bug">
      <option name="closed" value="true" />
      <created>1754022110097</created>
      <option name="number" value="00144" />
      <option name="presentableId" value="LOCAL-00144" />
      <option name="project" value="LOCAL" />
      <updated>1754022110097</updated>
    </task>
    <task id="LOCAL-00145" summary="augment优化代码----模板3优化--布局优化--bug">
      <option name="closed" value="true" />
      <created>1754022533787</created>
      <option name="number" value="00145" />
      <option name="presentableId" value="LOCAL-00145" />
      <option name="project" value="LOCAL" />
      <updated>1754022533787</updated>
    </task>
    <task id="LOCAL-00146" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754022837312</created>
      <option name="number" value="00146" />
      <option name="presentableId" value="LOCAL-00146" />
      <option name="project" value="LOCAL" />
      <updated>1754022837312</updated>
    </task>
    <task id="LOCAL-00147" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754023236889</created>
      <option name="number" value="00147" />
      <option name="presentableId" value="LOCAL-00147" />
      <option name="project" value="LOCAL" />
      <updated>1754023236889</updated>
    </task>
    <task id="LOCAL-00148" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754023656065</created>
      <option name="number" value="00148" />
      <option name="presentableId" value="LOCAL-00148" />
      <option name="project" value="LOCAL" />
      <updated>1754023656065</updated>
    </task>
    <task id="LOCAL-00149" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754023976140</created>
      <option name="number" value="00149" />
      <option name="presentableId" value="LOCAL-00149" />
      <option name="project" value="LOCAL" />
      <updated>1754023976140</updated>
    </task>
    <task id="LOCAL-00150" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754024433273</created>
      <option name="number" value="00150" />
      <option name="presentableId" value="LOCAL-00150" />
      <option name="project" value="LOCAL" />
      <updated>1754024433273</updated>
    </task>
    <task id="LOCAL-00151" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754024758849</created>
      <option name="number" value="00151" />
      <option name="presentableId" value="LOCAL-00151" />
      <option name="project" value="LOCAL" />
      <updated>1754024758849</updated>
    </task>
    <task id="LOCAL-00152" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754025064074</created>
      <option name="number" value="00152" />
      <option name="presentableId" value="LOCAL-00152" />
      <option name="project" value="LOCAL" />
      <updated>1754025064074</updated>
    </task>
    <task id="LOCAL-00153" summary="augment优化代码----模板3优化--布局优化--教育经历">
      <option name="closed" value="true" />
      <created>1754026935268</created>
      <option name="number" value="00153" />
      <option name="presentableId" value="LOCAL-00153" />
      <option name="project" value="LOCAL" />
      <updated>1754026935268</updated>
    </task>
    <task id="LOCAL-00154" summary="augment优化代码----模板3优化--布局优化--练手项目">
      <option name="closed" value="true" />
      <created>1754027367681</created>
      <option name="number" value="00154" />
      <option name="presentableId" value="LOCAL-00154" />
      <option name="project" value="LOCAL" />
      <updated>1754027367681</updated>
    </task>
    <task id="LOCAL-00155" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754027565046</created>
      <option name="number" value="00155" />
      <option name="presentableId" value="LOCAL-00155" />
      <option name="project" value="LOCAL" />
      <updated>1754027565046</updated>
    </task>
    <task id="LOCAL-00156" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754027868941</created>
      <option name="number" value="00156" />
      <option name="presentableId" value="LOCAL-00156" />
      <option name="project" value="LOCAL" />
      <updated>1754027868941</updated>
    </task>
    <task id="LOCAL-00157" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754028103509</created>
      <option name="number" value="00157" />
      <option name="presentableId" value="LOCAL-00157" />
      <option name="project" value="LOCAL" />
      <updated>1754028103509</updated>
    </task>
    <task id="LOCAL-00158" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754273759841</created>
      <option name="number" value="00158" />
      <option name="presentableId" value="LOCAL-00158" />
      <option name="project" value="LOCAL" />
      <updated>1754273759841</updated>
    </task>
    <task id="LOCAL-00159" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754274385213</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1754274385213</updated>
    </task>
    <task id="LOCAL-00160" summary="augment优化代码----模板3优化--字体颜色修改，">
      <option name="closed" value="true" />
      <created>1754274978349</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1754274978349</updated>
    </task>
    <task id="LOCAL-00161" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754275382478</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1754275382478</updated>
    </task>
    <task id="LOCAL-00162" summary="augment优化代码----模板3优化--布局优化--头像位置修改">
      <option name="closed" value="true" />
      <created>1754275738257</created>
      <option name="number" value="00162" />
      <option name="presentableId" value="LOCAL-00162" />
      <option name="project" value="LOCAL" />
      <updated>1754275738257</updated>
    </task>
    <task id="LOCAL-00163" summary="日志">
      <option name="closed" value="true" />
      <created>1754286759051</created>
      <option name="number" value="00163" />
      <option name="presentableId" value="LOCAL-00163" />
      <option name="project" value="LOCAL" />
      <updated>1754286759051</updated>
    </task>
    <task id="LOCAL-00164" summary="templateId类型">
      <option name="closed" value="true" />
      <created>1754287070373</created>
      <option name="number" value="00164" />
      <option name="presentableId" value="LOCAL-00164" />
      <option name="project" value="LOCAL" />
      <updated>1754287070373</updated>
    </task>
    <task id="LOCAL-00165" summary="templateId类型">
      <option name="closed" value="true" />
      <created>1754287680580</created>
      <option name="number" value="00165" />
      <option name="presentableId" value="LOCAL-00165" />
      <option name="project" value="LOCAL" />
      <updated>1754287680580</updated>
    </task>
    <task id="LOCAL-00166" summary="augment优化代码----模板1优化">
      <option name="closed" value="true" />
      <created>1754291178938</created>
      <option name="number" value="00166" />
      <option name="presentableId" value="LOCAL-00166" />
      <option name="project" value="LOCAL" />
      <updated>1754291178938</updated>
    </task>
    <task id="LOCAL-00167" summary="augment优化代码----模板1优化">
      <option name="closed" value="true" />
      <created>1754293875914</created>
      <option name="number" value="00167" />
      <option name="presentableId" value="LOCAL-00167" />
      <option name="project" value="LOCAL" />
      <updated>1754293875914</updated>
    </task>
    <task id="LOCAL-00168" summary="augment优化代码----模板1优化">
      <option name="closed" value="true" />
      <created>1754298162763</created>
      <option name="number" value="00168" />
      <option name="presentableId" value="LOCAL-00168" />
      <option name="project" value="LOCAL" />
      <updated>1754298162763</updated>
    </task>
    <task id="LOCAL-00169" summary="augment优化代码----模板1优化-表头">
      <option name="closed" value="true" />
      <created>1754299189164</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1754299189166</updated>
    </task>
    <task id="LOCAL-00170" summary="augment优化代码----模板1优化-表头">
      <option name="closed" value="true" />
      <created>1754302381803</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1754302381803</updated>
    </task>
    <option name="localTasksCounter" value="171" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="508882c3-3b90-4a81-8d93-783580712658" value="TOOL_WINDOW" />
        <entry key="48117be4-ca99-4ef7-a8f1-bd7a3dd29436" value="TOOL_WINDOW" />
        <entry key="22a432c3-65a0-4c7d-a0ca-a5d2f5f122d5" value="TOOL_WINDOW" />
        <entry key="af16615c-559d-41ae-ae49-62cac2e25fc7" value="TOOL_WINDOW" />
        <entry key="2bb5e6c4-4443-40d7-9445-4b3f1eb8caaa" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="22a432c3-65a0-4c7d-a0ca-a5d2f5f122d5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="2bb5e6c4-4443-40d7-9445-4b3f1eb8caaa">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="48117be4-ca99-4ef7-a8f1-bd7a3dd29436">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="508882c3-3b90-4a81-8d93-783580712658">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="af16615c-559d-41ae-ae49-62cac2e25fc7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="iText+Adobe Acrobat Pro---完善模板" />
    <MESSAGE value="回滚到flying方案" />
    <MESSAGE value="字体" />
    <MESSAGE value="字体加载" />
    <MESSAGE value="优化" />
    <MESSAGE value="字体修改" />
    <MESSAGE value="字体别名" />
    <MESSAGE value="修改设置" />
    <MESSAGE value="字体路径设置为linux绝对路径" />
    <MESSAGE value="augment优化" />
    <MESSAGE value="模板优化" />
    <MESSAGE value="augment优化代码" />
    <MESSAGE value="augment优化代码----模板3优化" />
    <MESSAGE value="augment优化代码----模板3优化--添加日志" />
    <MESSAGE value="augment优化代码----模板3优化--粗体处理" />
    <MESSAGE value="augment优化代码----模板3优化--布局优化" />
    <MESSAGE value="augment优化代码----模板3优化--布局优化--bug" />
    <MESSAGE value="augment优化代码----模板3优化--布局优化--教育经历" />
    <MESSAGE value="augment优化代码----模板3优化--布局优化--练手项目" />
    <MESSAGE value="augment优化代码----模板3优化--字体颜色修改，" />
    <MESSAGE value="augment优化代码----模板3优化--布局优化--头像位置修改" />
    <MESSAGE value="日志" />
    <MESSAGE value="templateId类型" />
    <MESSAGE value="augment优化代码----模板1优化" />
    <MESSAGE value="augment优化代码----模板1优化-表头" />
    <option name="LAST_COMMIT_MESSAGE" value="augment优化代码----模板1优化-表头" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-web/src/main/java/com/bimowu/resume/controller/UserController.java</url>
          <line>237</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/impl/FlyingSaucerPDFGeneratorImpl.java</url>
          <line>134</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/impl/FlyingSaucerPDFGeneratorImpl.java</url>
          <line>74</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/resume-service/src/main/java/com/bimowu/resume/common/service/StyleSyncService.java</url>
          <line>19</line>
          <properties class="com.bimowu.resume.common.service.StyleSyncService" method="syncTemplateStyles">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.bimowu.resume.dto.ResumeFullSaveDto" memberName="practiceList" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
</project>