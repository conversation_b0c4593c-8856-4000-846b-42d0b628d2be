package com.bimowu.resume.exception;

/**
 * 内存不足异常
 * 当PDF生成过程中内存不足时抛出此异常
 */
public class MemoryInsufficientException extends PDFGenerationException {
    
    private final long requiredMemory;
    private final long availableMemory;
    private final double memoryUsagePercent;
    
    public MemoryInsufficientException(String message, long requiredMemory, long availableMemory, double memoryUsagePercent) {
        super("INSUFFICIENT_MEMORY", message, null, null);
        this.requiredMemory = requiredMemory;
        this.availableMemory = availableMemory;
        this.memoryUsagePercent = memoryUsagePercent;
    }
    
    public MemoryInsufficientException(String message, long requiredMemory, long availableMemory, double memoryUsagePercent, Throwable cause) {
        super("INSUFFICIENT_MEMORY", message, cause, null, null);
        this.requiredMemory = requiredMemory;
        this.availableMemory = availableMemory;
        this.memoryUsagePercent = memoryUsagePercent;
    }
    
    public long getRequiredMemory() {
        return requiredMemory;
    }
    
    public long getAvailableMemory() {
        return availableMemory;
    }
    
    public double getMemoryUsagePercent() {
        return memoryUsagePercent;
    }
    
    @Override
    public String getMessage() {
        return String.format("%s (需要: %dMB, 可用: %dMB, 使用率: %.1f%%)", 
            super.getMessage(), 
            requiredMemory / (1024 * 1024), 
            availableMemory / (1024 * 1024), 
            memoryUsagePercent);
    }
}