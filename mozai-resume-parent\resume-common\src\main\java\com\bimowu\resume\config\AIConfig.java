package com.bimowu.resume.config;

import com.volcengine.ark.runtime.service.ArkService;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "ai")
@Data
public class AIConfig {
    private String apiKey;
    private String modelName;
    private UsageLimit usageLimit;

    @Data
    public static class UsageLimit {
        private Integer default_;
        
        // 为了匹配yaml中的default属性
        public Integer getDefault() {
            return default_;
        }
        
        public void setDefault(Integer default_) {
            this.default_ = default_;
        }
    }
    
    @Bean
    public ArkService arkService(){
        ArkService arkService = ArkService
                .builder()
//            .baseUrl()
                .timeout(Duration.ofSeconds(120))
                .connectTimeout(Duration.ofSeconds(20))
                .retryTimes(2)
                .apiKey(apiKey)
                .build();
        return arkService;
    }
}
