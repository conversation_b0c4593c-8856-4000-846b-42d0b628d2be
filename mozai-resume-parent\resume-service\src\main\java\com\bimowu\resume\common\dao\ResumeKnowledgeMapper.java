package com.bimowu.resume.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeKnowledge;
import org.mapstruct.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 简历知识管理Mapper接口
 */
@Mapper
public interface ResumeKnowledgeMapper extends BaseMapper<ResumeKnowledge> {

    /**
     * 插入知识信息
     * 
     * @param resumeKnowledge 知识信息
     * @return 影响的行数
     */
    int insert(ResumeKnowledge resumeKnowledge);
    
    /**
     * 更新知识信息
     * 
     * @param resumeKnowledge 知识信息
     * @return 影响的行数
     */
    int update(ResumeKnowledge resumeKnowledge);
    
    /**
     * 根据ID查询知识信息
     * 
     * @param id 知识ID
     * @return 知识信息
     */
    ResumeKnowledge selectById(@Param("id") Long id);
    
    /**
     * 根据职位类型查询知识信息列表
     * 
     * @param positionType 职位类型
     * @return 知识信息列表
     */
    List<ResumeKnowledge> selectByPositionType(@Param("positionType") Integer positionType);
    
    /**
     * 根据知识目录查询知识信息
     * 
     * @param knowledgeCatalog 知识目录
     * @return 知识信息
     */
    ResumeKnowledge selectByKnowledgeCatalog(@Param("knowledgeCatalog") String knowledgeCatalog);
    
    /**
     * 根据条件查询知识信息列表
     * 
     * @param params 查询条件
     * @return 知识信息列表
     */
    List<ResumeKnowledge> selectByCondition(Map<String, Object> params);
    
    /**
     * 逻辑删除知识信息
     * 
     * @param id 知识ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
} 