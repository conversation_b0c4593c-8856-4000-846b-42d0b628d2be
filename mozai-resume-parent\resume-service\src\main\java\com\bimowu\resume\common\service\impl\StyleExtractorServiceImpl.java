package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.StyleExtractorService;
import org.apache.commons.io.IOUtils;
import com.bimowu.resume.dto.ExtractedStyles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 样式提取服务实现
 */
@Slf4j
@Service
public class StyleExtractorServiceImpl implements StyleExtractorService {
    
    private static final Pattern STYLE_PATTERN = Pattern.compile("<style[^>]*>([\\s\\S]*?)</style>", Pattern.CASE_INSENSITIVE);
    private static final Pattern FONT_FAMILY_PATTERN = Pattern.compile("font-family\\s*:\\s*([^;]+)");
    private static final Pattern FONT_SIZE_PATTERN = Pattern.compile("font-size\\s*:\\s*([^;]+)");
    private static final Pattern COLOR_PATTERN = Pattern.compile("color\\s*:\\s*([^;]+)");
    private static final Pattern BACKGROUND_COLOR_PATTERN = Pattern.compile("background-color\\s*:\\s*([^;]+)");
    
    @Override
    public ExtractedStyles extractStyles(int templateId) {
        try {
            String templatePath = "templates/vue/resume-template-" + templateId + ".vue";
            ClassPathResource resource = new ClassPathResource(templatePath);
            
            if (!resource.exists()) {
                log.warn("Vue模板文件不存在: {}", templatePath);
                return createDefaultStyles(templateId);
            }
            
            String vueContent = new String(IOUtils.toByteArray(resource.getInputStream()), StandardCharsets.UTF_8);
            return extractStylesFromVueContent(vueContent, templateId);
            
        } catch (IOException e) {
            log.error("读取Vue模板文件失败，模板ID: {}", templateId, e);
            return createDefaultStyles(templateId);
        }
    }
    
    @Override
    public ExtractedStyles extractStylesFromVueContent(String vueContent, int templateId) {
        if (!StringUtils.hasText(vueContent)) {
            return createDefaultStyles(templateId);
        }
        
        ExtractedStyles styles = new ExtractedStyles();
        styles.setTimestamp(System.currentTimeMillis());
        styles.setVersion(generateVersion());
        
        // 提取<style>标签中的CSS
        String css = extractCSSFromVue(vueContent);
        styles.setCss(css);
        
        // 从CSS中提取各种样式信息
        if (StringUtils.hasText(css)) {
            styles.setFonts(extractFontInfo(css));
            styles.setColors(extractColorPalette(css));
            styles.setLayout(extractLayoutInfo(css));
            styles.setResponsive(extractResponsiveRules(css));
        } else {
            // 设置默认值
            styles.setFonts(createDefaultFonts());
            styles.setColors(createDefaultColors());
            styles.setLayout(createDefaultLayout());
            styles.setResponsive(createDefaultResponsive());
        }
        
        log.info("成功提取模板 {} 的样式信息", templateId);
        return styles;
    }
    
    @Override
    public ExtractedStyles extractStylesFromCSS(String cssContent) {
        ExtractedStyles styles = new ExtractedStyles();
        styles.setTimestamp(System.currentTimeMillis());
        styles.setVersion(generateVersion());
        styles.setCss(cssContent);
        
        if (StringUtils.hasText(cssContent)) {
            styles.setFonts(extractFontInfo(cssContent));
            styles.setColors(extractColorPalette(cssContent));
            styles.setLayout(extractLayoutInfo(cssContent));
            styles.setResponsive(extractResponsiveRules(cssContent));
        } else {
            styles.setFonts(createDefaultFonts());
            styles.setColors(createDefaultColors());
            styles.setLayout(createDefaultLayout());
            styles.setResponsive(createDefaultResponsive());
        }
        
        return styles;
    }
    
    @Override
    public boolean validateExtractedStyles(ExtractedStyles styles) {
        if (styles == null) {
            return false;
        }
        
        // 检查基本字段
        if (!StringUtils.hasText(styles.getVersion())) {
            return false;
        }
        
        if (styles.getTimestamp() == null || styles.getTimestamp() <= 0) {
            return false;
        }
        
        // 检查字体信息
        if (styles.getFonts() != null) {
            for (ExtractedStyles.FontInfo font : styles.getFonts()) {
                if (!StringUtils.hasText(font.getFamily())) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    @Override
    public ExtractedStyles mergeStyles(ExtractedStyles... stylesList) {
        if (stylesList == null || stylesList.length == 0) {
            return new ExtractedStyles();
        }
        
        ExtractedStyles merged = new ExtractedStyles();
        merged.setTimestamp(System.currentTimeMillis());
        merged.setVersion(generateVersion());
        
        StringBuilder cssBuilder = new StringBuilder();
        List<ExtractedStyles.FontInfo> allFonts = new ArrayList<>();
        
        for (ExtractedStyles styles : stylesList) {
            if (styles == null) continue;
            
            // 合并CSS
            if (StringUtils.hasText(styles.getCss())) {
                cssBuilder.append(styles.getCss()).append("\n");
            }
            
            // 合并字体
            if (styles.getFonts() != null) {
                allFonts.addAll(styles.getFonts());
            }
        }
        
        merged.setCss(cssBuilder.toString());
        merged.setFonts(allFonts);
        
        // 使用第一个非空的颜色和布局信息
        for (ExtractedStyles styles : stylesList) {
            if (styles == null) continue;
            
            if (merged.getColors() == null && styles.getColors() != null) {
                merged.setColors(styles.getColors());
            }
            
            if (merged.getLayout() == null && styles.getLayout() != null) {
                merged.setLayout(styles.getLayout());
            }
            
            if (merged.getResponsive() == null && styles.getResponsive() != null) {
                merged.setResponsive(styles.getResponsive());
            }
        }
        
        return merged;
    }
    
    /**
     * 从Vue文件中提取CSS内容
     */
    private String extractCSSFromVue(String vueContent) {
        StringBuilder cssBuilder = new StringBuilder();
        
        Matcher matcher = STYLE_PATTERN.matcher(vueContent);
        while (matcher.find()) {
            String styleContent = matcher.group(1);
            cssBuilder.append(styleContent).append("\n");
        }
        
        return cssBuilder.toString();
    }
    
    /**
     * 提取字体信息
     */
    private List<ExtractedStyles.FontInfo> extractFontInfo(String css) {
        List<ExtractedStyles.FontInfo> fonts = new ArrayList<>();
        Set<String> processedFonts = new HashSet<>();
        
        Matcher matcher = FONT_FAMILY_PATTERN.matcher(css);
        while (matcher.find()) {
            String fontFamily = matcher.group(1).trim().replaceAll("['\"]", "");
            
            if (!processedFonts.contains(fontFamily)) {
                ExtractedStyles.FontInfo fontInfo = new ExtractedStyles.FontInfo();
                fontInfo.setFamily(fontFamily);
                fontInfo.setStyle("normal");
                fontInfo.setWeight(400);
                fontInfo.setSize(extractFontSize(css, fontFamily));
                fontInfo.setLineHeight("1.5");
                fontInfo.setWebFont(isWebFont(fontFamily));
                
                fonts.add(fontInfo);
                processedFonts.add(fontFamily);
            }
        }
        
        if (fonts.isEmpty()) {
            fonts.addAll(createDefaultFonts());
        }
        
        return fonts;
    }
    
    /**
     * 提取颜色调色板
     */
    private ExtractedStyles.ColorPalette extractColorPalette(String css) {
        ExtractedStyles.ColorPalette palette = new ExtractedStyles.ColorPalette();
        Set<String> colors = new HashSet<>();
        
        // 提取color属性
        Matcher colorMatcher = COLOR_PATTERN.matcher(css);
        while (colorMatcher.find()) {
            colors.add(colorMatcher.group(1).trim());
        }
        
        // 提取background-color属性
        Matcher bgColorMatcher = BACKGROUND_COLOR_PATTERN.matcher(css);
        while (bgColorMatcher.find()) {
            colors.add(bgColorMatcher.group(1).trim());
        }
        
        // 分析颜色并分类
        List<String> colorList = new ArrayList<>(colors);
        if (!colorList.isEmpty()) {
            palette.setPrimary(colorList.get(0));
            if (colorList.size() > 1) {
                palette.setSecondary(colorList.get(1));
            }
            if (colorList.size() > 2) {
                palette.setAccent(colorList.get(2));
            }
        }
        
        // 设置默认颜色
        if (palette.getPrimary() == null) palette.setPrimary("#333333");
        if (palette.getBackground() == null) palette.setBackground("#ffffff");
        if (palette.getText() == null) palette.setText("#333333");
        if (palette.getBorder() == null) palette.setBorder("#cccccc");
        
        // 存储自定义颜色
        Map<String, String> customColors = new HashMap<>();
        int index = 1;
        for (String color : colors) {
            customColors.put("color" + index++, color);
        }
        palette.setCustomColors(customColors);
        
        return palette;
    }
    
    /**
     * 提取布局信息
     */
    private ExtractedStyles.LayoutInfo extractLayoutInfo(String css) {
        ExtractedStyles.LayoutInfo layout = new ExtractedStyles.LayoutInfo();
        
        // 设置默认值
        layout.setWidth(800);
        layout.setHeight(1200);
        layout.setDisplay("block");
        layout.setPosition("static");
        
        // 设置边距
        ExtractedStyles.Margins margins = new ExtractedStyles.Margins();
        margins.setTop("20px");
        margins.setRight("20px");
        margins.setBottom("20px");
        margins.setLeft("20px");
        layout.setMargins(margins);
        
        // 设置内边距
        ExtractedStyles.Padding padding = new ExtractedStyles.Padding();
        padding.setTop("10px");
        padding.setRight("10px");
        padding.setBottom("10px");
        padding.setLeft("10px");
        layout.setPadding(padding);
        
        return layout;
    }
    
    /**
     * 提取响应式规则
     */
    private ExtractedStyles.ResponsiveRules extractResponsiveRules(String css) {
        ExtractedStyles.ResponsiveRules responsive = new ExtractedStyles.ResponsiveRules();
        
        Map<String, String> breakpoints = new HashMap<>();
        breakpoints.put("mobile", "768px");
        breakpoints.put("tablet", "1024px");
        breakpoints.put("desktop", "1200px");
        responsive.setBreakpoints(breakpoints);
        
        responsive.setMediaQueries(new HashMap<>());
        
        return responsive;
    }
    
    /**
     * 创建默认样式
     */
    private ExtractedStyles createDefaultStyles(int templateId) {
        ExtractedStyles styles = new ExtractedStyles();
        styles.setTimestamp(System.currentTimeMillis());
        styles.setVersion("1.0.0");
        styles.setCss("");
        styles.setFonts(createDefaultFonts());
        styles.setColors(createDefaultColors());
        styles.setLayout(createDefaultLayout());
        styles.setResponsive(createDefaultResponsive());
        
        return styles;
    }
    
    /**
     * 创建默认字体
     */
    private List<ExtractedStyles.FontInfo> createDefaultFonts() {
        List<ExtractedStyles.FontInfo> fonts = new ArrayList<>();
        
        ExtractedStyles.FontInfo defaultFont = new ExtractedStyles.FontInfo();
        defaultFont.setFamily("SimSun");
        defaultFont.setStyle("normal");
        defaultFont.setWeight(400);
        defaultFont.setSize("14px");
        defaultFont.setLineHeight("1.5");
        defaultFont.setWebFont(false);
        
        fonts.add(defaultFont);
        return fonts;
    }
    
    /**
     * 创建默认颜色
     */
    private ExtractedStyles.ColorPalette createDefaultColors() {
        ExtractedStyles.ColorPalette colors = new ExtractedStyles.ColorPalette();
        colors.setPrimary("#333333");
        colors.setSecondary("#666666");
        colors.setBackground("#ffffff");
        colors.setText("#333333");
        colors.setBorder("#cccccc");
        colors.setCustomColors(new HashMap<>());
        
        return colors;
    }
    
    /**
     * 创建默认布局
     */
    private ExtractedStyles.LayoutInfo createDefaultLayout() {
        ExtractedStyles.LayoutInfo layout = new ExtractedStyles.LayoutInfo();
        layout.setWidth(800);
        layout.setHeight(1200);
        layout.setDisplay("block");
        layout.setPosition("static");
        
        ExtractedStyles.Margins margins = new ExtractedStyles.Margins();
        margins.setTop("20px");
        margins.setRight("20px");
        margins.setBottom("20px");
        margins.setLeft("20px");
        layout.setMargins(margins);
        
        ExtractedStyles.Padding padding = new ExtractedStyles.Padding();
        padding.setTop("10px");
        padding.setRight("10px");
        padding.setBottom("10px");
        padding.setLeft("10px");
        layout.setPadding(padding);
        
        return layout;
    }
    
    /**
     * 创建默认响应式规则
     */
    private ExtractedStyles.ResponsiveRules createDefaultResponsive() {
        ExtractedStyles.ResponsiveRules responsive = new ExtractedStyles.ResponsiveRules();
        
        Map<String, String> breakpoints = new HashMap<>();
        breakpoints.put("mobile", "768px");
        breakpoints.put("tablet", "1024px");
        breakpoints.put("desktop", "1200px");
        responsive.setBreakpoints(breakpoints);
        
        responsive.setMediaQueries(new HashMap<>());
        
        return responsive;
    }
    
    /**
     * 提取字体大小
     */
    private String extractFontSize(String css, String fontFamily) {
        // 简化实现，返回默认值
        return "14px";
    }
    
    /**
     * 检查是否为Web字体
     */
    private boolean isWebFont(String fontFamily) {
        String[] webFonts = {"Google Fonts", "Adobe Fonts", "Font Awesome"};
        for (String webFont : webFonts) {
            if (fontFamily.contains(webFont)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成版本号
     */
    private String generateVersion() {
        return "v" + System.currentTimeMillis();
    }
}