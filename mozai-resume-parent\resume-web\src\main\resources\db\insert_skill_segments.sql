-- 插入技能数据
INSERT INTO `resume_skill` (`ski_id`, `name`, `create_time`, `update_time`) VALUES
(1, 'Java', NOW(), NOW()),
(2, 'Python', NOW(), NOW()),
(3, 'JavaScript', NOW(), NOW()),
(4, 'Vue.js', NOW(), NOW()),
(5, 'Spring Boot', NOW(), NOW()),
(6, 'MySQL', NOW(), NOW()),
(7, 'Redis', NOW(), NOW());

-- 插入技能段落数据（Java技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(1, 'Java基础', '一般', '了解Java基础语法和概念，能够编写简单的程序。', NOW(), NOW()),
(1, 'Java基础', '良好', '掌握Java语言基础，了解面向对象编程，能够独立完成一般难度的Java程序开发。', NOW(), NOW()),
(1, 'Java基础', '熟练', '熟练掌握Java语言，深入理解面向对象编程思想，能高效实现各类Java应用。', NOW(), NOW()),
(1, 'Java基础', '擅长', '精通Java核心知识，包括多线程、集合框架、IO流等，能够设计和实现复杂系统。', NOW(), NOW()),
(1, 'Java基础', '精通', '对Java语言有深入研究，熟悉JVM原理，能解决高并发、性能优化等复杂问题，有丰富的项目经验。', NOW(), NOW());

-- 插入技能段落数据（Python技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(2, 'Python开发', '一般', '了解Python基础语法，能够编写简单的脚本程序。', NOW(), NOW()),
(2, 'Python开发', '良好', '掌握Python编程，了解主要的库和框架，能够开发一般的应用程序。', NOW(), NOW()),
(2, 'Python开发', '熟练', '熟练使用Python进行开发，熟悉多种库和框架，能够高效实现各类应用。', NOW(), NOW()),
(2, 'Python开发', '擅长', '精通Python编程，能够设计和实现复杂系统，擅长数据分析和处理。', NOW(), NOW()),
(2, 'Python开发', '精通', '对Python有深入研究，能够解决高性能、高并发等复杂问题，有丰富的项目经验。', NOW(), NOW());

-- 插入技能段落数据（JavaScript技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(3, 'JavaScript编程', '一般', '了解JavaScript基础语法，能够编写简单的网页交互功能。', NOW(), NOW()),
(3, 'JavaScript编程', '良好', '掌握JavaScript编程，熟悉DOM操作和常用API，能够开发一般的Web应用。', NOW(), NOW()),
(3, 'JavaScript编程', '熟练', '熟练使用JavaScript进行开发，熟悉现代JS框架和工具，能够高效实现各类Web应用。', NOW(), NOW()),
(3, 'JavaScript编程', '擅长', '精通JavaScript编程，深入理解异步编程和闭包，能够设计和实现复杂的前端系统。', NOW(), NOW()),
(3, 'JavaScript编程', '精通', '对JavaScript有深入研究，熟悉ES6+特性和各种设计模式，能够解决复杂的性能和兼容性问题。', NOW(), NOW());

-- 插入技能段落数据（Vue.js技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(4, 'Vue.js开发', '一般', '了解Vue.js基础，能够使用Vue构建简单的单页面应用。', NOW(), NOW()),
(4, 'Vue.js开发', '良好', '掌握Vue.js开发，熟悉组件化开发和常用特性，能够开发一般的Vue应用。', NOW(), NOW()),
(4, 'Vue.js开发', '熟练', '熟练使用Vue.js进行开发，熟悉Vue全家桶，能够高效实现各类Web应用。', NOW(), NOW()),
(4, 'Vue.js开发', '擅长', '精通Vue.js开发，深入理解Vue的响应式原理，能够设计和实现复杂的前端系统。', NOW(), NOW()),
(4, 'Vue.js开发', '精通', '对Vue.js有深入研究，熟悉Vue源码和内部实现，能够解决复杂的性能优化和状态管理问题。', NOW(), NOW());

-- 插入技能段落数据（Spring Boot技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(5, 'Spring Boot开发', '一般', '了解Spring Boot基础，能够搭建简单的Web应用。', NOW(), NOW()),
(5, 'Spring Boot开发', '良好', '掌握Spring Boot开发，熟悉常用注解和配置，能够开发一般的Spring Boot应用。', NOW(), NOW()),
(5, 'Spring Boot开发', '熟练', '熟练使用Spring Boot进行开发，熟悉Spring全家桶，能够高效实现各类Web应用和微服务。', NOW(), NOW()),
(5, 'Spring Boot开发', '擅长', '精通Spring Boot开发，深入理解Spring核心原理，能够设计和实现复杂的企业级应用。', NOW(), NOW()),
(5, 'Spring Boot开发', '精通', '对Spring Boot有深入研究，熟悉Spring源码和内部实现，能够解决复杂的性能优化和架构设计问题。', NOW(), NOW());

-- 插入技能段落数据（MySQL技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(6, 'MySQL数据库', '一般', '了解MySQL基础，能够编写简单的SQL查询语句。', NOW(), NOW()),
(6, 'MySQL数据库', '良好', '掌握MySQL数据库，熟悉常用SQL语句和数据库设计，能够实现一般的数据库应用。', NOW(), NOW()),
(6, 'MySQL数据库', '熟练', '熟练使用MySQL数据库，熟悉索引优化和事务管理，能够高效实现各类数据库应用。', NOW(), NOW()),
(6, 'MySQL数据库', '擅长', '精通MySQL数据库，深入理解数据库原理，能够设计和实现高性能、高可用的数据库系统。', NOW(), NOW()),
(6, 'MySQL数据库', '精通', '对MySQL有深入研究，熟悉MySQL底层实现和调优技术，能够解决复杂的性能问题和高并发场景。', NOW(), NOW());

-- 插入技能段落数据（Redis技能）
INSERT INTO `resume_skill_segment` (`skill_id`, `seg_name`, `proficiency`, `text`, `create_time`, `update_time`) VALUES
(7, 'Redis缓存', '一般', '了解Redis基础，能够使用简单的Redis命令。', NOW(), NOW()),
(7, 'Redis缓存', '良好', '掌握Redis缓存，熟悉常用数据结构和命令，能够实现一般的缓存应用。', NOW(), NOW()),
(7, 'Redis缓存', '熟练', '熟练使用Redis缓存，熟悉分布式锁和消息队列等高级特性，能够高效实现各类缓存应用。', NOW(), NOW()),
(7, 'Redis缓存', '擅长', '精通Redis缓存，深入理解Redis原理，能够设计和实现高性能、高可用的缓存系统。', NOW(), NOW()),
(7, 'Redis缓存', '精通', '对Redis有深入研究，熟悉Redis底层实现和调优技术，能够解决复杂的性能问题和高并发场景。', NOW(), NOW()); 