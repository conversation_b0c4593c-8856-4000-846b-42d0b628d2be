<template>
  <div class="pdf-test-page">
    <el-card class="test-card">
      <div slot="header" class="clearfix">
        <span>PDF系统测试页面</span>
      </div>
      
      <div class="test-section">
        <h3>PDF导出测试</h3>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="简历ID:">
            <el-input v-model="testForm.resumeId" placeholder="请输入简历ID"></el-input>
          </el-form-item>
          <el-form-item label="简历名称:">
            <el-input v-model="testForm.resumeName" placeholder="请输入简历名称"></el-input>
          </el-form-item>
        </el-form>
        
        <div class="test-buttons">
          <PDFExportButton 
            :resume-id="testForm.resumeId" 
            :resume-data="{ information: { name: testForm.resumeName } }"
            @export-success="onExportSuccess"
            @export-error="onExportError"
            @preview-success="onPreviewSuccess"
            @preview-error="onPreviewError" />
        </div>
      </div>
      
      <div class="test-section">
        <h3>PDF系统管理</h3>
        <PDFSystemManager />
      </div>
      
      <div class="test-section">
        <h3>测试日志</h3>
        <el-table :data="logs" size="mini" max-height="300">
          <el-table-column prop="timestamp" label="时间" width="160">
            <template slot-scope="scope">
              {{ formatTime(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="80"></el-table-column>
          <el-table-column prop="success" label="结果" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'" size="mini">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="消息"></el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import PDFExportButton from '@/components/PDFExportButton.vue'
import PDFSystemManager from '@/components/PDFSystemManager.vue'

export default {
  name: 'PDFTestPage',
  components: {
    PDFExportButton,
    PDFSystemManager
  },
  data() {
    return {
      testForm: {
        resumeId: '1',
        resumeName: '测试简历'
      },
      logs: []
    }
  },
  methods: {
    onExportSuccess(event) {
      this.addLog('导出', true, `PDF导出成功，耗时: ${event.duration}ms`)
    },
    
    onExportError(event) {
      this.addLog('导出', false, `PDF导出失败: ${event.error}`)
    },
    
    onPreviewSuccess(event) {
      this.addLog('预览', true, 'PDF预览成功')
    },
    
    onPreviewError(event) {
      this.addLog('预览', false, `PDF预览失败: ${event.error}`)
    },
    
    addLog(type, success, message) {
      this.logs.unshift({
        timestamp: new Date(),
        type,
        success,
        message
      })
      
      // 只保留最近20条日志
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.pdf-test-page {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.test-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.test-buttons {
  margin-top: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>