package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.AutoStyleUpdateService;
import com.bimowu.resume.common.service.StyleExtractorService;
import com.bimowu.resume.common.service.StyleSyncService;
import com.bimowu.resume.config.PDFGenerationConfig;
import com.bimowu.resume.dto.ExtractedStyles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 自动样式更新服务实现
 */
@Slf4j
@Service
public class AutoStyleUpdateServiceImpl implements AutoStyleUpdateService {
    
    @Autowired
    private StyleExtractorService styleExtractorService;
    
    @Autowired
    private StyleSyncService styleSyncService;
    
    @Autowired
    private PDFGenerationConfig pdfConfig;
    
    private ScheduledExecutorService scheduler;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private long updateIntervalSeconds;
    
    @PostConstruct
    public void init() {
        this.updateIntervalSeconds = pdfConfig.getTemplate().getSyncInterval();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "auto-style-update");
            thread.setDaemon(true);
            return thread;
        });
        
        // 如果配置启用，则自动启动
        if (updateIntervalSeconds > 0) {
            startAutoUpdate();
        }
    }
    
    @PreDestroy
    public void destroy() {
        stopAutoUpdate();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    @Override
    public void startAutoUpdate() {
        if (running.compareAndSet(false, true)) {
            log.info("启动自动样式更新服务，更新间隔: {} 秒", updateIntervalSeconds);
            
            scheduler.scheduleWithFixedDelay(
                this::checkAndUpdateAllTemplates,
                updateIntervalSeconds, // 初始延迟
                updateIntervalSeconds, // 执行间隔
                TimeUnit.SECONDS
            );
        } else {
            log.warn("自动样式更新服务已经在运行中");
        }
    }
    
    @Override
    public void stopAutoUpdate() {
        if (running.compareAndSet(true, false)) {
            log.info("停止自动样式更新服务");
            
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        }
    }
    
    @Override
    public void checkAndUpdateAllTemplates() {
        if (!running.get()) {
            log.debug("自动更新服务未运行，跳过检查");
            return;
        }
        
        log.info("开始检查所有模板样式更新");
        
        try {
            // 获取所有活跃模板ID
            for (int templateId = 1; templateId <= 10; templateId++) {
                checkAndUpdateTemplate(templateId);
            }
            
            log.info("完成所有模板样式检查");
            
        } catch (Exception e) {
            log.error("检查模板样式更新时发生错误", e);
        }
    }
    
    @Override
    public void checkAndUpdateTemplate(Integer templateId) {
        try {
            log.debug("检查模板 {} 的样式更新", templateId);
            
            // 提取最新的前端样式
            ExtractedStyles latestStyles = styleExtractorService.extractStyles(templateId);
            
            // 获取当前后端样式
            ExtractedStyles currentStyles = styleSyncService.getLatestStyles(templateId);
            
            // 检查是否需要更新
            if (needsUpdate(latestStyles, currentStyles)) {
                log.info("模板 {} 需要样式更新", templateId);
                
                // 执行同步
                styleSyncService.syncTemplateStyles(templateId, latestStyles);
                
                log.info("模板 {} 样式更新完成", templateId);
            } else {
                log.debug("模板 {} 样式无需更新", templateId);
            }
            
        } catch (Exception e) {
            log.error("检查模板 {} 样式更新时发生错误", templateId, e);
        }
    }
    
    @Override
    public boolean isRunning() {
        return running.get();
    }
    
    @Override
    public void setUpdateInterval(long intervalSeconds) {
        this.updateIntervalSeconds = intervalSeconds;
        
        // 如果服务正在运行，重启以应用新的间隔
        if (running.get()) {
            stopAutoUpdate();
            startAutoUpdate();
        }
    }
    
    /**
     * 判断是否需要更新
     */
    private boolean needsUpdate(ExtractedStyles latestStyles, ExtractedStyles currentStyles) {
        if (currentStyles == null) {
            return true;
        }
        
        if (latestStyles == null) {
            return false;
        }
        
        // 比较时间戳
        if (latestStyles.getTimestamp() != null && currentStyles.getTimestamp() != null) {
            return latestStyles.getTimestamp() > currentStyles.getTimestamp();
        }
        
        // 比较版本
        if (latestStyles.getVersion() != null && currentStyles.getVersion() != null) {
            return !latestStyles.getVersion().equals(currentStyles.getVersion());
        }
        
        // 比较CSS内容
        if (latestStyles.getCss() != null && currentStyles.getCss() != null) {
            return !latestStyles.getCss().equals(currentStyles.getCss());
        }
        
        return true;
    }
}