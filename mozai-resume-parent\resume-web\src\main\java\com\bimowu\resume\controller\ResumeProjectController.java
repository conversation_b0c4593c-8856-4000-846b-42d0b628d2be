package com.bimowu.resume.controller;


import com.bimowu.resume.vo.ResumeCategoryVo;
import com.bimowu.resume.vo.ResumeProjectContentVo;
import com.bimowu.resume.vo.ResumeProjectVo;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.bimowu.resume.common.service.ResumeProjectService;
import com.bimowu.resume.entity.ResumeProject;
import com.bimowu.resume.base.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bimowu.resume.common.service.ResumeProjectContentService;
import com.bimowu.resume.entity.ResumeProjectContent;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 项目表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/project")
public class ResumeProjectController {

    @Autowired
    private ResumeProjectService resumeProjectService;

    @Autowired
    private ResumeProjectContentService resumeProjectContentService;

    /**
     * 根据职位类别id查询项目列表
     */
    @GetMapping("/listByCategory")
    public BaseResponse listByCategory(@RequestParam Long catId) {
        List<ResumeProjectVo> voList = new ArrayList<>();
        QueryWrapper<ResumeProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cat_id", catId);
        List<ResumeProject> list = resumeProjectService.list(queryWrapper);
        for (ResumeProject resumeProject : list) {
            ResumeProjectVo resumeProjectVo = new ResumeProjectVo();
            BeanUtils.copyProperties(resumeProject,resumeProjectVo);
            voList.add(resumeProjectVo);
        }
        return BaseResponse.ok(voList);
    }

    /**
     * 根据项目id查询项目内容列表
     */
    @GetMapping("/contentListByProject")
    public BaseResponse contentListByProject(@RequestParam Long proId) {
        List<ResumeProjectContentVo> voList = new ArrayList<>();
        QueryWrapper<ResumeProjectContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", proId).orderByAsc("content_order");
        List<ResumeProjectContent> list = resumeProjectContentService.list(queryWrapper);
        for (ResumeProjectContent resumeProjectContent : list) {
            ResumeProjectContentVo resumeProjectContentVo = new ResumeProjectContentVo();
            BeanUtils.copyProperties(resumeProjectContent,resumeProjectContentVo);
            voList.add(resumeProjectContentVo);
        }
        return BaseResponse.ok(voList);
    }
}

