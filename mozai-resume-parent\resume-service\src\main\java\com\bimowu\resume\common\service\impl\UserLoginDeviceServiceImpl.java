package com.bimowu.resume.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.resume.common.dao.UserLoginDeviceMapper;
import com.bimowu.resume.common.service.UserLoginDeviceService;
import com.bimowu.resume.entity.UserLoginDevice;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class UserLoginDeviceServiceImpl extends ServiceImpl<UserLoginDeviceMapper, UserLoginDevice>
    implements UserLoginDeviceService {
    @Override
    public UserLoginDevice findItemByToken(String lastLoginToken, String type) {
        // QueryWrapper<UserLoginDevice> queryWrapper = new QueryWrapper<>();
        // queryWrapper.eq(UserLoginDevice::getLoginType, lastLoginToken);
        // queryWrapper.eq("loginType", type);
        List<UserLoginDevice> list = baseMapper.selectList(Wrappers.<UserLoginDevice>lambdaQuery()
            .eq(UserLoginDevice::getToken, lastLoginToken).eq(UserLoginDevice::getLoginType, type));
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }
}
