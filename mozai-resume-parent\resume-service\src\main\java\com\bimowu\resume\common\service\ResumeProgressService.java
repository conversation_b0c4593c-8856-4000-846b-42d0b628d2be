package com.bimowu.resume.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.resume.entity.ResumeProgress;

import java.util.List;
import java.util.Map;

/**
 * 简历进度管理服务接口
 */
public interface ResumeProgressService extends IService<ResumeProgress> {
    
    /**
     * 创建进度信息
     * 
     * @param resumeProgress 进度信息
     * @return 是否成功
     */
    boolean createResumeProgress(ResumeProgress resumeProgress);
    
    /**
     * 更新进度信息
     * 
     * @param resumeProgress 进度信息
     * @return 是否成功
     */
    boolean updateResumeProgress(ResumeProgress resumeProgress);
    
    /**
     * 获取进度信息
     * 
     * @param id 进度ID
     * @return 进度信息
     */
    ResumeProgress getResumeProgressById(Long id);
    
    /**
     * 根据用户ID获取进度信息
     * 
     * @param userId 用户ID
     * @return 进度信息
     */
    ResumeProgress getResumeProgressByUserId(Integer userId);
    
    /**
     * 根据用户ID和简历ID获取进度信息
     * 
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @return 进度信息
     */
    ResumeProgress getResumeProgressByUserIdAndResumeId(Integer userId, Long resumeId);
    
    /**
     * 根据条件查询进度列表
     * 
     * @param params 查询条件
     * @return 进度列表
     */
    List<ResumeProgress> getResumeProgressByCondition(Map<String, Object> params);
    
    /**
     * 更新进度阶段
     * 
     * @param id 进度ID
     * @param currentStage 当前阶段
     * @return 是否成功
     */
    boolean updateResumeProgressStage(Long id, String currentStage);
    
    /**
     * 删除进度信息
     * 
     * @param id 进度ID
     * @return 是否成功
     */
    boolean deleteResumeProgress(Long id);
} 