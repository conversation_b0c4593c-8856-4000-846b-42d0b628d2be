package com.bimowu.resume.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.resume.common.dao.ResumeKnowledgeMapper;
import com.bimowu.resume.common.service.ResumeKnowledgeService;
import com.bimowu.resume.entity.ResumeKnowledge;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 简历知识管理服务实现类
 */
@Service
@Slf4j
public class ResumeKnowledgeServiceImpl extends ServiceImpl<ResumeKnowledgeMapper, ResumeKnowledge>
        implements ResumeKnowledgeService {

    @Autowired
    private ResumeKnowledgeMapper resumeKnowledgeMapper;

    @Override
    @Transactional
    public boolean createResumeKnowledge(ResumeKnowledge resumeKnowledge) {
        log.info("创建知识信息: positionType={}, knowledgeCatalog={}", 
                resumeKnowledge.getPositionType(), resumeKnowledge.getKnowledgeCatalog());
        
        // 设置默认值
        if (resumeKnowledge.getIsDeleted() == null) {
            resumeKnowledge.setIsDeleted(0);
        }
        
        // 设置时间
        Date now = new Date();
        resumeKnowledge.setCreateTime(now);
        resumeKnowledge.setUpdateTime(now);
        
        try {
            int result = resumeKnowledgeMapper.insert(resumeKnowledge);
            return result > 0;
        } catch (Exception e) {
            log.error("创建知识信息失败", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean updateResumeKnowledge(ResumeKnowledge resumeKnowledge) {
        log.info("更新知识信息: id={}, positionType={}, knowledgeCatalog={}", 
                resumeKnowledge.getId(), resumeKnowledge.getPositionType(), resumeKnowledge.getKnowledgeCatalog());
        
        // 设置更新时间
        resumeKnowledge.setUpdateTime(new Date());
        
        try {
            int result = resumeKnowledgeMapper.update(resumeKnowledge);
            return result > 0;
        } catch (Exception e) {
            log.error("更新知识信息失败", e);
            throw e;
        }
    }

    @Override
    public ResumeKnowledge getResumeKnowledgeById(Long id) {
        return resumeKnowledgeMapper.selectById(id);
    }

    @Override
    public List<ResumeKnowledge> getResumeKnowledgeByPositionType(Integer positionType) {
        return resumeKnowledgeMapper.selectByPositionType(positionType);
    }

    @Override
    public ResumeKnowledge getResumeKnowledgeByKnowledgeCatalog(String knowledgeCatalog) {
        return resumeKnowledgeMapper.selectByKnowledgeCatalog(knowledgeCatalog);
    }

    @Override
    public List<ResumeKnowledge> getResumeKnowledgeByCondition(Map<String, Object> params) {
        return resumeKnowledgeMapper.selectByCondition(params);
    }

    @Override
    @Transactional
    public boolean deleteResumeKnowledge(Long id) {
        log.info("删除知识信息: id={}", id);
        
        try {
            int result = resumeKnowledgeMapper.deleteById(id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除知识信息失败", e);
            throw e;
        }
    }
} 