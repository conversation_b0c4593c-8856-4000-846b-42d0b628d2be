<template>
  <div class="form-container">
    <el-form :model="formData" label-width="80px">
      <el-form-item label="姓名">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      
      <el-form-item label="性别">
        <el-select v-model="formData.gender" placeholder="请选择性别">
          <el-option label="男" value="男" />
          <el-option label="女" value="女" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="出生日期">
        <el-date-picker
          v-model="formData.birthdate"
          type="date"
          placeholder="选择出生日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
        <el-checkbox v-model="formData.showAge" style="margin-left: 10px">显示年龄</el-checkbox>
      </el-form-item>
      
      <el-form-item label="联系电话">
        <el-input v-model="formData.phone" placeholder="请输入联系电话" />
      </el-form-item>
      
      <el-form-item label="联系邮箱">
        <el-input v-model="formData.email" placeholder="请输入联系邮箱" />
      </el-form-item>
      
      <el-form-item label="籍贯">
        <el-input v-model="formData.hometown" placeholder="请输入籍贯" />
      </el-form-item>
      
      <el-form-item label="民族">
        <el-input v-model="formData.ethnicity" placeholder="请输入民族" />
      </el-form-item>
      
      <el-form-item label="政治面貌">
        <el-select v-model="formData.politicalStatus" placeholder="请选择政治面貌">
          <el-option label="群众" value="群众" />
          <el-option label="共青团员" value="共青团员" />
          <el-option label="中共党员" value="中共党员" />
          <el-option label="中共预备党员" value="中共预备党员" />
          <el-option label="民主党派" value="民主党派" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="照片">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="handleAvatarUpload"
        >
          <img v-if="formData.photo" :src="formData.photo" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><plus /></el-icon>
        </el-upload>
        <div class="upload-tip">请上传证件照（推荐尺寸：295*413像素）</div>
      </el-form-item>
      
      <el-form-item label="婚姻状况">
        <el-select v-model="formData.maritalStatus" placeholder="请选择婚姻状况">
          <el-option label="未婚" value="未婚" />
          <el-option label="已婚" value="已婚" />
          <el-option label="保密" value="保密" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="身高">
        <el-input-number v-model="formData.height" :min="0" :max="250" placeholder="cm" />
        <span class="unit">cm</span>
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref({
  name: '',
  gender: '',
  birthdate: '',
  showAge: false,
  phone: '',
  email: '',
  hometown: '',
  ethnicity: '',
  politicalStatus: '',
  photo: '',
  maritalStatus: '未婚',
  height: ''
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    Object.keys(formData.value).forEach(key => {
      if (newVal[key] !== undefined) {
        formData.value[key] = newVal[key]
      }
    })
  }
}, { immediate: true, deep: true })

// 保存表单
const saveForm = () => {
  emit('update', { ...formData.value })
  ElMessage.success('基本信息已保存')
}

// 重置表单
const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    if (props.data[key] !== undefined) {
      formData.value[key] = props.data[key]
    } else {
      formData.value[key] = ''
    }
  })
  ElMessage.info('已重置为上次保存的内容')
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 头像上传处理
const handleAvatarUpload = (options) => {
  const file = options.file
  const reader = new FileReader()
  reader.readAsDataURL(file)
  reader.onload = () => {
    formData.value.photo = reader.result
  }
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.avatar-uploader {
  width: 120px;
  height: 170px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: border-color 0.3s;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}
</style> 