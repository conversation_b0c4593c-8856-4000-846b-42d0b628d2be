package com.bimowu.resume.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.TemplateStyleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模板样式配置Mapper
 */
@Mapper
public interface TemplateStyleConfigMapper extends BaseMapper<TemplateStyleConfig> {
    
    /**
     * 根据模板ID查询样式配置
     */
    TemplateStyleConfig selectByTemplateId(@Param("templateId") Integer templateId);
    
    /**
     * 查询所有启用的样式配置
     */
    List<TemplateStyleConfig> selectEnabledConfigs();
    
    /**
     * 根据版本查询样式配置
     */
    List<TemplateStyleConfig> selectByVersion(@Param("version") String version);
    
    /**
     * 批量更新同步时间
     */
    int batchUpdateSyncTime(@Param("templateIds") List<Integer> templateIds);
    
    /**
     * 查询需要同步的模板配置
     */
    List<TemplateStyleConfig> selectNeedSyncConfigs(@Param("syncIntervalSeconds") long syncIntervalSeconds);
    
    /**
     * 根据模板ID删除配置
     */
    int deleteByTemplateId(@Param("templateId") Integer templateId);
}