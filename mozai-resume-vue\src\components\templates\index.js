import Template1 from './Template1.vue';
import Template2 from './Template2.vue';
import Template3 from './Template3.vue';
import Template4 from './Template4.vue';
import Template5 from './Template5.vue';
import Template6 from './Template6.vue';
import Template7 from './Template7.vue';
import Template8 from './Template8.vue';
import Template9 from './Template9.vue';
import Template10 from './Template10.vue';
import TemplateSelector from './TemplateSelector.vue';
import ResumePreview from './ResumePreview.vue';

// 模板配置信息
export const templateConfig = [
  { 
    id: 1, 
    name: '模板1',
    thumbnail: import.meta.env.BASE_URL + 'images/muban1.png', 
    component: Template1,
    features: ['专业简洁', '渐变标题', '适合程序员'] 
  },
  { 
    id: 2, 
    name: '模板2',
    thumbnail: import.meta.env.BASE_URL + 'images/muban2.png',
    component: Template2,
    features: ['单列布局', '红色标题', '适合设计师'] 
  },
  { 
    id: 3, 
    name: '模板3',
    thumbnail: import.meta.env.BASE_URL + 'images/muban3.png',
    component: Template3, 
    features: ['清新简约', '灰色配色', '通用模板'] 
  },
  { 
    id: 4, 
    name: '模板4',
    thumbnail: import.meta.env.BASE_URL + 'images/muban4.png',
    component: Template4, 
    features: ['商务风格', '标签式布局', '突出技能'] 
  },
  { 
    id: 5, 
    name: '模板5',
    thumbnail: import.meta.env.BASE_URL + 'images/muban5.png',
    component: Template5, 
    features: ['蓝色主题', '时间轴布局', '简洁大方'] 
  },
  { 
    id: 6, 
    name: '模板6',
    thumbnail: import.meta.env.BASE_URL + 'images/muban6.png',
    component: Template6, 
    features: ['左右分栏', '绿色主题', '中英文标题'] 
  },
  { 
    id: 7, 
    name: '模板7',
    thumbnail: import.meta.env.BASE_URL + 'images/muban7.png',
    component: Template7, 
    features: ['红色标题', '居中布局', '突出项目经验'] 
  },
];

// 获取模板组件函数
export const getTemplateComponent = (templateId) => {
  const template = templateConfig.find(t => t.id === Number(templateId));
  return template ? template.component : Template1;
};

export {
  Template1,
  Template2,
  Template3,
  Template4,
  Template5,
  Template6,
  Template7,
  Template8,
  Template9,
  Template10,
  TemplateSelector,
  ResumePreview
}; 