<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            line-height: 1.6;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 40px;
            box-sizing: border-box;
        }
        
        /* 头部样式 */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .name {
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
            color: #7f8c8d;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
        }
        
        .contact-icon {
            margin-right: 8px;
            color: #3498db;
        }
        
        .job-objective {
            margin-top: 20px;
            font-style: italic;
            color: #3498db;
        }
        
        /* 分隔线 */
        .divider {
            height: 1px;
            background-color: #ecf0f1;
            margin: 25px 0;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #3498db;
        }
        
        /* 项目经验样式 */
        .entry {
            margin-bottom: 25px;
        }
        
        .entry:last-child {
            margin-bottom: 0;
        }
        
        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .entry-title {
            font-weight: 500;
            font-size: 18px;
            color: #2c3e50;
        }
        
        .entry-subtitle {
            color: #7f8c8d;
            font-size: 16px;
            margin-top: 3px;
        }
        
        .entry-date {
            color: #95a5a6;
            font-size: 14px;
            white-space: nowrap;
        }
        
        .entry-content {
            color: #555;
            line-height: 1.7;
        }
        
        /* 技能部分样式 */
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .skill-item {
            flex: 1;
            min-width: 150px;
        }
        
        .skill-name {
            font-weight: 500;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .skill-level {
            height: 4px;
            background-color: #ecf0f1;
            border-radius: 2px;
            margin-bottom: 3px;
        }
        
        .skill-level-fill {
            height: 100%;
            background-color: #3498db;
            border-radius: 2px;
        }
        
        /* 列表样式 */
        ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 8px;
            position: relative;
        }
        
        li:last-child {
            margin-bottom: 0;
        }
        
        /* 其他元素样式 */
        p {
            margin: 0 0 10px 0;
        }
        
        p:last-child {
            margin-bottom: 0;
        }
        
        strong {
            font-weight: 500;
            color: #2c3e50;
        }
        
        em {
            font-style: italic;
            color: #7f8c8d;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        /* 头像样式（如果需要） */
        .avatar-container {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            border-radius: 50%;
            overflow: hidden;
        }
        
        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <div class="header">
            <div class="avatar-container">
                ${avatar}
            </div>
            <div class="name">${name}</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span class="contact-icon">👤</span>
                    <span>${gender} | ${age}岁</span>
                </div>
                <div class="contact-item">
                    <span class="contact-icon">📞</span>
                    <span>${phone}</span>
                </div>
                <div class="contact-item">
                    <span class="contact-icon">📧</span>
                    <span>${email}</span>
                </div>
                <div class="contact-item">
                    <span class="contact-icon">🏠</span>
                    <span>${hometown}</span>
                </div>
            </div>
            <div class="job-objective">${jobObjective}</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 教育经历 -->
        <div class="section">
            <div class="section-title">教育经历</div>
            ${education}
        </div>
        
        <div class="divider"></div>
        
        <!-- 专业技能 -->
        <div class="section">
            <div class="section-title">专业技能</div>
            ${skills}
        </div>
        
        <div class="divider"></div>
        
        <!-- 工作经验 -->
        <div class="section">
            <div class="section-title">工作经验</div>
            ${work}
        </div>
        
        <div class="divider"></div>
        
        <!-- 项目经验 -->
        <div class="section">
            <div class="section-title">项目经验</div>
            ${projects}
        </div>
        
        <div class="divider"></div>
        
        <!-- 练手项目 -->
        <div class="section">
            <div class="section-title">练手项目</div>
            ${practices}
        </div>
        
        <div class="divider"></div>
        
        <!-- 证书奖项 -->
        <div class="section">
            <div class="section-title">证书奖项</div>
            ${certificates}
        </div>
        
        <div class="divider"></div>
        
        <!-- 校园经历 -->
        <div class="section">
            <div class="section-title">校园经历</div>
            ${campus}
        </div>
        
        <div class="divider"></div>
        
        <!-- 兴趣爱好 -->
        <div class="section">
            <div class="section-title">兴趣爱好</div>
            ${interests}
        </div>
        
        <div class="divider"></div>
        
        <!-- 自我评价 -->
        <div class="section">
            <div class="section-title">自我评价</div>
            ${selfEvaluation}
        </div>
    </div>
</body>
</html> 