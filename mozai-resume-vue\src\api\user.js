import request from '../utils/request.js'

// 账号密码登录
export function loginByPassword(data) {
  return request({
    url: '/sso/login',
    method: 'post',
    data
  })
}

// 手机号验证码登录
export function loginByMobile(data) {
  return request({
    url: '/sso/mobileLogin',
    method: 'post',
    data
  })
}

// 获取短信验证码
export function getVerifyCode(mobile) {
  return request({
    url: '/sso/verifyCode',
    method: 'get',
    params: { mobile }
  })
}

// 获取当前用户信息
export function getUserInfo() {
  console.log('API - 调用获取用户信息接口')
  return request({
    url: '/user/info',
    method: 'get'
  }).then(res => {
    console.log('API - 获取用户信息成功:', res)
    return res
  }).catch(err => {
    console.error('API - 获取用户信息失败:', err)
    throw err
  })
}

// 注销登录
export function logout() {
  console.log('API - 调用注销登录接口')
  return request({
    url: '/user/logout',
    method: 'post'
  }).then(res => {
    console.log('API - 注销登录成功:', res)
    return res
  }).catch(err => {
    console.error('API - 注销登录失败:', err)
    throw err
  })
}










