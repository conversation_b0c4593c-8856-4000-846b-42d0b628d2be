package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 模板样式配置实体
 */
@Data
@TableName("resume_template_style_config")
public class TemplateStyleConfig {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板ID
     */
    private Integer templateId;
    
    /**
     * CSS样式内容
     */
    private String cssContent;
    
    /**
     * 字体配置JSON
     */
    private String fontConfig;
    
    /**
     * 颜色调色板JSON
     */
    private String colorPalette;
    
    /**
     * 布局配置JSON
     */
    private String layoutConfig;
    
    /**
     * 响应式规则JSON
     */
    private String responsiveRules;
    
    /**
     * 最后同步时间
     */
    private Date lastSyncTime;
    
    /**
     * 样式版本
     */
    private String version;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 备注
     */
    private String remark;
}