<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.resume.common.dao.ResumeCertificateMapper">

    <select id="selectByResumeId" resultType="com.bimowu.resume.vo.ResumeCertificateVo"
            parameterType="java.lang.Long">
        SELECT * FROM resume_certificate WHERE resume_id = #{resumeId} AND is_delete = 0
    </select>
</mapper>
