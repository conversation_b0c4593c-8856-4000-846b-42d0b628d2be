<template>
  <div class="resume-template template-10">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <div class="personal-info">
          <h1 class="name">{{ resume.modules.basic?.name || '未填写' }}</h1>
          <div class="job-title">{{ resume.modules.basic?.jobObjective || '求职意向：未填写' }}</div>
        </div>
        <div class="contact-section">
          <div class="contact-item" v-if="resume.modules.basic?.phone">
            <div class="contact-icon"><i class="el-icon-phone"></i></div>
            <div class="contact-text">{{ resume.modules.basic.phone }}</div>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.email">
            <div class="contact-icon"><i class="el-icon-message"></i></div>
            <div class="contact-text">{{ resume.modules.basic.email }}</div>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.address">
            <div class="contact-icon"><i class="el-icon-location"></i></div>
            <div class="contact-text">{{ resume.modules.basic.address }}</div>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.age">
            <div class="contact-icon"><i class="el-icon-user"></i></div>
            <div class="contact-text">{{ resume.modules.basic.age }}岁</div>
          </div>
        </div>
        <div class="avatar-container">
          <img v-if="resume.modules.basic?.avatar" :src="resume.modules.basic.avatar" alt="头像" class="avatar" />
          <div v-else class="avatar-placeholder"></div>
        </div>
      </div>

      <!-- 教育背景 -->
      <div v-if="hasEducation" class="section">
        <div class="section-header">
          <div class="section-icon"><i class="el-icon-school"></i></div>
          <h2>教育背景</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="education-header">
              <div class="education-school">{{ edu.school }}</div>
              <div class="education-date">{{ formatDate(edu.startDate) }} - {{ formatDate(edu.endDate) }}</div>
            </div>
            <div class="item-major">{{ edu.major }} · {{ edu.degree }}</div>
            <div class="item-description">
              <MdPreview :modelValue="edu.courses" />
            </div>
          </div>
        </div>
      </div>

      <!-- 实习经历 -->
      <div v-if="hasWork" class="section">
        <div class="section-header">
          <div class="section-icon"><i class="el-icon-suitcase"></i></div>
          <h2>实习经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="item-date">{{ work.startDate || formatDate(work.time?.[0]) }} - {{ work.endDate || formatDate(work.time?.[1]) }}</div>
            <div class="item-company">{{ work.company }}</div>
            <div class="item-position">{{ work.position }}</div>
            <div class="item-description">
              <MdPreview :modelValue="work.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 项目经历 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <div class="section-icon"><i class="el-icon-folder"></i></div>
          <h2>项目经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-name">{{ project.name }}</div>
              <div class="project-date">{{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}</div>
            </div>
            <div class="project-role">{{ project.role }}</div>
            <div class="item-tech" v-if="project.technologies">技术栈：{{ project.technologies }}</div>
            <div class="item-description">
              <MdPreview :modelValue="project.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 专业技能 -->
      <div v-if="hasSkills" class="section">
        <div class="section-header">
          <div class="section-icon"><i class="el-icon-medal"></i></div>
          <h2>专业技能</h2>
        </div>
        <div class="section-content">
          <ul class="skills-list">
            <li v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
              <div class="skill-icon"><i class="el-icon-check"></i></div>
              <div class="skill-content">
                <MdPreview :modelValue="skill.description" />
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 个人总结 -->
      <div v-if="hasEvaluation" class="section">
        <div class="section-header">
          <div class="section-icon"><i class="el-icon-user"></i></div>
          <h2>个人总结</h2>
        </div>
        <div class="section-content">
          <div class="evaluation-content">
            <MdPreview :modelValue="resume.modules.evaluation" />
          </div>
        </div>
      </div>

      <!-- 项目经验 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-name">{{ project.name }}</div>
              <div class="project-date">{{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}</div>
            </div>
            <div class="project-role">{{ project.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="project.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-name">{{ practice.name }}</div>
              <div class="project-date">{{ practice.startDate }} - {{ practice.endDate || '至今' }}</div>
            </div>
            <div class="project-role">{{ practice.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="practice.description" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  // Extract year and month from Chinese date format (e.g., "2021年9月22日")
  const year = date.match(/(\d{4})年/)?.[1];
  const month = date.match(/(\d{1,2})月/)?.[1];
  if (!year || !month) return '';
  // Pad month with leading zero if needed
  const paddedMonth = month.padStart(2, '0');
  return `${year}.${paddedMonth}`;
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasEvaluation = computed(() => props.resume.modules && props.resume.modules.evaluation && typeof props.resume.modules.evaluation === 'string' && props.resume.modules.evaluation.trim() !== '');
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化列表项
const formatListItems = (text) => {
  if (!text) return [];
  return text.split('\n')
      .filter(item => item.trim() !== '')
      .map(item => item.trim().replace(/^[•·-]\s*/, ''));
};

// 格式化段落
const formatParagraphs = (text) => {
  if (!text) return [];
  return text.split('\n').filter(p => p.trim() !== '');
};

// 格式化文本内容，支持Markdown格式
const formatContent = (content) => {
  if (!content || typeof content === 'object') return '';

  // 如果内容已经包含HTML标签，说明是从富文本编辑器来的，直接返回
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  return markdownToHtml(content);
}

// Markdown转HTML的简单实现
const markdownToHtml = (markdown) => {
  if (!markdown) return '';
  return markdown
      // 处理加粗
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // 处理斜体
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 处理标题
      .replace(/^### (.*?)$/gm, '<h3>$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2>$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1>$1</h1>')
      // 处理列表
      .replace(/^\- (.*?)$/gm, '<li>$1</li>')
      .replace(/(<\/li>\n<li>)/g, '</li><li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')
      // 处理段落和换行
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^(.+)$/, '<p>$1</p>');
}
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  color: #333;
  background-color: #fff;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 头部样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.personal-info {
  flex: 1;
}

.name {
  font-size: 28px;
  margin: 0 0 5px 0;
  font-weight: bold;
}

.job-title {
  color: #666;
  font-size: 16px;
}

.contact-section {
  display: flex;
  flex-direction: column;
  margin: 0 50px 0 0;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.contact-icon {
  width: 24px;
  height: 24px;
  background-color: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: white;
  font-size: 12px;
}

.contact-text {
  font-size: 14px;
  color: #666;
}

.avatar-container {
  width: 100px;
  height: 100px;
  margin-left: 100px;
}

.avatar, .avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #3498db;
}

.avatar-placeholder {
  background-color: #f0f0f0;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 25px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.section-icon {
  width: 30px;
  height: 30px;
  background-color: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: white;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #3498db;
}

.section-content {
  padding: 0 5px 0 40px;
}

/* 教育经历样式 */
.education-item {
  margin-bottom: 20px;
  position: relative;
}

.education-item::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #3498db;
}

.item-date {
  font-weight: bold;
  margin-bottom: 5px;
  color: #3498db;
}

.item-school {
  font-weight: bold;
  margin-bottom: 5px;
}

.item-major {
  color: #666;
  margin-bottom: 8px;
}

.item-description ul {
  margin: 0;
  padding-left: 20px;
}

.item-description li {
  margin-bottom: 3px;
}

/* 工作经历样式 */
.work-item {
  margin-bottom: 20px;
  position: relative;
}

.work-item::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #3498db;
}

.item-company {
  font-weight: bold;
  margin-bottom: 5px;
}

.item-position {
  color: #666;
  margin-bottom: 8px;
}

/* 项目经历样式 */
.project-item {
  margin-bottom: 20px;
  position: relative;
}

.project-item::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #3498db;
}

.project-header {
  margin-bottom: 5px;
}

.item-name {
  font-weight: bold;
}

.item-role {
  color: #666;
  font-style: italic;
}

.item-tech {
  color: #666;
  margin-bottom: 8px;
  font-style: italic;
}

.description-title {
  font-weight: bold;
  margin-bottom: 5px;
}

/* 技能样式 */
.skills-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.skill-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.skill-icon {
  width: 20px;
  height: 20px;
  background-color: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: white;
  flex-shrink: 0;
}

.skill-content {
  flex: 1;
}

/* 个人总结样式 */
.evaluation-content {
  line-height: 1.7;
}

.evaluation-paragraph {
  display: flex;
  margin-bottom: 10px;
}

.paragraph-icon {
  margin-right: 10px;
  color: #3498db;
}

.paragraph-content {
  flex: 1;
}

/* 添加 MdPreview 相关样式 */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 20px;
}

:deep(.md-preview-html li) {
  margin: 0;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}
</style> 