<template>
  <div class="template-selector">
    <h3 class="selector-title">选择简历模板</h3>
    <div class="templates-container">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-item"
        :class="{ 'active': selectedTemplateId === template.id }"
        @click="selectTemplate(template.id)"
      >
        <div class="template-preview">
          <img :src="template.thumbnail" :alt="template.name" class="template-image" />
        </div>
        <div class="template-info">
          <div class="template-name">{{ template.name }}</div>
          <div class="template-badges">
            <span class="template-badge" v-for="(feature, index) in template.features" :key="index">
              {{ feature }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  selectedTemplateId: {
    type: Number,
    required: true
  },
  templates: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:selectedTemplateId']);

const selectTemplate = (templateId) => {
  emit('update:selectedTemplateId', templateId);
};
</script>

<style scoped>
.template-selector {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.selector-title {
  font-size: 18px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.templates-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.template-item {
  display: flex;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-item.active {
  border-color: #1e88e5;
  background-color: rgba(30, 136, 229, 0.05);
}

.template-preview {
  width: 100px;
  height: 130px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  flex: 1;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  color: #333;
}

.template-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.template-badge {
  font-size: 12px;
  padding: 2px 8px;
  background-color: #f0f0f0;
  border-radius: 10px;
  color: #666;
}
</style> 