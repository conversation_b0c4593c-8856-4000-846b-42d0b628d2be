package com.bimowu.resume.common.service;

import java.io.InputStream;

/**
 * 资源解析器接口
 * 负责解析和加载PDF生成所需的各种资源
 */
public interface ResourceResolver {
    
    /**
     * 解析图片资源
     * @param imagePath 图片路径
     * @return 图片输入流
     */
    InputStream resolveImage(String imagePath);
    
    /**
     * 解析CSS资源
     * @param cssPath CSS路径
     * @return CSS内容
     */
    String resolveCSS(String cssPath);
    
    /**
     * 解析字体资源
     * @param fontPath 字体路径
     * @return 字体输入流
     */
    InputStream resolveFont(String fontPath);
    
    /**
     * 解析模板资源
     * @param templatePath 模板路径
     * @return 模板内容
     */
    String resolveTemplate(String templatePath);
    
    /**
     * 检查资源是否存在
     * @param resourcePath 资源路径
     * @return 是否存在
     */
    boolean resourceExists(String resourcePath);
    
    /**
     * 获取资源的绝对路径
     * @param relativePath 相对路径
     * @return 绝对路径
     */
    String getAbsolutePath(String relativePath);
    
    /**
     * 验证资源路径安全性
     * @param resourcePath 资源路径
     * @return 是否安全
     */
    boolean isPathSafe(String resourcePath);
}