package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 技能段落表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume_skill_segment")
public class ResumeSkillSegment implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "seg_id", type = IdType.AUTO)
    private Long segId;

    /**
     * 技能ID
     */
    private Long skillId;

    /**
     * 技能点名称
     */
    private String segName;

    /**
     * 熟练度（一般、良好、熟练、擅长、精通）
     */
    private String proficiency;

    /**
     * 段落文本
     */
    private String text;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
