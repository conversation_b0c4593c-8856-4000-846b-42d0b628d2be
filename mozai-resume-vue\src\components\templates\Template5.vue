<template>
  <div class="resume-template template-5">
    <div class="resume-container">

      <!-- 头部信息区域 -->
      <div class="header">
        <div class="header-info">
          <div class="name">{{ resume.modules.basic?.name || '未填写姓名' }}</div>
          <div class="contact-info">
            <span class="info-item" v-if="resume.modules.basic?.phone"><i class="icon">📞</i> {{ resume.modules.basic.phone }}</span>
            <span class="info-item" v-if="resume.modules.basic?.email"><i class="icon">📧</i> {{ resume.modules.basic.email }}</span>
            <span class="info-item" v-if="resume.modules.basic?.address"><i class="icon">🏠</i> {{ resume.modules.basic.address }}</span>
            <span class="info-item" v-if="resume.modules.basic?.age"><i class="icon">🎂</i> 年龄：{{ resume.modules.basic.age }}岁</span>
          </div>
        </div>
        <div class="avatar-container">
          <img v-if="resume.modules.basic?.avatar" :src="resume.modules.basic.avatar" alt="头像" class="avatar" />
        </div>
      </div>

      <!-- 教育背景 -->
      <div v-if="hasEducation" class="section education-section">
        <div class="section-title">
          <h2>教育背景 / EDUCATIONAL EXPERIENCE</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="item-header">
              <div class="item-header-left">
                <span class="edu-school">{{ edu.school }}（{{ edu.degree }}）</span>
                <span class="edu-major">{{ edu.major }}</span>
                <span class="item-time">{{ formatDate(edu.time[0]) }} - {{ formatDate(edu.time[1]) || '至今' }}</span>
              </div>

            </div>
            <div class="edu-courses" v-if="edu.courses">
              主修课程：
              <div v-html="formatContent(edu.courses)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实习经历 -->
      <div v-if="hasWork" class="section work-section">
        <div class="section-title">
          <h2>实习经历 / INTERNSHIP EXPERIENCE</h2>
        </div>
        <div class="section-content work-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="item-header">
              <div class="item-header-left">
                <span class="work-time">{{ formatDate(work.time[0]) }} - {{ formatDate(work.time[1]) || '至今' }}</span>
                <span class="work-company">{{ work.company }}</span>
                <span class="work-position">{{ work.position }}</span>
              </div>
            </div>
            <div class="work-description" v-if="work.description">
              工作职责：
              <div v-html="formatContent(work.description)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目经历 -->
      <div v-if="hasProjects" class="section projects-section">
        <div class="section-title">
          <h2>项目经历 / PROJECT EXPERIENCE</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="item-header">
              <div class="item-header-left">
                <span class="project-time">{{ formatDate(project.time[0]) }} - {{ formatDate(project.time[1]) || '至今' }}
                </span>
                <span class="project-role" v-if="project.role">{{ project.role }}</span>
                <span class="project-title">{{ project.name }}</span>
              </div>

            </div>
            <div class="project-description" v-if="project.description">
              项目描述：
              <div v-html="formatContent(project.description)"></div>
            </div>

          </div>
        </div>
      </div>

      <!-- 练手项目 (Assuming practices data exists and you want to display it like projects) -->
      <div v-if="hasPractices" class="section practices-section">
        <div class="section-title">
          <h2>练手项目 / PRACTICE PROJECT</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="item-header">
              <div class="item-header-left">
                <span class="project-time">{{ formatDate(practice.time[0]) }} - {{ formatDate(practice.time[1]) || '至今' }}</span>
                <span class="project-role" v-if="practice.role">{{ practice.role }}</span>
                <span class="project-title">{{ practice.name }}</span>
              </div>

            </div>

            <div class="project-description" v-if="practice.description">
              项目描述：
              <div v-html="formatContent(practice.description)"></div>
            </div>

          </div>
        </div>
      </div>


      <!-- 专业技能 -->
      <div v-if="hasSkills" class="section skills-section">
        <div class="section-title">
          <h2>专业技能 / PROFESSIONAL SKILLS</h2>
        </div>
        <div class="section-content">
          <!-- Iterate over skills array, expecting objects with description field -->
          <div v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item-description">
            <div class="skill-description-body" v-html="formatContent(skill.description)"></div>
          </div>
        </div>
      </div>

      <!-- 证书奖项 -->
      <div v-if="hasCertificates" class="section certificates-section">
        <div class="section-title">
          <h2>证书奖项 / CERTIFICATES AND AWARDS</h2>
        </div>
        <div class="section-content">
          <div v-if="typeof resume.modules.certificates === 'string'" v-html="formatContent(resume.modules.certificates)"></div>
          <div v-else-if="typeof resume.modules.certificates === 'object'" v-html="formatContent(resume.modules.certificates.certificateName || '')"></div>
        </div>
      </div>

      <!-- 兴趣爱好 -->
      <div v-if="hasInterests" class="section interests-section">
        <div class="section-title">
          <h2>兴趣爱好 / INTERESTS</h2>
        </div>
        <div class="section-content">
          <div v-if="typeof resume.modules.interests === 'string'" v-html="formatContent(resume.modules.interests)"></div>
          <div v-else-if="typeof resume.modules.interests === 'object'" v-html="formatContent(resume.modules.interests.description || '')"></div>
        </div>
      </div>

      <!-- 个人总结/自我评价 -->
      <div v-if="hasEvaluation" class="section evaluation-section">
        <div class="section-title">
          <h2>个人总结 / SELF EVALUATION</h2>
        </div>
        <div class="section-content">
          <!-- Handle both string and object formats for selfEvaluation -->
          <div v-if="typeof resume.modules.selfEvaluation === 'string'" v-html="formatContent(resume.modules.selfEvaluation)"></div>
          <div v-else-if="typeof resume.modules.selfEvaluation === 'object'" v-html="formatContent(resume.modules.selfEvaluation.description || '')"></div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期为 YYYY.MM
const formatDate = (date) => {
  if (!date) return '';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      // If not a valid Date object, try to parse as YYYY-MM-DD or YYYY/MM/DD etc.
      const match = date.match(/^(\d{4})[-\.\/年]?(\d{1,2})/);
      if (match && match[1] && match[2]) {
        return `${match[1]}.${match[2].padStart(2, '0')}`; // Return YYYY.MM from string
      }
      return date; // Return original if parsing fails
    }
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    return `${year}.${month}`;
  } catch (e) {
    console.error('Error formatting date:', e);
    return date; // Return original string in case of error
  }
};

// 判断各模块是否有内容 (Adjusting based on potential data structure)
const hasEducation = computed(() => props.resume.modules?.education?.length > 0);
const hasWork = computed(() => props.resume.modules?.work?.length > 0);
const hasProjects = computed(() => props.resume.modules?.projects?.length > 0);
const hasSkills = computed(() => {
  const skills = props.resume.modules?.skills;
  if (!skills || !Array.isArray(skills)) return false;

  // Check if the array contains at least one object with a non-empty description
  return skills.some(skill => skill && typeof skill === 'object' && skill.description && skill.description.trim() !== '');
});
const hasCertificates = computed(() => {
  if (!props.resume.modules?.certificates) return false;
  if (typeof props.resume.modules.certificates === 'string') {
    return props.resume.modules.certificates.trim() !== '';
  }
  return typeof props.resume.modules.certificates === 'object' &&
      props.resume.modules.certificates.certificateName &&
      props.resume.modules.certificates.certificateName.trim() !== '';
});
const hasEvaluation = computed(() => {
  if (!props.resume.modules?.selfEvaluation) return false;
  const evaluation = props.resume.modules.selfEvaluation;
  return typeof evaluation === 'string' ? evaluation.trim() !== '' :
      (typeof evaluation === 'object' && evaluation.description && evaluation.description.trim() !== '');
});
const hasPractices = computed(() => props.resume.modules?.practices?.length > 0);
const hasInterests = computed(() => {
  if (!props.resume.modules?.interests) return false;
  if (typeof props.resume.modules.interests === 'string') {
    return props.resume.modules.interests.trim() !== '';
  }
  return typeof props.resume.modules.interests === 'object' &&
      props.resume.modules.interests.description &&
      props.resume.modules.interests.description.trim() !== '';
});

// Simplified formatContent function for Markdown bold/italic and converting all newlines to <br/>
const formatContent = (content) => {
  if (!content || typeof content !== 'string') {
    return '';
  }

  let html = content;

  // Handle bold and italic Markdown first
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Convert all newlines to HTML line breaks
  html = html.replace(/\n/g, '<br/>');

  return html;
};

</script>

<style scoped>
/* Reset some default styles */
ul,
li,
p,
div,
span, h1, h2 {
  margin: 0;
  padding: 0;
  box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif; /* Match font */
  color: #333; /* Dark gray text color */
  background-color: #fff; /* White background */
  line-height: 1.7; /* Standard line height */
}

.resume-container {
  max-width: 210mm; /* A4 width */
  margin: 0 auto; /* Center the content */
  padding: 20px 30px; /* Overall padding around content (top/bottom 20, left/right 30 as in image) */
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px; /* Padding inside header */
  border-bottom: 1px solid #000; /* Black border below header as in image */
  margin-bottom: 20px; /* Space below header */
}

.header-info {
  flex-grow: 1;
  margin-right: 20px; /* Space between text and avatar */
}

.name {
  font-size: 28px; /* Name font size */
  font-weight: bold;
  color: #000; /* Black color for name */
  margin-bottom: 5px; /* Space below name */
}

.contact-info {
  font-size: 14px; /* Contact info font size */
  color: #333; /* Darker color for contact as in image */
  display: flex;
  flex-wrap: wrap; /* Allow wrapping */
  gap: 15px; /* Space between info items */
}

.info-item {
  display: flex;
  align-items: center;
}

/* Basic icons - placehoder */
.icon {
  margin-right: 5px;
  color: #666; /* Icon color */
  font-size: 13px; /* Adjust icon size */
  /* Using simple text icons as placeholders */
  font-style: bold; /* Reset default icon font style */
}

.avatar-container {
  width: 80px; /* Adjusted Avatar width */
  height: 100px; /* Adjusted Avatar height */
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid #ccc; /* Border around avatar */
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Section Title Styles */
.section-title {
  margin-top: 20px; /* Space above section */
  margin-bottom: 12px; /* Space below title */
}

.section-title h2 {
  font-size: 18px; /* Title font size */
  color: #000; /* Title color */
  margin: 0;
  font-weight: bold;
  display: inline-block; /* Make block only as wide as content */
  border-bottom: 2px solid #c0392b; /* Red underline */
  padding-bottom: 3px; /* Space between text and underline */
}

/* Section Content Styles */
.section-content {
  margin-top: 8px; /* Space below title */
  font-size: 14px; /* Content font size */
  color: #333; /* Dark gray text color */
  line-height: 1.7; /* Content line height */
}

/* Styles for individual items within sections (education, work, project) */
.education-item,
.work-item,
.project-item,
.skill-item /* Apply item spacing to skill items as well */{
  margin-bottom: 15px; /* Space between items */
}

.education-item:last-child,
.work-item:last-child,
.project-item:last-child,
.skill-item:last-child{
  margin-bottom: 0;
}


.item-header {
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 5px;
  font-size: 15px;
  font-weight: bold;
}

.item-header-left {
  display: flex;
  justify-content: space-between;}

.item-time {
  flex-shrink: 0;
  font-weight: bold;
  font-size: 14px;
}

/* Education Specific Styles */
.edu-school {
  color: #000;
  margin-right: 5px;
}

.edu-degree {
  font-weight: bold;
  margin-right: 10px;
}

.edu-major {
  color: #333;
  font-size: 14px; /* Match content font size */
  font-weight: bold;
}

.edu-courses {
  margin-top: 5px;
  /* Styles for the HTML content rendered by v-html */
}

/* Work Specific Styles */
.work-company {
  color: #000;
  margin-right: 10px; /* Space between company and position */
}

.work-position {
  color: #333;
  font-size: 14px;
  font-weight: bold;
}

/* Project Specific Styles */
.project-title {
  color: #000;
  margin-right: 10px; /* Space between title and role */
}

.project-tech {
  color: #555; /* Match time color */
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: bold; /* Not bold */
}

/* Styles for v-html rendered content within sections */
.edu-courses > div,
.work-description > div,
.project-description > div,
.project-achievements > div,
.skills-content > div,
.evaluation-content > div,
.certificate-content > div {
  /* Add specific styles for the rendered HTML if needed, e.g., padding-left for lists */
}

/* Common styles for v-html rendered lists */
:deep(ul) {
  list-style-type: disc; /* Black solid circle */
  padding-left: 20px; /* Indent list */
  margin: 0;
}

:deep(li) {
  margin-bottom: 5px; /* Space between list items */
  line-height: 1.7; /* Match content line height */
}

/* Common styles for v-html rendered paragraphs */
:deep(p) {
  margin: 0 0 5px 0; /* Space between paragraphs */
  line-height: 1.7; /* Match content line height */
}

:deep(strong) {
  font-weight: bold;
}

:deep(em) {
  font-style: italic;
}

/* Adjustments for specific list/paragraph rendering */
/* Example: if the markdown has headers, you might need to style them */
:deep(h1), :deep(h2), :deep(h3) {
  margin-top: 10px; /* Space above headers in markdown */
  margin-bottom: 5px; /* Space below headers in markdown */
  font-weight: bold; /* Headers are bold */
}

:deep(a) {
  color: #4472c4; /* Link color */
  text-decoration: underline; /* Underline links */
}

/* New styles added in the style section */
.skill-description-item {
  margin-bottom: 10px;
}

.skill-description-body {
  line-height: 1.7;
  text-align: justify;
}

.certificates-section,
.interests-section {
  margin-top: 20px;
}

.section-content ul {
  padding-left: 20px;
  margin: 0;
}

.section-content li {
  margin-bottom: 5px;
  line-height: 1.7;
}

.section-content p {
  margin: 0 0 5px 0;
  line-height: 1.7;
}

/* Ensure strong tag has bold font weight */
:deep(strong) {
  font-weight: bold;
}

/* Add minimal styles for the new skill content containers if needed */
.skill-item-string,
.skill-block-string {
  margin-bottom: 5px; /* Add some spacing below each item or the block */
  line-height: 1.7; /* Ensure readable line height */
}

/* Add styles for the skill description items if needed */
.skill-item-description {
  margin-bottom: 10px; /* Spacing between skill items */
  line-height: 1.7; /* Readable line height */
}

.skill-description-body {
  /* Any specific styles for the description text */
}

/* Ensure specific styles for evaluation content if needed */
.evaluation-content {
  line-height: 1.7; /* Match content line height */
  text-align: justify; /* Align text as in other sections */
}

/* Ensure strong tag has bold font weight */
:deep(strong) {
  font-weight: bold;
}

</style> 