<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            color: #333;
            background-color: #fff;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* 头部样式 */
        .header-section {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .name {
            font-size: 28px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        
        .contact-item i {
            margin-right: 5px;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 0;
        }
        
        .section:not(:last-child) {
            margin-bottom: 8px;
        }
        
        .section-header {
            margin-bottom: 15px;
            border-bottom: none;
            position: relative;
        }
        
        .section-header h2 {
            display: inline-block;
            margin: 0;
            padding: 5px 15px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            background-color: #e74c3c;
            border-radius: 0;
        }
        
        .section-header h2::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 2px;
            background-color: #e74c3c;
        }
        
        .section-content {
            padding: 0 5px 0 5px;
            min-height: 0;
        }
        
        /* 教育经历样式 */
        .education-item {
            display: flex;
            margin-bottom: 8px;
        }
        
        .item-date {
            width: 120px;
            font-weight: bold;
            margin-right: 20px;
        }
        
        .item-content {
            flex: 1;
        }
        
        .item-school {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .item-major {
            color: #666;
            margin-bottom: 5px;
        }
        
        .item-description ul {
            margin: 5px 0 0 0;
            padding-left: 20px;
        }
        
        .item-description li {
            margin-bottom: 3px;
        }
        
        /* 技能样式 */
        .skills-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .skill-item {
            margin-bottom: 8px;
            display: flex;
            flex-wrap: wrap;
        }
        
        .skill-name {
            font-weight: bold;
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .skill-description {
            color: #666;
        }
        
        /* 项目经验样式 */
        .project-item, .work-item, .internship-item, .practice-item {
            margin-bottom: 10px;
        }
        
        .project-header, .work-header, .internship-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
        }
        
        .project-title, .work-title, .internship-title {
            display: flex;
            flex-direction: column;
        }
        
        .project-name, .work-company, .internship-company {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .project-role, .work-position, .internship-position {
            color: #666;
            font-style: italic;
        }
        
        .project-date, .work-date, .internship-date {
            color: #666;
            white-space: nowrap;
        }
        
        .project-tech {
            margin-bottom: 8px;
            color: #666;
            font-style: italic;
        }
        
        .description-content {
            margin-top: 5px;
        }
        
        .description-item {
            margin-bottom: 5px;
            line-height: 1.7;
        }
        
        /* 自我评价样式 */
        .evaluation-content {
            line-height: 1.7;
        }
        
        .evaluation-paragraph {
            margin-bottom: 8px;
        }
        
        /* 默认元素样式 */
        ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 3px;
        }
        
        p {
            margin: 0 0 5px 0;
        }
        
        h1, h2, h3 {
            margin: 10px 0 5px 0;
        }
        
        strong {
            font-weight: bold;
        }
        
        em {
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="resume-template template-7">
        <div class="resume-container">
            <!-- 个人信息部分 -->
            <div class="header-section">
                <h1 class="name">${name}</h1>
                <div class="contact-info">
                    <div class="contact-item">
                        <i>👤</i>
                        <span>${age}岁</span>
                    </div>
                    <div class="contact-item">
                        <i>📞</i>
                        <span>${phone}</span>
                    </div>
                    <div class="contact-item">
                        <i>📧</i>
                        <span>${email}</span>
                    </div>
                    <div class="contact-item">
                        <i>📍</i>
                        <span>${hometown}</span>
                    </div>
                </div>
                <div class="job-objective">
                    <i>🎯</i>
                    <span>${jobObjective}</span>
                </div>
            </div>

            <!-- 教育背景 -->
            ${education}

            <!-- 专业技能 -->
            ${skills}

            <!-- 项目经验 -->
            ${projects}

            <!-- 练手项目 -->
            ${practices}

            <!-- 工作经验 -->
            ${work}

            <!-- 自我评价 -->
            ${selfEvaluation}
        </div>
    </div>
</body>
</html> 