<template>
  <div class="main-layout">
    <Header />
    <main class="main-content">
      <slot></slot>
    </main>
  </div>
</template>

<script setup>
import Header from '@/components/Header.vue'
import { useUserStore } from '@/stores/user'
import { onMounted } from 'vue'

const userStore = useUserStore()

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.token && !userStore.userInfo.id) {
    await userStore.fetchUserInfo()
  }
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 65px; /* 头部高度 */
}
</style> 