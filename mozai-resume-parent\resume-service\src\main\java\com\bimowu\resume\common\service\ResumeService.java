package com.bimowu.resume.common.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.entity.Resume;

import java.util.List;

/**
 * <p>
 * 简历主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeService extends IService<Resume> {

    List<ResumeFullSaveDto> getFullResumeListByUserId(Long userId);

    ResumeFullSaveDto getResumeDetail(Long resumeId, Long userId);
}
