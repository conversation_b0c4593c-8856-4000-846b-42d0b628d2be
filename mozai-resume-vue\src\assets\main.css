:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --text-color: #333;
  --light-text: #666;
  --lighter-text: #999;
  --border-color: #eaeaea;
  --background-color: #f9f9f9;
  --white: #ffffff;
  --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: #27ae60;
}

.section {
  padding: 60px 0;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
}

.section-subtitle {
  font-size: 16px;
  color: var(--light-text);
  text-align: center;
  margin-bottom: 40px;
}

.card {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: 20px;
  transition: all 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  
  .section {
    padding: 40px 0;
  }
  
  .section-title {
    font-size: 24px;
  }
} 