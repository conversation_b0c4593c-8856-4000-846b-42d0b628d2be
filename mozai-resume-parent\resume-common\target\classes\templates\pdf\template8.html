<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            color: #333;
            background-color: #fff;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* 头部样式 */
        .header-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0px;
        }
        
        .personal-info {
            flex: 1;
        }
        
        .name {
            font-size: 24px;
            margin: 0 0 15px 0;
            font-weight: bold;
        }
        
        .basic-info {
            display: block;
            flex-wrap: wrap;
        }
        
        .info-item {
            font-size: 20px;
            margin-right: 20px;
            margin-bottom: 8px;
        }
        
        .label {
            font-weight: bold;
        }
        
        .avatar-container {
            width: 120px;
            height: 160px;
            margin-left: 20px;
        }
        
        .avatar, .avatar-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 1px solid #ccc;
        }
        
        .avatar-placeholder {
            background-color: #f0f0f0;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 25px;
        }
        
        .section-header {
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .section-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: #3498db;
        }
        
        .section-content {
            padding: 0 5px;
        }
        
        /* 两栏布局样式 */
        .education-item, .work-item, .project-item, .internship-item {
            display: flex;
            margin-bottom: 15px;
            position: relative;
        }
        
        .item-left {
            position: absolute;
            top: 0;
            right: 0;
            font-weight: bold;
            color: #666;
        }
        
        .item-right {
            flex: 1;
            padding-right: 120px;
        }
        
        .item-title {
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .item-subtitle {
            color: #666;
            margin-bottom: 5px;
        }
        
        .item-tech {
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }
        
        .item-description {
            margin-top: 5px;
        }
        
        .description-line {
            margin-bottom: 5px;
            position: relative;
            padding-left: 15px;
        }
        
        .description-line::before {
            content: "•";
            position: absolute;
            left: 0;
        }
        
        /* 技能样式 */
        .skills-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .skill-item {
            margin-bottom: 8px;
            position: relative;
            padding-left: 15px;
        }
        
        .skill-item::before {
            content: "•";
            position: absolute;
            left: 0;
        }
        
        /* 自我评价样式 */
        .evaluation-content {
            line-height: 1.7;
        }
        
        .evaluation-paragraph {
            margin-bottom: 8px;
            text-indent: 2em;
        }
        
        /* 项目经验样式 */
        .project-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .project-title {
            font-weight: bold;
        }
        
        .project-date {
            color: #666;
        }
        
        .project-role {
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }
        
        .project-description {
            margin-top: 5px;
        }
        
        /* 其他元素样式 */
        ul {
            margin: 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        p {
            margin: 0 0 5px 0;
        }
        
        strong {
            font-weight: bold;
        }
        
        em {
            font-style: italic;
        }
        
        h1 {
            font-size: 1.5em;
            margin: 0.5em 0;
        }
        
        h2 {
            font-size: 1.3em;
            margin: 0.5em 0;
        }
        
        h3 {
            font-size: 1.1em;
            margin: 0.5em 0;
        }
    </style>
</head>
<body>
    <div class="resume-template template-8">
        <div class="resume-container">
            <!-- 个人信息部分 -->
            <div class="header-section">
                <div class="personal-info">
                    <h1 class="name">${name}</h1>
                    <div class="basic-info">
                        <div class="info-item">
                            <span class="label">年龄：</span>
                            <span>${age}岁</span>
                        </div>
                        <div class="info-item">
                            <span class="label">联系电话：</span>
                            <span>${phone}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">电子邮箱：</span>
                            <span>${email}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">所在地：</span>
                            <span>${hometown}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">求职意向：</span>
                            <span>${jobObjective}</span>
                        </div>
                    </div>
                </div>
                <div class="avatar-container">
                    ${avatar}
                </div>
            </div>

            <!-- 教育背景 -->
            ${education}

            <!-- 专业技能 -->
            ${skills}

            <!-- 工作经历 -->
            ${work}

            <!-- 项目经验 -->
            ${projects}

            <!-- 练手项目 -->
            ${practices}

            <!-- 自我评价 -->
            ${selfEvaluation}
        </div>
    </div>
</body>
</html> 