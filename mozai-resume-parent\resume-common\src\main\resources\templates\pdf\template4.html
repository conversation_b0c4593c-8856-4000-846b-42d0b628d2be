<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: rgb(68, 84, 106);
            line-height: 1.7;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            background-color: #fff;
            line-height: 1.7;
            color: rgb(68, 84, 106);
            padding: 0 !important;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
            border: 15px solid rgb(68, 84, 106);
        }
        
        .info-name {
            font-weight: bold;
            font-size: 30px;
            padding-bottom: 20px;
        }
        
        /* 头部个人信息样式 */
        .header-section {
            display: flex;
            justify-content: space-between;
            padding-bottom: 15px;
        }
        
        .personal-info {
            flex: 1;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .info-item {
            flex: 1;
            min-width: 0;
        }
        
        .info-item.full-width {
            flex: 2;
        }
        
        .label {
            font-weight: bold;
        }
        
        .avatar-container {
            width: 120px;
            height: 150px;
            margin: 0 auto;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 1px solid #ccc;
        }
        
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 5px;
            padding: 0 !important;
        }
        
        .section-header {
            margin-bottom: 5px;
            position: relative;
            display: inline-block;
            padding: 0 5px;
        }
        
        .section-header::before,
        .section-header::after {
            content: '';
            position: absolute;
            height: 1px;
            background-color: #333;
            width: calc(100% + 10px);
            left: -5px;
        }
        
        .section-header::before {
            bottom: -2px;
        }
        
        .section-header::after {
            bottom: -5px;
        }
        
        .section-header h2 {
            display: inline-block;
            margin: 0;
            padding: 5px 15px;
            font-size: 25px;
            font-weight: bold;
            background-color: #f0f0f0;
            border-radius: 5px 5px 0 0;
        }
        
        .section-content {
            padding: 5px 0;
        }
        
        /* 自我评价样式 */
        .evaluation-content {
            padding: 5px;
        }
        
        .evaluation-tags {
            margin-bottom: 10px;
        }
        
        .tag {
            margin-bottom: 5px;
        }
        
        .evaluation-text {
            line-height: 1.7;
        }
        
        /* 工作经验样式 */
        .experience-item {
            margin-bottom: 20px;
        }
        
        .experience-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .education-date {
            flex: 1;
            font-weight: bold;
            font-size: 17px;
            margin-bottom: 10px;
            text-align: right;
        }
        
        .experience-date {
            margin-right: 40px;
            font-weight: bold;
            color: black;
            font-size: 17px;
            flex: 1;
        }
        
        .experience-company {
            margin-right: 15px;
            font-weight: bold;
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 18px;
        }
        
        .experience-company::after {
            content: " | ";
            color: inherit;
        }
        
        .experience-position {
            white-space: nowrap;
            font-size: 18px;
        }
        
        .experience-description {
            line-height: 1.7;
        }
        
        /* 项目经验样式 */
        .project-item {
            margin-bottom: 12px;
        }
        
        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .project-title {
            font-weight: bold;
            font-size: 17px;
            flex: 1;
        }
        
        .project-date {
            font-weight: bold;
            color: black;
            font-size: 17px;
            flex: 1;
            text-align: right;
        }
        
        .project-role {
            margin-right: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .project-description {
            padding-left: 10px;
            line-height: 1.7;
        }
        
        /* 描述内容样式 */
        .description-content {
            line-height: 1.7;
        }
        
        .description-tag {
            margin-bottom: 5px;
        }
        
        .tag-title {
            font-weight: bold;
        }
        
        .description-bullet {
            padding-left: 20px;
            position: relative;
            margin-bottom: 5px;
        }
        
        .description-bullet::before {
            content: "•";
            position: absolute;
            left: 5px;
        }
        
        .description-paragraph {
            margin-bottom: 5px;
            text-indent: 2em;
        }
        
        /* 技能部分样式 */
        .skills-content {
            padding: 10px;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
        }
        
        .skill-item {
            width: 100%;
            margin-bottom: 10px;
            align-items: center;
        }
        
        .skill-name {
            font-weight: bold;
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .skill-description {
            flex: 1;
            line-height: 1.7;
        }
        
        /* 教育背景样式 */
        .education-item {
            margin-bottom: 5px;
        }
        
        .education-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .education-school {
            width: 200px;
            flex: 1;
            font-weight: bold;
            margin-right: 20px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .education-major {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .education-details {
            margin-top: 5px;
        }
        
        .education-courses {
            line-height: 1.7;
        }
        
        .education-courses p,
        .education-courses li {
            margin-bottom: 5px;
        }
        
        .certificate-content {
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="resume-template template-4">
        <div class="resume-container">
            <!-- 个人信息部分 -->
            <div class="header-section">
                <div class="personal-info">
                    <div class="info-name">${name}</div>
                    <div class="info-row">
                        <div class="info-item"><span class="label">性别：</span>${gender}</div>
                        <div class="info-item"><span class="label">年龄：</span>${age}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-item"><span class="label">电话：</span>${phone}</div>
                        <div class="info-item"><span class="label">邮箱：</span>${email}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-item full-width"><span class="label">求职意向：</span>${jobObjective}</div>
                    </div>
                </div>
                <div class="avatar-container">
                    ${avatar}
                </div>
            </div>

            <!-- 自我评价部分 -->
            ${selfEvaluation}

            <!-- 工作经验部分 -->
            ${work}

            <!-- 项目经验部分 -->
            ${projects}

            <!-- 技能特长 -->
            ${skills}

            <!-- 教育背景部分 -->
            ${education}

            <!-- 证书奖项部分 -->
            ${certificates}
            
            <!-- 校园经历 -->
            ${campus}
            
            <!-- 兴趣爱好 -->
            ${interests}
        </div>
    </div>
</body>
</html> 