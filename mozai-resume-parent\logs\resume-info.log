18:29:39.252 [main] INFO  com.bimowu.ResumeWebApplication - Starting ResumeWebApplication on su with PID 22396 (C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent\resume-web\target\classes started by 14629 in C:\Users\<USER>\IdeaProjects\sso\resume\mozai-resume-parent)
18:29:39.270 [main] INFO  com.bimowu.ResumeWebApplication - The following profiles are active: test
18:29:40.276 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7094"]
18:29:40.284 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
18:29:40.284 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.16]
18:29:40.290 [main] INFO  org.apache.catalina.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files (x86)\Java\jdk-1.8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Aibote;D:\Aibote (1)\Aibote;D:\Aibote (1)\Aibote\node_modules\.bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;D:\program files (x86)\Git\cmd;D:\program files (x86)\Docker\resources\bin;D:\program files (x86)\;d:\program files (x86)\cursor\resources\app\bin;C:\Program Files\CursorModifier;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\program files (x86)\cursor\resources\app\bin;D:\gradle-7.3.3-bin\gradle-7.3.3\bin;D:\apache-maven-3.2.3-bin\apache-maven-3.2.3-bin\apache-maven-3.2.3\bin;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\program files (x86)\IntelliJ IDEA 2025.1\bin;;D:\program files (x86)\Kiro\bin;D:\program files (x86)\Microsoft VS Code\bin;.]
18:29:40.413 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/resume] - Initializing Spring embedded WebApplicationContext
18:29:40.574 [main] INFO  com.bimowu.resume.config.WebConfig - 初始化CORS过滤器
18:29:40.575 [main] INFO  com.bimowu.resume.config.WebConfig - CORS配置完成: org.springframework.web.cors.CorsConfiguration@6d2016
18:29:40.621 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 初始化字体管理器...【修复版本】
18:29:40.621 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 初始化默认字体族映射
18:29:40.622 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 当前内存使用率: {:.1f}%，继续加载字体: 94.30728488498264
18:29:40.715 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 成功加载并缓存字体: SimSun (SimSun) - 大小: 24536KB
18:29:40.716 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 当前内存使用率: {:.1f}%，继续加载字体: 80.42566472833806
18:29:40.781 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 成功加载并缓存字体: Microsoft YaHei (Microsoft YaHei) - 大小: 17333KB
18:29:40.782 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 当前内存使用率: {:.1f}%，继续加载字体: 61.239278774068815
18:29:40.809 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 成功加载并缓存字体: SimHei (SimHei) - 大小: 17333KB
18:29:40.809 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 当前内存使用率: {:.1f}%，继续加载字体: 81.04632753314394
18:29:40.872 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 成功加载并缓存字体: Arial (Arial) - 大小: 24536KB
18:29:40.872 [main] INFO  com.bimowu.resume.common.service.impl.FontManagerImpl - 字体管理器初始化完成，共加载 4 个字体
18:29:42.066 [main] INFO  com.bimowu.resume.common.service.impl.AutoStyleUpdateServiceImpl - 启动自动样式更新服务，更新间隔: 3600 秒
18:29:42.099 [main] INFO  com.bimowu.resume.common.service.impl.ResourceCacheServiceImpl - 资源缓存服务初始化完成
18:29:42.258 [main] INFO  com.bimowu.resume.config.WebConfig - 添加CORS映射
18:29:42.259 [main] INFO  com.bimowu.resume.config.WebConfig - CORS映射添加完成
18:29:42.480 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7094"]
18:29:42.486 [main] ERROR org.apache.catalina.util.LifecycleBase - Failed to start component [Connector[HTTP/1.1-7094]]
org.apache.catalina.LifecycleException: Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1008) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:226) [tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.addPreviouslyRemovedConnectors(TomcatWebServer.java:259) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:197) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:311) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:552) [spring-context-5.1.5.RELEASE.jar:5.1.5.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248) [spring-boot-2.1.3.RELEASE.jar:2.1.3.RELEASE]
	at com.bimowu.ResumeWebApplication.main(ResumeWebApplication.java:14) [classes/:?]
Caused by: java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_431]
	at sun.nio.ch.Net.bind(Net.java:438) ~[?:1.8.0_431]
	at sun.nio.ch.Net.bind(Net.java:430) ~[?:1.8.0_431]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:225) ~[?:1.8.0_431]
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74) ~[?:1.8.0_431]
	at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:236) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:210) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1085) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:1171) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:568) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1005) ~[tomcat-embed-core-9.0.16.jar:9.0.16]
	... 14 more
18:29:42.509 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-7094"]
18:29:42.509 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
18:29:42.515 [main] INFO  org.apache.catalina.util.LifecycleBase - The stop() method was called on component [StandardServer[-1]] after stop() had already been called. The second call will be ignored.
18:29:42.516 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-7094"]
18:29:42.516 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-7094"]
18:29:42.521 [main] INFO  com.bimowu.resume.common.service.impl.AutoStyleUpdateServiceImpl - 停止自动样式更新服务
18:29:42.521 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
