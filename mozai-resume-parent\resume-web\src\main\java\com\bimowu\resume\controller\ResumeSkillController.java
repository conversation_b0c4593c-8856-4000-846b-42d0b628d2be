package com.bimowu.resume.controller;

import com.bimowu.resume.common.service.ResumeSkillService;
import com.bimowu.resume.common.service.ResumeSkillSegmentService;
import com.bimowu.resume.entity.ResumeSkill;
import com.bimowu.resume.entity.ResumeSkillSegment;
import com.bimowu.resume.base.BaseResponse;
import com.bimowu.resume.vo.ResumeSkillVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 技能表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/skill")
public class ResumeSkillController {

    @Autowired
    private ResumeSkillService resumeSkillService;

    /**
     * 获取所有技能列表
     */
    @GetMapping("/selectlist")
    public BaseResponse list() {
        List<ResumeSkill> list = resumeSkillService.list();
        return BaseResponse.ok(list);
    }
}

