package com.bimowu.resume.common.service;

import com.bimowu.resume.config.FontConfig;

import java.io.InputStream;

/**
 * 字体管理器接口
 * 负责字体的加载、缓存和管理
 */
public interface FontManager {
    
    /**
     * 初始化字体管理器
     */
    void initialize();
    
    /**
     * 加载字体
     * @param fontInfo 字体信息
     * @return 是否成功
     */
    boolean loadFont(FontConfig.FontInfo fontInfo);
    
    /**
     * 获取字体数据
     * @param fontName 字体名称
     * @return 字体输入流
     */
    InputStream getFontStream(String fontName);
    
    /**
     * 获取字体数据（字节数组）
     * @param fontName 字体名称
     * @return 字体字节数组
     */
    byte[] getFontData(String fontName);
    
    /**
     * 检查字体是否可用
     * @param fontName 字体名称
     * @return 是否可用
     */
    boolean isFontAvailable(String fontName);
    
    /**
     * 获取所有可用字体
     * @return 字体名称数组
     */
    String[] getAvailableFonts();
    
    /**
     * 根据字体族获取字体名称
     * @param fontFamily 字体族
     * @return 字体名称
     */
    String getFontNameByFamily(String fontFamily);
    
    /**
     * 获取默认字体名称
     * @return 默认字体名称
     */
    String getDefaultFontName();
    
    /**
     * 清除字体缓存
     */
    void clearCache();
    
    /**
     * 重新加载所有字体
     */
    void reloadFonts();
    
    /**
     * 获取字体信息
     * @param fontName 字体名称
     * @return 字体信息
     */
    FontConfig.FontInfo getFontInfo(String fontName);
    
    /**
     * 验证字体文件
     * @param fontPath 字体文件路径
     * @return 验证结果
     */
    FontValidationResult validateFont(String fontPath);
    
    /**
     * 字体验证结果
     */
    class FontValidationResult {
        private boolean valid;
        private String fontName;
        private String fontFamily;
        private String[] supportedCharsets;
        private String errorMessage;
        
        public FontValidationResult(boolean valid) {
            this.valid = valid;
        }
        
        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getFontName() { return fontName; }
        public void setFontName(String fontName) { this.fontName = fontName; }
        
        public String getFontFamily() { return fontFamily; }
        public void setFontFamily(String fontFamily) { this.fontFamily = fontFamily; }
        
        public String[] getSupportedCharsets() { return supportedCharsets; }
        public void setSupportedCharsets(String[] supportedCharsets) { this.supportedCharsets = supportedCharsets; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}