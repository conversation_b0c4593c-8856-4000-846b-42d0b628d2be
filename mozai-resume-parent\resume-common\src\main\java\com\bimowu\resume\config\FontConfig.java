package com.bimowu.resume.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 字体配置类
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "pdf.fonts")
public class FontConfig {
    
    /**
     * 字体配置列表
     */
    private List<FontInfo> fonts;
    
    /**
     * 默认字体族
     */
    private String defaultFamily = "SimSun";
    
    /**
     * 字体别名映射
     */
    private Map<String, String> aliases;
    
    /**
     * 字体信息
     */
    @Data
    public static class FontInfo {
        /**
         * 字体名称
         */
        private String name;
        
        /**
         * 字体文件路径
         */
        private String path;
        
        /**
         * 字体族名称
         */
        private String family;
        
        /**
         * 字体样式 (normal, bold, italic, bold-italic)
         */
        private String style = "normal";
        
        /**
         * 字体权重
         */
        private int weight = 400;
        
        /**
         * 是否为默认字体
         */
        private boolean isDefault = false;
        
        /**
         * 字体编码
         */
        private String encoding = "UTF-8";
        
        /**
         * 是否嵌入到PDF中
         */
        private boolean embedded = true;
    }
}