<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.resume.common.dao.StyleSyncRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.resume.entity.StyleSyncRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_id" property="templateId" jdbcType="INTEGER"/>
        <result column="sync_type" property="syncType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP"/>
        <result column="change_log" property="changeLog" jdbcType="VARCHAR"/>
        <result column="before_version" property="beforeVersion" jdbcType="VARCHAR"/>
        <result column="after_version" property="afterVersion" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="changed_files" property="changedFiles" jdbcType="INTEGER"/>
        <result column="trigger_type" property="triggerType" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, template_id, sync_type, status, error_message, sync_time, change_log, 
        before_version, after_version, duration, changed_files, trigger_type, 
        operator, create_time, remark
    </sql>

    <!-- 根据模板ID查询同步记录 -->
    <select id="selectByTemplateId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE template_id = #{templateId,jdbcType=INTEGER}
        ORDER BY sync_time DESC
    </select>

    <!-- 根据模板ID查询同步记录（带限制） -->
    <select id="selectByTemplateIdWithLimit" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE template_id = #{templateId,jdbcType=INTEGER}
        ORDER BY sync_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询最近的同步记录 -->
    <select id="selectLatestByTemplateId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE template_id = #{templateId,jdbcType=INTEGER}
        ORDER BY sync_time DESC
        LIMIT 1
    </select>

    <!-- 根据状态查询同步记录 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE status = #{status,jdbcType=VARCHAR}
        ORDER BY sync_time DESC
    </select>

    <!-- 查询指定时间范围内的同步记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE sync_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        ORDER BY sync_time DESC
    </select>

    <!-- 统计同步成功率 -->
    <select id="selectSuccessRate" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END as success_rate
        FROM resume_style_sync_record
        WHERE template_id = #{templateId,jdbcType=INTEGER}
        <if test="days != null and days > 0">
            AND sync_time >= DATE_SUB(NOW(), INTERVAL #{days,jdbcType=INTEGER} DAY)
        </if>
    </select>

    <!-- 清理过期的同步记录 -->
    <delete id="deleteExpiredRecords" parameterType="java.lang.Integer">
        DELETE FROM resume_style_sync_record
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{expireDays,jdbcType=INTEGER} DAY)
    </delete>

    <!-- 插入同步记录 -->
    <insert id="insert" parameterType="com.bimowu.resume.entity.StyleSyncRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_style_sync_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="syncType != null">sync_type,</if>
            <if test="status != null">status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="changeLog != null">change_log,</if>
            <if test="beforeVersion != null">before_version,</if>
            <if test="afterVersion != null">after_version,</if>
            <if test="duration != null">duration,</if>
            <if test="changedFiles != null">changed_files,</if>
            <if test="triggerType != null">trigger_type,</if>
            <if test="operator != null">operator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId,jdbcType=INTEGER},</if>
            <if test="syncType != null">#{syncType,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
            <if test="syncTime != null">#{syncTime,jdbcType=TIMESTAMP},</if>
            <if test="changeLog != null">#{changeLog,jdbcType=VARCHAR},</if>
            <if test="beforeVersion != null">#{beforeVersion,jdbcType=VARCHAR},</if>
            <if test="afterVersion != null">#{afterVersion,jdbcType=VARCHAR},</if>
            <if test="duration != null">#{duration,jdbcType=BIGINT},</if>
            <if test="changedFiles != null">#{changedFiles,jdbcType=INTEGER},</if>
            <if test="triggerType != null">#{triggerType,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <!-- 更新同步记录 -->
    <update id="updateById" parameterType="com.bimowu.resume.entity.StyleSyncRecord">
        UPDATE resume_style_sync_record
        <set>
            <if test="templateId != null">template_id = #{templateId,jdbcType=INTEGER},</if>
            <if test="syncType != null">sync_type = #{syncType,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">error_message = #{errorMessage,jdbcType=VARCHAR},</if>
            <if test="syncTime != null">sync_time = #{syncTime,jdbcType=TIMESTAMP},</if>
            <if test="changeLog != null">change_log = #{changeLog,jdbcType=VARCHAR},</if>
            <if test="beforeVersion != null">before_version = #{beforeVersion,jdbcType=VARCHAR},</if>
            <if test="afterVersion != null">after_version = #{afterVersion,jdbcType=VARCHAR},</if>
            <if test="duration != null">duration = #{duration,jdbcType=BIGINT},</if>
            <if test="changedFiles != null">changed_files = #{changedFiles,jdbcType=INTEGER},</if>
            <if test="triggerType != null">trigger_type = #{triggerType,jdbcType=VARCHAR},</if>
            <if test="operator != null">operator = #{operator,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM resume_style_sync_record
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据ID查询记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM resume_style_sync_record
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

</mapper>