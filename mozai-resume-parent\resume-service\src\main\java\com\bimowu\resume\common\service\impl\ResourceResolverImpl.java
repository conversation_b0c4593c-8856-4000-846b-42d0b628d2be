package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.ResourceResolver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;

/**
 * 资源解析器实现
 */
@Slf4j
@Service
public class ResourceResolverImpl implements ResourceResolver {
    
    private static final String IMAGES_BASE_PATH = "static/images/";
    private static final String CSS_BASE_PATH = "static/css/";
    private static final String TEMPLATES_BASE_PATH = "templates/";
    
    @Override
    public InputStream resolveImage(String imagePath) {
        try {
            if (!isPathSafe(imagePath)) {
                log.warn("不安全的图片路径: {}", imagePath);
                return null;
            }
            
            String fullPath = IMAGES_BASE_PATH + imagePath;
            Resource resource = new ClassPathResource(fullPath);
            
            if (resource.exists()) {
                log.debug("成功解析图片资源: {}", fullPath);
                return resource.getInputStream();
            } else {
                log.warn("图片资源不存在: {}", fullPath);
                return null;
            }
            
        } catch (IOException e) {
            log.error("解析图片资源失败: {}", imagePath, e);
            return null;
        }
    }
    
    @Override
    public String resolveCSS(String cssPath) {
        if (!StringUtils.hasText(cssPath)) {
            log.warn("CSS路径为空");
            return null;
        }
        
        try {
            // 标准化路径
            String fullPath = Paths.get("static/css", cssPath).toString();
            Resource resource = new ClassPathResource(fullPath);
            
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String cssContent = new String(IOUtils.toByteArray(inputStream), StandardCharsets.UTF_8);
                    log.debug("成功解析CSS资源: {}", fullPath);
                    return cssContent;
                }
            } else {
                log.warn("CSS资源不存在: {}", fullPath);
                return null;
            }
        } catch (IOException e) {
            log.error("解析CSS资源失败: {}", cssPath, e);
            return null;
        }
    }
    
    @Override
    public InputStream resolveFont(String fontPath) {
        try {
            if (!isPathSafe(fontPath)) {
                log.warn("不安全的字体路径: {}", fontPath);
                return null;
            }
            
            String fullPath =  fontPath;
            Resource resource = new ClassPathResource(fullPath);
            
            if (resource.exists()) {
                log.debug("成功解析字体资源: {}", fullPath);
                return resource.getInputStream();
            } else {
                log.warn("字体资源不存在: {}", fullPath);
                return null;
            }
            
        } catch (IOException e) {
            log.error("解析字体资源失败: {}", fontPath, e);
            return null;
        }
    }
    
    @Override
    public String resolveTemplate(String templatePath) {
        try {
            if (!isPathSafe(templatePath)) {
                log.warn("不安全的模板路径: {}", templatePath);
                return null;
            }
            
            String fullPath = TEMPLATES_BASE_PATH + templatePath;
            Resource resource = new ClassPathResource(fullPath);
            
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    String templateContent = new String(IOUtils.toByteArray(inputStream), StandardCharsets.UTF_8);
                    log.debug("成功解析模板资源: {}", fullPath);
                    return templateContent;
                }
            } else {
                log.warn("模板资源不存在: {}", fullPath);
                return null;
            }

        } catch (IOException e) {
            log.error("解析模板资源失败: {}", templatePath, e);
            return null;
        }
    }
    
    @Override
    public boolean resourceExists(String resourcePath) {
        try {
            if (!isPathSafe(resourcePath)) {
                return false;
            }
            
            Resource resource = new ClassPathResource(resourcePath);
            return resource.exists();
            
        } catch (Exception e) {
            log.error("检查资源存在性失败: {}", resourcePath, e);
            return false;
        }
    }
    
    @Override
    public String getAbsolutePath(String relativePath) {
        try {
            if (!isPathSafe(relativePath)) {
                log.warn("不安全的相对路径: {}", relativePath);
                return null;
            }
            
            Resource resource = new ClassPathResource(relativePath);
            if (resource.exists()) {
                return resource.getURI().toString();
            } else {
                return null;
            }
            
        } catch (IOException e) {
            log.error("获取绝对路径失败: {}", relativePath, e);
            return null;
        }
    }
    
    @Override
    public boolean isPathSafe(String resourcePath) {
        if (!StringUtils.hasText(resourcePath)) {
            return false;
        }
        
        // 检查路径遍历攻击
        if (resourcePath.contains("..") || resourcePath.contains("./") || resourcePath.contains("\\")) {
            log.warn("检测到不安全的路径: {}", resourcePath);
            return false;
        }
        
        // 检查绝对路径
        if (Paths.get(resourcePath).isAbsolute()) {
            log.warn("不允许绝对路径: {}", resourcePath);
            return false;
        }
        
        // 检查危险字符
        if (resourcePath.contains(";") || resourcePath.contains("|") || resourcePath.contains("&")) {
            log.warn("路径包含危险字符: {}", resourcePath);
            return false;
        }
        
        return true;
    }
}