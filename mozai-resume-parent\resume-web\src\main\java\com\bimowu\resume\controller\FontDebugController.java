package com.bimowu.resume.controller;

import com.bimowu.resume.common.service.impl.FlyingSaucerPDFGenerator;
import com.bimowu.resume.utils.FontUtil;
import com.bimowu.resume.utils.ChineseFontFixer;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;

/**
 * 字体调试控制器
 * 用于测试中文字体在PDF中的显示效果
 */
@RestController
@RequestMapping("/api/debug/font")
@Slf4j
public class FontDebugController {

    @Autowired
    private FlyingSaucerPDFGenerator pdfGenerator;

    @Autowired
    private com.bimowu.resume.utils.ResumeDataMapper resumeDataMapper;

    @Autowired
    private ChineseFontFixer chineseFontFixer;
    
    /**
     * 测试中文字体显示
     */
    @GetMapping("/test-chinese")
    public ResponseEntity<byte[]> testChineseFont() {
        try {
            log.info("开始测试中文字体显示");
            
            // 生成测试HTML
            String testHtml = generateTestHtml();
            log.info("生成的测试HTML: {}", testHtml);
            
            // 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            boolean success = pdfGenerator.generatePDFFromHTML(testHtml, outputStream);
            
            if (success) {
                byte[] pdfBytes = outputStream.toByteArray();
                log.info("PDF生成成功，大小: {} bytes", pdfBytes.length);
                
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                headers.setContentDispositionFormData("attachment", "font-test.pdf");
                
                return ResponseEntity.ok()
                        .headers(headers)
                        .body(pdfBytes);
            } else {
                log.error("PDF生成失败");
                return ResponseEntity.status(500).build();
            }
            
        } catch (Exception e) {
            log.error("测试中文字体失败", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 获取字体信息
     */
    @GetMapping("/info")
    public ResponseEntity<String> getFontInfo() {
        try {
            StringBuilder info = new StringBuilder();
            info.append("=== 字体信息调试 ===\n");
            
            // 测试字体文件是否存在
            info.append("SimSun字体可用: ").append(FontUtil.isFontAvailable("SimSun")).append("\n");
            info.append("Microsoft YaHei字体可用: ").append(FontUtil.isFontAvailable("Microsoft YaHei")).append("\n");
            info.append("SimHei字体可用: ").append(FontUtil.isFontAvailable("SimHei")).append("\n");
            
            // 获取默认字体
            info.append("默认中文字体: ").append(FontUtil.getDefaultChineseFont()).append("\n");
            
            // 获取生成的CSS
            String css = FontUtil.getChineseFontCSS();
            info.append("生成的字体CSS长度: ").append(css.length()).append("\n");
            info.append("生成的字体CSS预览: ").append(css.substring(0, Math.min(500, css.length()))).append("\n");
            
            // 运行字体加载测试
            FontUtil.testFontLoading();
            
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_PLAIN)
                    .body(info.toString());
                    
        } catch (Exception e) {
            log.error("获取字体信息失败", e);
            return ResponseEntity.status(500)
                    .body("获取字体信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成测试HTML
     */
    private String generateTestHtml() {
        // 获取字体CSS
        String fontCSS = FontUtil.getChineseFontCSS();
        log.info("字体CSS: {}", fontCSS);
        return "<!DOCTYPE html>\n" +
               "<html lang=\"zh-CN\">\n" +
               "<head>\n" +
               "    <meta charset=\"UTF-8\" />\n" +
               "    <title>中文字体测试</title>\n" +
               "    <style type=\"text/css\">\n" +
               fontCSS + "\n" +
               "        .test-section {\n" +
               "            margin: 20px;\n" +
               "            padding: 15px;\n" +
               "            border: 1px solid #ccc;\n" +
               "        }\n" +
               "        .font-simsun {\n" +
               "            font-family: 'SimSun', '宋体', serif !important;\n" +
               "        }\n" +
               "        .font-simhei {\n" +
               "            font-family: 'SimHei', '黑体', sans-serif !important;\n" +
               "        }\n" +
               "        .font-yahei {\n" +
               "            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif !important;\n" +
               "        }\n" +
               "        .font-fallback {\n" +
               "            font-family: serif !important;\n" +
               "        }\n" +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <div class=\"test-section\">\n" +
               "        <h1>中文字体显示测试</h1>\n" +
               "        <p>这是一段测试中文文本，用于验证字体是否正确显示。</p>\n" +
               "    </div>\n" +
               "    \n" +
               "    <div class=\"test-section font-simsun\">\n" +
               "        <h2>SimSun字体测试</h2>\n" +
               "        <p>姓名：张三</p>\n" +
               "        <p>电话：138-0000-0000</p>\n" +
               "        <p>邮箱：<EMAIL></p>\n" +
               "        <p>地址：北京市朝阳区</p>\n" +
               "    </div>\n" +
               "    \n" +
               "    <div class=\"test-section font-simhei\">\n" +
               "        <h2>SimHei字体测试</h2>\n" +
               "        <p>工作经验：软件开发工程师</p>\n" +
               "        <p>技能特长：Java、Spring、MySQL</p>\n" +
               "        <p>教育背景：计算机科学与技术专业</p>\n" +
               "    </div>\n" +
               "    \n" +
               "    <div class=\"test-section font-yahei\">\n" +
               "        <h2>Microsoft YaHei字体测试</h2>\n" +
               "        <p>项目经验：电商系统开发</p>\n" +
               "        <p>自我评价：具有良好的团队合作精神</p>\n" +
               "    </div>\n" +
               "    \n" +
               "    <div class=\"test-section font-fallback\">\n" +
               "        <h2>系统默认字体测试</h2>\n" +
               "        <p>English Text: This is a test for English font display.</p>\n" +
               "        <p>中文文本：这是中文字体显示测试。</p>\n" +
               "        <p>混合文本：Name: 李四, Age: 25岁</p>\n" +
               "    </div>\n" +
               "</body>\n" +
               "</html>";
    }

    /**
     * 测试简历模板的中文字体显示
     */
    @GetMapping("/test-template/{templateId}")
    public ResponseEntity<byte[]> testResumeTemplate(@PathVariable Long templateId) {
        try {
            log.info("开始测试简历模板{}的中文字体显示", templateId);

            // 读取模板
            String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
            log.info("读取模板{}成功，长度: {}", templateId, templateHtml.length());

            // 填充测试数据
            String testHtml = fillTestData(templateHtml);
            log.info("填充测试数据完成，长度: {}", testHtml.length());

            // 应用中文字体修复
            String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(testHtml);
            log.info("中文字体修复完成，长度: {}", fixedHtml.length());

            // 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            boolean success = pdfGenerator.generatePDFFromHTML(fixedHtml, outputStream);

            if (success) {
                byte[] pdfBytes = outputStream.toByteArray();
                log.info("模板{}测试PDF生成成功，大小: {} KB", templateId, pdfBytes.length / 1024);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                headers.add("Content-Disposition", "inline; filename=template" + templateId + "_test.pdf");

                return ResponseEntity.ok()
                        .headers(headers)
                        .body(pdfBytes);
            } else {
                log.error("模板{}测试PDF生成失败", templateId);
                return ResponseEntity.status(500).body("PDF生成失败".getBytes());
            }

        } catch (Exception e) {
            log.error("测试模板{}失败", templateId, e);
            return ResponseEntity.status(500).body(("测试失败: " + e.getMessage()).getBytes());
        }
    }

    /**
     * 填充测试数据（包含加粗格式测试）
     */
    private String fillTestData(String template) {
        // 替换常见的占位符，包含**加粗**格式测试
        String result = template;
        result = result.replace("${name}", "**张三**");
        result = result.replace("${age}", "25");
        result = result.replace("${gender}", "男");
        result = result.replace("${phone}", "138-0000-0000");
        result = result.replace("${email}", "<EMAIL>");
        result = result.replace("${hometown}", "北京市朝阳区");
        result = result.replace("${jobObjective}", "**Java开发工程师**");
        result = result.replace("${education}", "本科 | **计算机科学与技术** | **北京大学** | 2018-2022");
        result = result.replace("${workExperience}", "**软件开发工程师** | 腾讯科技 | 2022-至今<br/>负责**后端系统开发**和维护，主要技术栈包括**Spring Boot**、**MySQL**等");
        result = result.replace("${projects}", "**电商系统开发** | 2023年<br/>使用**Spring Boot**开发微服务架构的电商平台，负责**用户模块**和**订单模块**的设计与实现");
        result = result.replace("${skills}", "**编程语言**：Java、Python<br/>**框架技术**：Spring、Spring Boot、MyBatis<br/>**数据库**：MySQL、Redis<br/>**其他工具**：Docker、Git");
        result = result.replace("${selfEvaluation}", "具有**良好的团队合作精神**，**热爱学习新技术**，有**较强的问题解决能力**。熟练掌握**Java开发**，对**微服务架构**有深入理解。");

        // 替换其他可能的占位符
        result = result.replaceAll("\\$\\{[^}]+\\}", "**测试内容**");

        return result;
    }

    /**
     * 测试真实简历数据结构的加粗格式
     */
    @GetMapping("/test-real-data/{templateId}")
    public ResponseEntity<byte[]> testRealResumeData(@PathVariable Long templateId) {
        try {
            log.info("开始测试真实简历数据结构的加粗格式，模板{}", templateId);

            // 创建测试简历数据
            ResumeFullSaveDto resume = createTestResumeData();

            // 读取模板
            String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
            log.info("读取模板{}成功，长度: {}", templateId, templateHtml.length());

            // 使用真实的填充方法
            log.error("准备调用HtmlTemplateUtil.fillTemplate - resume: {}, talentList: {}",
                resume != null ? "not null" : "null",
                resume != null && resume.getTalentList() != null ? resume.getTalentList().size() : "null");
            String filledHtml = HtmlTemplateUtil.fillTemplate(templateHtml, resume);
            log.error("HtmlTemplateUtil.fillTemplate调用完成，长度: {}", filledHtml.length());

            // 应用中文字体修复
            String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(filledHtml);
            log.info("中文字体修复完成，长度: {}", fixedHtml.length());

            // 检查HTML中是否包含<strong>标签
            int strongCount = (filledHtml.split("<strong>").length - 1);
            log.error("生成的HTML中包含 {} 个<strong>标签", strongCount);
            if (strongCount > 0) {
                log.error("HTML中包含<strong>标签，示例: {}", filledHtml.substring(Math.max(0, filledHtml.indexOf("<strong>") - 50), Math.min(filledHtml.length(), filledHtml.indexOf("<strong>") + 100)));
            }

            // 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            boolean success = pdfGenerator.generatePDFFromHTML(fixedHtml, outputStream);

            if (success) {
                byte[] pdfBytes = outputStream.toByteArray();
                log.info("真实数据模板{}测试PDF生成成功，大小: {} KB", templateId, pdfBytes.length / 1024);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                headers.add("Content-Disposition", "inline; filename=template" + templateId + "_real_test.pdf");

                return ResponseEntity.ok()
                        .headers(headers)
                        .body(pdfBytes);
            } else {
                log.error("真实数据模板{}测试PDF生成失败", templateId);
                return ResponseEntity.status(500).body("PDF生成失败".getBytes());
            }

        } catch (Exception e) {
            log.error("测试真实数据模板{}失败", templateId, e);
            return ResponseEntity.status(500).body(("测试失败: " + e.getMessage()).getBytes());
        }
    }

    /**
     * 创建包含加粗格式的测试简历数据
     */
    private ResumeFullSaveDto createTestResumeData() {
        ResumeFullSaveDto resume = new ResumeFullSaveDto();

        // 基本信息
        ResumeInformationVo info = new ResumeInformationVo();
        info.setName("张三");
        info.setAge(25);
        info.setGender("男");
        info.setPhone("**138-0000-0000**");
        info.setEmail("<EMAIL>");
        info.setHometown("北京市朝阳区");
        info.setJobObjective("**Java开发工程师**");
        resume.setInformation(info);

        // 教育经历
        ResumeEducationalVo education = new ResumeEducationalVo();
        education.setTimePeriod("2018-09 - 2022-07");
        education.setSchool("**北京大学**");
        education.setMajor("**计算机科学与技术**");
        education.setEducation("本科");
        education.setMainCourses("**数据结构与算法**、**操作系统**、**计算机网络**、**数据库系统**、**软件工程**");
        resume.setEducationList(Arrays.asList(education));

        // 工作经验
        ResumeWorkVo work = new ResumeWorkVo();
        work.setTimePeriod("2022-09 - 2025-07");
        work.setCompany("**腾讯科技有限公司**");
        work.setPosition("**软件开发工程师**");
        work.setWorkDescription("1. 负责**后端系统开发**和维护，主要使用**Spring Boot**框架；\n2. 参与**微服务架构**设计，使用**Docker**进行容器化部署；\n3. 优化**数据库查询**性能，熟练使用**MySQL**和**Redis**；\n4. 与前端团队协作，开发**RESTful API**接口。");
        resume.setWorkList(Arrays.asList(work));

        // 项目经验
        ResumeProjectExperienceVo project = new ResumeProjectExperienceVo();
        project.setTimePeriod("2024-09 - 2024-12");
        project.setProjectName("**电商系统开发**");
        project.setRole("**后端开发工程师**");
        project.setProjectDescription("**项目描述**：基于**Spring Boot**的微服务电商平台\n**技术栈**：**Spring Boot** + **MyBatis** + **MySQL** + **Redis** + **Docker**\n**主要职责**：\n1. 负责**用户模块**和**订单模块**的设计与实现；\n2. 使用**Redis**实现分布式缓存，提升系统性能；\n3. 基于**Docker**进行应用容器化部署。");
        resume.setProjectList(Arrays.asList(project));

        // 技能特长
        ResumeTalentVo skill = new ResumeTalentVo();
        skill.setSkillName("**技能特长**");
        skill.setSkillDescription("**编程语言**：熟练掌握**Java**、**Python**\n**框架技术**：**Spring**、**Spring Boot**、**MyBatis**\n**数据库**：**MySQL**、**Redis**、**MongoDB**\n**开发工具**：**IntelliJ IDEA**、**Git**、**Maven**\n**其他技能**：**Docker**、**Linux**、**微服务架构**");
        resume.setTalentList(Arrays.asList(skill));

        // 自我评价
        ResumeEvaluateVo evaluation = new ResumeEvaluateVo();
        evaluation.setSelfEvaluation("具有**良好的团队合作精神**，**热爱学习新技术**，有**较强的问题解决能力**。熟练掌握**Java开发**，对**微服务架构**有深入理解。具备**良好的代码规范**意识，注重**系统性能优化**。");
        resume.setEvaluate(evaluation);

        return resume;
    }

    /**
     * 测试markdownToHtml方法
     */
    @GetMapping("/test-markdown")
    public ResponseEntity<String> testMarkdownToHtml() {
        try {
            String testText = "这是**加粗文本**，这是普通文本，这是**另一个加粗**。";
            log.info("原始文本: {}", testText);

            // 使用反射调用私有方法
            java.lang.reflect.Method method = HtmlTemplateUtil.class.getDeclaredMethod("markdownToHtml", String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(null, testText);

            log.info("转换结果: {}", result);

            String response = "原始文本: " + testText + "\n转换结果: " + result;
            return ResponseEntity.ok()
                    .header("Content-Type", "text/plain; charset=utf-8")
                    .body(response);

        } catch (Exception e) {
            log.error("测试markdownToHtml失败", e);
            return ResponseEntity.status(500).body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试总结页面
     */
    @GetMapping("/test-summary")
    public ResponseEntity<String> testSummary() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html><head><meta charset='UTF-8'><title>中文字体和加粗功能测试总结</title></head><body>");
        html.append("<h1>🎉 中文字体和加粗功能修复完成！</h1>");
        html.append("<h2>✅ 修复内容：</h2>");
        html.append("<ul>");
        html.append("<li><strong>字体管理器修复</strong>：修复了FontManagerImpl中的内存检查逻辑错误</li>");
        html.append("<li><strong>PDF生成器修复</strong>：修复了字体路径问题，确保SimSun和SimHei字体正确加载</li>");
        html.append("<li><strong>简历模板修复</strong>：为所有10个简历模板添加了增强的中文字体CSS</li>");
        html.append("<li><strong>模板3 CSS修复</strong>：移除了覆盖加粗效果的font-weight: normal规则</li>");
        html.append("<li><strong>加粗功能验证</strong>：确认markdownToHtml()方法支持**文本**格式转换</li>");
        html.append("</ul>");
        html.append("<h2>🔧 测试链接：</h2>");
        html.append("<ul>");
        html.append("<li><a href='/resume/api/debug/font/test-markdown' target='_blank'>测试Markdown转换功能</a></li>");
        html.append("<li><a href='/resume/api/debug/font/test-real-data/1' target='_blank'>测试模板1（真实数据+加粗）</a></li>");
        html.append("<li><a href='/resume/api/debug/font/test-real-data/3' target='_blank'>测试模板3（真实数据+加粗）</a></li>");
        html.append("<li><a href='/resume/api/debug/font/test-real-data/5' target='_blank'>测试模板5（真实数据+加粗）</a></li>");
        html.append("<li><a href='/resume/api/debug/font/test-real-data/8' target='_blank'>测试模板8（真实数据+加粗）</a></li>");
        html.append("</ul>");
        html.append("<h2>📝 使用方法：</h2>");
        html.append("<p>用户现在可以在简历的各个字段中使用<code>**文本**</code>格式来实现加粗效果：</p>");
        html.append("<ul>");
        html.append("<li>工作描述：<code>负责**后端系统开发**和维护</code></li>");
        html.append("<li>技能描述：<code>**编程语言**：Java、Python</code></li>");
        html.append("<li>项目描述：<code>**项目名称**：电商系统开发</code></li>");
        html.append("</ul>");
        html.append("<p><strong>✨ 系统现在完全支持中文字体显示和加粗格式功能！</strong></p>");
        html.append("</body></html>");

        return ResponseEntity.ok()
                .header("Content-Type", "text/html; charset=utf-8")
                .body(html.toString());
    }

    /**
     * 测试ResumeDataMapper的formatContent方法
     */
    @GetMapping("/test-format-content")
    public ResponseEntity<String> testFormatContent() {
        try {
            // 直接测试formatContent方法
            String testText = "这是**加粗文本**，这是普通文本，这是**另一个加粗**。";
            log.info("测试formatContent - 原始文本: {}", testText);

            // 使用反射调用私有方法（单参数版本）
            java.lang.reflect.Method method = com.bimowu.resume.utils.ResumeDataMapper.class.getDeclaredMethod("formatContent", String.class);
            method.setAccessible(true);

            String result = (String) method.invoke(resumeDataMapper, testText);
            log.info("测试formatContent - 转换结果: {}", result);

            String response = "原始文本: " + testText + "\n转换结果: " + result;
            return ResponseEntity.ok()
                    .header("Content-Type", "text/plain; charset=utf-8")
                    .body(response);

        } catch (Exception e) {
            log.error("测试formatContent失败", e);
            return ResponseEntity.status(500).body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试Flying Saucer的加粗支持
     */
    @GetMapping("/test-bold-support")
    public ResponseEntity<byte[]> testBoldSupport() {
        log.info("开始测试Flying Saucer的加粗支持");

        try {
            // 创建专门测试加粗的HTML
            String htmlContent = "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset=\"UTF-8\"/>" +
                "<style>" +
                "body { font-family: 'SimSun', '宋体', Arial, sans-serif; font-size: 14px; line-height: 1.8; margin: 20px; }" +
                ".test-section { margin-bottom: 20px; border: 1px solid #ccc; padding: 10px; }" +
                ".bold-class { font-weight: bold; }" +
                ".bold-700 { font-weight: 700; }" +
                "strong { font-weight: bold !important; }" +
                "b { font-weight: bold !important; }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<h1>Flying Saucer 加粗测试</h1>" +
                "<div class=\"test-section\">" +
                "<h3>1. 普通文本 vs 加粗文本</h3>" +
                "<p>这是普通文本</p>" +
                "<p class=\"bold-class\">这是使用font-weight: bold的文本</p>" +
                "<p class=\"bold-700\">这是使用font-weight: 700的文本</p>" +
                "</div>" +
                "<div class=\"test-section\">" +
                "<h3>2. HTML标签测试</h3>" +
                "<p>这是普通文本，<strong>这是strong标签</strong>，继续普通文本</p>" +
                "<p>这是普通文本，<b>这是b标签</b>，继续普通文本</p>" +
                "</div>" +
                "<div class=\"test-section\">" +
                "<h3>3. 中文加粗测试</h3>" +
                "<p>普通中文：你好世界，测试加粗功能</p>" +
                "<p><strong>加粗中文：你好世界，测试加粗功能</strong></p>" +
                "<p>混合测试：普通文本<strong>加粗文本</strong>普通文本</p>" +
                "</div>" +
                "<div class=\"test-section\">" +
                "<h3>4. 英文加粗测试</h3>" +
                "<p>Normal text: Hello World, testing bold functionality</p>" +
                "<p><strong>Bold text: Hello World, testing bold functionality</strong></p>" +
                "<p>Mixed test: Normal text <strong>Bold text</strong> Normal text</p>" +
                "</div>" +
                "</body>" +
                "</html>";

            log.info("加粗测试HTML创建完成，长度: {}", htmlContent.length());

            // 使用中文字体修复器
            String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(htmlContent);
            log.info("中文字体修复完成，长度: {}", fixedHtml.length());

            // 生成PDF
            byte[] pdfBytes = pdfGenerator.generatePDF(fixedHtml);
            log.info("加粗测试PDF生成成功，大小: {} KB", pdfBytes.length / 1024);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "bold-support-test.pdf");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("加粗测试失败", e);
            return ResponseEntity.status(500).build();
        }
    }
}