<template>
  <img
    :src="processedSrc"
    :alt="alt"
    :class="className"
    crossorigin="anonymous"
    @error="handleError"
  />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: '头像'
  },
  className: {
    type: String,
    default: 'avatar'
  },
  defaultSrc: {
    type: String,
    default: '/images/default-avatar.svg'
  }
})

const imageSrc = ref(props.src || '')
const hasError = ref(false)

const processedSrc = computed(() => {
  if (hasError.value) {
    return props.defaultSrc
  }
  
  if (!imageSrc.value) {
    return props.defaultSrc
  }
  
  // 处理OSS图片URL
  if (imageSrc.value.includes('.oss-cn-')) {
    const timestamp = new Date().getTime()
    const separator = imageSrc.value.includes('?') ? '&' : '?'
    return `${imageSrc.value}${separator}_t=${timestamp}`
  }
  
  return imageSrc.value
})

const handleError = () => {
  console.error('图片加载失败:', imageSrc.value)
  hasError.value = true
}

onMounted(() => {
  // 确保图片能正确加载
  if (imageSrc.value && imageSrc.value.includes('.oss-cn-')) {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.onload = () => {
      console.log('图片预加载成功')
    }
    img.onerror = () => {
      console.error('图片预加载失败，使用默认图片')
      hasError.value = true
    }
    
    // 添加时间戳防止缓存
    const timestamp = new Date().getTime()
    const separator = imageSrc.value.includes('?') ? '&' : '?'
    img.src = `${imageSrc.value}${separator}_t=${timestamp}`
  }
})
</script>

<style scoped>
/* 可以根据需要添加样式 */
</style> 