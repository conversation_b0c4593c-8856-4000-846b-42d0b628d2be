package com.bimowu.resume.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.bimowu.resume.common.service.StyleConsistencyValidator;
import com.bimowu.resume.common.service.StyleExtractorService;
import com.bimowu.resume.common.service.StyleSyncService;
import com.bimowu.resume.dto.ExtractedStyles;
import com.bimowu.resume.entity.StyleSyncRecord;
import com.bimowu.resume.entity.TemplateStyleConfig;
import com.bimowu.resume.common.dao.StyleSyncRecordMapper;
import com.bimowu.resume.common.dao.TemplateStyleConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 样式同步服务实现
 */
@Slf4j
@Service
public class StyleSyncServiceImpl implements StyleSyncService {
    
    @Autowired
    private TemplateStyleConfigMapper templateStyleConfigMapper;
    
    @Autowired
    private StyleSyncRecordMapper styleSyncRecordMapper;
    
    @Autowired
    private StyleExtractorService styleExtractorService;
    
    @Autowired
    private StyleConsistencyValidator styleConsistencyValidator;
    
    @Override
    @Transactional
    @CacheEvict(value = "templateStyles", key = "#templateId")
    public StyleSyncRecord syncTemplateStyles(Integer templateId, ExtractedStyles extractedStyles) {
        log.info("开始同步模板 {} 的样式", templateId);
        
        StyleSyncRecord syncRecord = new StyleSyncRecord();
        syncRecord.setTemplateId(templateId);
        syncRecord.setSyncType("INCREMENTAL");
        syncRecord.setStatus("PENDING");
        syncRecord.setSyncTime(new Date());
        syncRecord.setTriggerType("MANUAL");
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取现有配置
            TemplateStyleConfig existingConfig = templateStyleConfigMapper.selectByTemplateId(templateId);
            String beforeVersion = existingConfig != null ? existingConfig.getVersion() : "0.0";
            syncRecord.setBeforeVersion(beforeVersion);
            
            // 创建或更新样式配置
            TemplateStyleConfig config = createOrUpdateStyleConfig(templateId, extractedStyles, existingConfig);
            
            // 保存配置
            if (existingConfig == null) {
                templateStyleConfigMapper.insert(config);
            } else {
                templateStyleConfigMapper.updateById(config);
            }
            
            // 更新同步记录
            syncRecord.setAfterVersion(config.getVersion());
            syncRecord.setStatus("SUCCESS");
            syncRecord.setChangeLog(generateChangeLog(existingConfig, config));
            syncRecord.setChangedFiles(1);
            
            log.info("模板 {} 样式同步成功，版本: {} -> {}", templateId, beforeVersion, config.getVersion());
            
        } catch (Exception e) {
            log.error("模板 {} 样式同步失败", templateId, e);
            syncRecord.setStatus("FAILED");
            syncRecord.setErrorMessage(e.getMessage());
        } finally {
            syncRecord.setDuration(System.currentTimeMillis() - startTime);
            styleSyncRecordMapper.insert(syncRecord);
        }
        
        return syncRecord;
    }
    
    @Override
    @Cacheable(value = "templateStyles", key = "#templateId")
    public ExtractedStyles getLatestStyles(Integer templateId) {
        log.debug("获取模板 {} 的最新样式", templateId);
        
        TemplateStyleConfig config = templateStyleConfigMapper.selectByTemplateId(templateId);
        if (config == null || !config.getEnabled()) {
            log.warn("模板 {} 的样式配置不存在或未启用，使用默认样式", templateId);
            return styleExtractorService.extractStyles(templateId);
        }
        
        return convertToExtractedStyles(config);
    }
    
    @Override
    public boolean needsStyleUpdate(Integer templateId, String currentVersion) {
        TemplateStyleConfig config = templateStyleConfigMapper.selectByTemplateId(templateId);
        if (config == null) {
            return true;
        }
        
        return !config.getVersion().equals(currentVersion);
    }
    
    @Override
    public List<StyleSyncRecord> getSyncHistory(Integer templateId, int limit) {
        return styleSyncRecordMapper.selectByTemplateIdWithLimit(templateId, limit);
    }
    
    @Override
    @Transactional
    public StyleSyncRecord forceFullSync(Integer templateId) {
        log.info("强制全量同步模板 {} 的样式", templateId);
        
        StyleSyncRecord syncRecord = new StyleSyncRecord();
        syncRecord.setTemplateId(templateId);
        syncRecord.setSyncType("FULL");
        syncRecord.setStatus("PENDING");
        syncRecord.setSyncTime(new Date());
        syncRecord.setTriggerType("MANUAL");
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 重新提取样式
            ExtractedStyles extractedStyles = styleExtractorService.extractStyles(templateId);
            
            // 删除现有配置
            TemplateStyleConfig existingConfig = templateStyleConfigMapper.selectByTemplateId(templateId);
            if (existingConfig != null) {
                templateStyleConfigMapper.deleteById(existingConfig.getId());
            }
            
            // 创建新配置
            TemplateStyleConfig newConfig = createOrUpdateStyleConfig(templateId, extractedStyles, null);
            templateStyleConfigMapper.insert(newConfig);
            
            // 清除缓存
            clearStyleCache(templateId);
            
            // 更新同步记录
            syncRecord.setBeforeVersion(existingConfig != null ? existingConfig.getVersion() : "0.0");
            syncRecord.setAfterVersion(newConfig.getVersion());
            syncRecord.setStatus("SUCCESS");
            syncRecord.setChangeLog("全量同步完成");
            syncRecord.setChangedFiles(1);
            
            log.info("模板 {} 全量同步成功", templateId);
            
        } catch (Exception e) {
            log.error("模板 {} 全量同步失败", templateId, e);
            syncRecord.setStatus("FAILED");
            syncRecord.setErrorMessage(e.getMessage());
        } finally {
            syncRecord.setDuration(System.currentTimeMillis() - startTime);
            styleSyncRecordMapper.insert(syncRecord);
        }
        
        return syncRecord;
    }
    
    @Override
    public SyncResult batchSyncAllTemplates() {
        log.info("开始批量同步所有模板样式");
        
        SyncResult result = new SyncResult();
        List<String> errors = new ArrayList<>();
        
        // 获取所有模板ID（这里假设有1-10个模板）
        List<Integer> templateIds = getActiveTemplateIds();
        result.setTotalTemplates(templateIds.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (Integer templateId : templateIds) {
            try {
                StyleSyncRecord syncRecord = forceFullSync(templateId);
                if ("SUCCESS".equals(syncRecord.getStatus())) {
                    successCount++;
                } else {
                    failureCount++;
                    errors.add(String.format("模板 %d: %s", templateId, syncRecord.getErrorMessage()));
                }
            } catch (Exception e) {
                failureCount++;
                errors.add(String.format("模板 %d: %s", templateId, e.getMessage()));
                log.error("批量同步模板 {} 失败", templateId, e);
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setErrors(errors);
        
        log.info("批量同步完成，成功: {}, 失败: {}", successCount, failureCount);
        return result;
    }
    
    @Override
    public StyleConsistencyResult validateStyleConsistency(Integer templateId) {
        log.info("验证模板 {} 的样式一致性", templateId);
        
        StyleConsistencyResult result = new StyleConsistencyResult();
        
        try {
            // 获取前端样式
            ExtractedStyles frontendStyles = styleExtractorService.extractStyles(templateId);
            
            // 获取后端样式
            ExtractedStyles backendStyles = getLatestStyles(templateId);
            
            // 使用样式一致性验证器进行比较
            boolean consistent = styleConsistencyValidator.validateConsistency(frontendStyles, backendStyles);
            List<String> differences = styleConsistencyValidator.findDifferences(frontendStyles, backendStyles);
            double similarity = styleConsistencyValidator.calculateSimilarity(frontendStyles, backendStyles);
            
            result.setConsistent(consistent);
            result.setDifferences(differences);
            result.setSimilarityScore(similarity);
            
            log.info("模板 {} 样式一致性验证完成，一致性: {}, 相似度: {}", templateId, consistent, similarity);
            
        } catch (Exception e) {
            log.error("模板 {} 样式一致性验证失败", templateId, e);
            result.setConsistent(false);
//            result.setDifferences(List.of("验证过程出错: " + e.getMessage()));
            result.setSimilarityScore(0.0);
        }
        
        return result;
    }
    
    /**
     * 创建或更新样式配置
     */
    private TemplateStyleConfig createOrUpdateStyleConfig(Integer templateId, ExtractedStyles extractedStyles, TemplateStyleConfig existingConfig) {
        TemplateStyleConfig config = existingConfig != null ? existingConfig : new TemplateStyleConfig();
        
        config.setTemplateId(templateId);
        config.setCssContent(extractedStyles.getCss());
        config.setFontConfig(JSON.toJSONString(extractedStyles.getFonts()));
        config.setColorPalette(JSON.toJSONString(extractedStyles.getColors()));
        config.setLayoutConfig(JSON.toJSONString(extractedStyles.getLayout()));
        config.setResponsiveRules(JSON.toJSONString(extractedStyles.getResponsive()));
        config.setLastSyncTime(new Date());
        config.setVersion(generateVersion());
        config.setEnabled(true);
        config.setUpdateTime(new Date());
        
        if (existingConfig == null) {
            config.setCreateTime(new Date());
        }
        
        return config;
    }
    
    /**
     * 将配置转换为ExtractedStyles
     */
    private ExtractedStyles convertToExtractedStyles(TemplateStyleConfig config) {
        ExtractedStyles styles = new ExtractedStyles();
        
        styles.setCss(config.getCssContent());
        styles.setVersion(config.getVersion());
        styles.setTimestamp(config.getLastSyncTime().getTime());
        
        if (StringUtils.hasText(config.getFontConfig())) {
            styles.setFonts(JSON.parseArray(config.getFontConfig(), ExtractedStyles.FontInfo.class));
        }
        
        if (StringUtils.hasText(config.getColorPalette())) {
            styles.setColors(JSON.parseObject(config.getColorPalette(), ExtractedStyles.ColorPalette.class));
        }
        
        if (StringUtils.hasText(config.getLayoutConfig())) {
            styles.setLayout(JSON.parseObject(config.getLayoutConfig(), ExtractedStyles.LayoutInfo.class));
        }
        
        if (StringUtils.hasText(config.getResponsiveRules())) {
            styles.setResponsive(JSON.parseObject(config.getResponsiveRules(), ExtractedStyles.ResponsiveRules.class));
        }
        
        return styles;
    }
    
    /**
     * 生成变更日志
     */
    private String generateChangeLog(TemplateStyleConfig before, TemplateStyleConfig after) {
        StringBuilder changelog = new StringBuilder();
        
        if (before == null) {
            changelog.append("初始化样式配置");
        } else {
            changelog.append("样式更新: ");
            
            // 比较CSS内容
            if (!before.getCssContent().equals(after.getCssContent())) {
                changelog.append("CSS样式已更新; ");
            }
            
            // 比较字体配置
            if (!before.getFontConfig().equals(after.getFontConfig())) {
                changelog.append("字体配置已更新; ");
            }
            
            // 比较颜色配置
            if (!before.getColorPalette().equals(after.getColorPalette())) {
                changelog.append("颜色配置已更新; ");
            }
            
            // 比较布局配置
            if (!before.getLayoutConfig().equals(after.getLayoutConfig())) {
                changelog.append("布局配置已更新; ");
            }
        }
        
        return changelog.toString();
    }
    
    /**
     * 生成版本号
     */
    private String generateVersion() {
        return String.format("v%d.%s", System.currentTimeMillis() / 1000, UUID.randomUUID().toString().substring(0, 8));
    }
    
    /**
     * 获取活跃的模板ID列表
     */
    private List<Integer> getActiveTemplateIds() {
        // 这里应该从数据库查询活跃的模板ID
        // 暂时返回固定的模板ID列表
        List<Integer> templateIds = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            templateIds.add(i);
        }
        return templateIds;
    }
    
    /**
     * 清除样式缓存
     */
    @CacheEvict(value = "templateStyles", key = "#templateId")
    private void clearStyleCache(Integer templateId) {
        log.debug("清除模板 {} 的样式缓存", templateId);
    }
}