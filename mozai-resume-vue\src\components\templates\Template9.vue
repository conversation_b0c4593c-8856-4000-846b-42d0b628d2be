<template>
  <div class="resume-template template-9">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <h1 class="name">{{ resume.modules.basic?.name || '未填写' }}</h1>
        <div class="contact-info">
          <div class="contact-item" v-if="resume.modules.basic?.age">
            <i class="el-icon-user"></i>
            <span>{{ resume.modules.basic.age }}岁</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.phone">
            <i class="el-icon-phone"></i>
            <span>{{ resume.modules.basic?.phone }}</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.email">
            <i class="el-icon-message"></i>
            <span>{{ resume.modules.basic?.email }}</span>
          </div>
          <div class="contact-item" v-if="resume.modules.basic?.address">
            <i class="el-icon-location"></i>
            <span>{{ resume.modules.basic?.address }}</span>
          </div>
        </div>
        <div class="job-objective" v-if="resume.modules.basic?.jobObjective">
          <i class="el-icon-aim"></i>
          <span>{{ resume.modules.basic.jobObjective }}</span>
        </div>
      </div>

      <!-- 教育经历 -->
      <div v-if="hasEducation" class="section">
        <div class="section-header">
          <h2>教育经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="item-header">
              <div class="item-school">{{ edu.school }}</div>
              <div class="item-date">{{ formatDate(edu.startDate) }} - {{ formatDate(edu.endDate) }}</div>
            </div>
            <div class="item-details">
              <div class="item-major">{{ edu.major }} ({{ edu.degree }})</div>
              <div class="item-description" v-if="edu.courses">
                <ul>
                  <li v-for="(item, i) in formatListItems(edu.courses)" :key="i">{{ item }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业技能 -->
      <div v-if="hasSkills" class="section">
        <div class="section-header">
          <h2>专业技能</h2>
        </div>
        <div class="section-content">
          <ul class="skills-list">
            <li v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
              <div class="skill-content">
                <MdPreview :modelValue="skill.description" />
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 项目经历 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <div class="project-date">{{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}</div>
            </div>
            <div class="project-role">{{ project.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="project.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ practice.name }}</div>
              <div class="project-date">{{ practice.startDate }} - {{ practice.endDate || '至今' }}</div>
            </div>
            <div class="project-role">{{ practice.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="practice.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 工作经历 -->
      <div v-if="hasWork" class="section">
        <div class="section-header">
          <h2>工作经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="item-header">
              <div class="item-company">{{ work.company }}</div>
              <div class="item-date">{{ work.startDate || formatDate(work.time?.[0]) }} - {{ work.endDate || formatDate(work.time?.[1]) }}</div>
            </div>
            <div class="item-details">
              <div class="item-position">{{ work.position }}</div>
              <div class="item-description">
                <div class="description-title">工作内容：</div>
                <div class="work-description">
                  <MdPreview :modelValue="work.description" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实习经历 -->
      <div v-if="hasInternship" class="section">
        <div class="section-header">
          <h2>实习经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(internship, index) in resume.modules.internship" :key="index" class="internship-item">
            <div class="item-header">
              <div class="item-company">{{ internship.company }}</div>
              <div class="item-date">{{ internship.startDate || formatDate(internship.time?.[0]) }} - {{ internship.endDate || formatDate(internship.time?.[1]) }}</div>
            </div>
            <div class="item-details">
              <div class="item-position">{{ internship.position }}</div>
              <div class="item-description">
                <div class="description-title">实习内容：</div>
                <ul>
                  <li v-for="(item, i) in formatListItems(internship.description)" :key="i">{{ item }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人总结 -->
      <div v-if="hasEvaluation" class="section">
        <div class="section-header">
          <h2>个人总结</h2>
        </div>
        <div class="section-content">
          <div class="evaluation-content">
            <MdPreview :modelValue="resume.modules.evaluation" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  // Extract year and month from Chinese date format (e.g., "2021年9月22日")
  const year = date.match(/(\d{4})年/)?.[1];
  const month = date.match(/(\d{1,2})月/)?.[1];
  if (!year || !month) return '';
  // Pad month with leading zero if needed
  const paddedMonth = month.padStart(2, '0');
  return `${year}.${paddedMonth}`;
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasInternship = computed(() => props.resume.modules && props.resume.modules.internship && props.resume.modules.internship.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasEvaluation = computed(() => props.resume.modules && props.resume.modules.evaluation && typeof props.resume.modules.evaluation === 'string' && props.resume.modules.evaluation.trim() !== '');
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化列表项
const formatListItems = (text) => {
  if (!text) return [];
  return text.split('\n')
      .filter(item => item.trim() !== '')
      .map(item => item.trim().replace(/^[•·-]\s*/, ''));
};

// 格式化段落
const formatParagraphs = (text) => {
  if (!text) return [];
  return text.split('\n').filter(p => p.trim() !== '');
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  color: #333;
  background-color: #fff;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 头部样式 */
.header-section {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #666;
}

.name {
  font-size: 24px;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-item {
  margin: 0 15px;
  display: flex;
  align-items: center;
}

.contact-item i {
  margin-right: 5px;
  color: #666;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 25px;
}

.section-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #666;
  padding-bottom: 5px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 15px;
}

.section-header h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 18px;
  background-color: #666;
}

.section-content {
  padding: 0 5px;
}

/* 教育经历样式 */
.education-item, .work-item, .project-item, .internship-item {
  margin-bottom: 20px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.item-school, .item-company, .item-name {
  font-weight: bold;
}

.item-date {
  color: #666;
}

.item-details {
  padding-left: 10px;
}

.item-major, .item-position {
  margin-bottom: 8px;
  color: #555;
}

.item-role {
  margin-bottom: 5px;
  color: #555;
}

.item-tech {
  margin-bottom: 10px;
  color: #666;
  font-style: italic;
}

.description-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.item-description ul {
  margin: 0;
  padding-left: 20px;
}

.item-description li {
  margin-bottom: 5px;
}

/* 技能样式 */
.skills-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.skill-item {
  margin-bottom: 8px;
  position: relative;
  padding-left: 15px;
}

.skill-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #999;
}

/* 自我评价样式 */
.evaluation-content {
  line-height: 1.7;
}

.evaluation-content ul {
  margin: 0;
  padding-left: 20px;
}

.evaluation-content li {
  margin-bottom: 8px;
}

/* 添加 MdPreview 相关样式 */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 20px;
}

:deep(.md-preview-html li) {
  margin: 0;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}
</style> 