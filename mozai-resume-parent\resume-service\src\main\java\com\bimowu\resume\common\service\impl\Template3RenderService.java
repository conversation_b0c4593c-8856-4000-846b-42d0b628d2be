package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.exception.PDFGenerationException;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import com.bimowu.resume.utils.PDFGenerationLogger;
import com.bimowu.resume.utils.ResumeDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 模板3渲染服务
 * 专门处理模板3的HTML渲染逻辑
 */
@Service
@Slf4j
public class Template3RenderService {
    
    @Autowired
    private ResumeDataMapper dataMapper;
    
    @Autowired
    private PDFGenerationLogger pdfLogger;
    
    @Autowired(required = false)
    private TemplateCacheService templateCacheService;
    
    @Autowired(required = false)
    private PDFPerformanceMonitor performanceMonitor;
    
    /**
     * 渲染模板3
     * 
     * @param resume 简历数据
     * @return 渲染后的HTML内容
     */
    public String renderTemplate3(ResumeFullSaveDto resume) {
        String resumeId = resume.getResumeVo() != null ? 
            String.valueOf(resume.getResumeVo().getResumeId()) : "unknown";
        
        long startTime = System.currentTimeMillis();
        
        // 开始PDF生成流程跟踪
        pdfLogger.startPDFGenerationTrace(resumeId, "3");
        
        // 开始性能监控
        PDFPerformanceMonitor.MonitorContext monitorContext = null;
        if (performanceMonitor != null) {
            monitorContext = performanceMonitor.startMonitoring("3", resumeId);
        }
        
        try {
            log.info("开始渲染模板3 - resumeId: {}", resumeId);
            
            // 数据验证阶段
            pdfLogger.logDataRetrievalPhase(resumeId, "数据验证", resume);
            validateResumeData(resume);
            
            // 获取模板内容（使用缓存）
            String templateContent;
            try {
                if (templateCacheService != null) {
                    templateContent = templateCacheService.getTemplate(3L);
                } else {
                    templateContent = HtmlTemplateUtil.readTemplate(3L);
                }
                pdfLogger.logTemplateLoading("3", true, "模板加载成功，长度: " + templateContent.length());
            } catch (Exception e) {
                pdfLogger.logTemplateLoading("3", false, "模板加载失败: " + e.getMessage());
                throw new PDFGenerationException("TEMPLATE_LOAD_ERROR", 
                    "模板3加载失败", e, "3", resumeId);
            }
            
            // 数据映射阶段
            pdfLogger.logDataRetrievalPhase(resumeId, "数据映射开始", null);
            Map<String, String> templateData;
            try {
                templateData = dataMapper.mapAllModules(resume);
                pdfLogger.logDataMappingTrace(resumeId, "全部模块", "映射完成", resume, templateData);
                pdfLogger.logTemplateRendering("3", templateData.size(), templateContent.length());
            } catch (Exception e) {
                pdfLogger.logErrorHandling(resumeId, "数据映射", "DATA_MAPPING_ERROR", "数据映射失败", e);
                throw new PDFGenerationException("DATA_MAPPING_ERROR", 
                    "数据映射失败", e, "3", resumeId);
            }
            
            // 模板渲染阶段
            pdfLogger.logDataRetrievalPhase(resumeId, "模板渲染开始", templateContent);
            String result;
            try {
                result = renderTemplate(templateContent, templateData, resumeId);
                pdfLogger.logPDFGeneration(resumeId, "3", result);
            } catch (Exception e) {
                pdfLogger.logErrorHandling(resumeId, "模板渲染", "TEMPLATE_RENDER_ERROR", "模板渲染失败", e);
                throw new PDFGenerationException("TEMPLATE_RENDER_ERROR", 
                    "模板渲染失败", e, "3", resumeId);
            }
            
            long duration = System.currentTimeMillis() - startTime;
            pdfLogger.logPerformanceStats("Template3Render", duration, 
                "HTML长度: " + result.length() + ", 数据项: " + templateData.size());
            
            // 完成PDF生成流程跟踪
            pdfLogger.completePDFGenerationTrace(resumeId, "3", true, duration);
            
            log.info("模板3渲染完成 - resumeId: {}, 耗时: {}ms", resumeId, duration);
            
            // 结束性能监控（成功）
            if (performanceMonitor != null && monitorContext != null) {
                performanceMonitor.endMonitoring(monitorContext, true, null);
            }
            
            return result;
            
        } catch (PDFGenerationException e) {
            // 完成PDF生成流程跟踪（失败）
            long duration = System.currentTimeMillis() - startTime;
            pdfLogger.completePDFGenerationTrace(resumeId, "3", false, duration);
            
            // 结束性能监控（失败）
            if (performanceMonitor != null && monitorContext != null) {
                performanceMonitor.endMonitoring(monitorContext, false, e.getMessage());
            }
            throw e; // 重新抛出已包装的异常
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            pdfLogger.logErrorHandling(resumeId, "未知模块", "UNKNOWN_ERROR", "未知错误", e);
            pdfLogger.completePDFGenerationTrace(resumeId, "3", false, duration);
            
            // 结束性能监控（失败）
            if (performanceMonitor != null && monitorContext != null) {
                performanceMonitor.endMonitoring(monitorContext, false, e.getMessage());
            }
            
            throw new PDFGenerationException("UNKNOWN_ERROR", 
                "模板3渲染过程中发生未知错误", e, "3", resumeId);
        }
    }
    
    /**
     * 渲染模板，替换占位符
     * 
     * @param template 模板内容
     * @param data 数据映射
     * @param resumeId 简历ID
     * @return 渲染后的内容
     */
    private String renderTemplate(String template, Map<String, String> data, String resumeId) {
        String result = template;
        
        // 记录替换过程
        int replacedCount = 0;
        int totalPlaceholders = data.size();
        
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            
            boolean wasReplaced = result.contains(placeholder);
            if (wasReplaced) {
                result = result.replace(placeholder, value);
                replacedCount++;
                log.debug("替换占位符: {} -> {} 字符", placeholder, value.length());
            }
            
            // 记录占位符处理过程
            pdfLogger.logPlaceholderProcessing(resumeId, placeholder, value, wasReplaced);
        }
        
        // 检查未替换的占位符
        int unreplacedCount = checkUnreplacedPlaceholders(result);
        
        // 记录占位符替换统计
        pdfLogger.logPlaceholderReplacement(totalPlaceholders, replacedCount, unreplacedCount);
        
        return result;
    }
    
    /**
     * 检查未替换的占位符
     * 
     * @param content 内容
     */
    private int checkUnreplacedPlaceholders(String content) {
        int count = 0;
        String temp = content;
        
        while (temp.contains("${")) {
            int start = temp.indexOf("${");
            int end = temp.indexOf("}", start);
            if (end > start) {
                String placeholder = temp.substring(start, end + 1);
                log.warn("发现未替换的占位符: {}", placeholder);
                count++;
                temp = temp.substring(end + 1);
            } else {
                break;
            }
        }
        
        return count;
    }
    
    /**
     * 验证简历数据完整性
     * 
     * @param resume 简历数据
     */
    public void validateResumeData(ResumeFullSaveDto resume) {
        String resumeId = resume != null && resume.getResumeVo() != null ? 
            String.valueOf(resume.getResumeVo().getResumeId()) : "unknown";
        
        if (resume == null) {
            pdfLogger.logDataValidation(resumeId, "验证失败: 简历数据为空");
            throw new PDFGenerationException("DATA_VALIDATION_ERROR", 
                "简历数据不能为空", "3", resumeId);
        }
        
        StringBuilder validationResult = new StringBuilder();
        validationResult.append("数据验证结果: ");
        
        // 基本信息验证
        boolean hasBasicInfo = resume.getInformation() != null;
        validationResult.append("基本信息:").append(hasBasicInfo ? "✓" : "✗").append(" ");
        
        // 各模块数据统计
        int educationCount = resume.getEducationList() != null ? resume.getEducationList().size() : 0;
        int workCount = resume.getWorkList() != null ? resume.getWorkList().size() : 0;
        int projectCount = resume.getProjectList() != null ? resume.getProjectList().size() : 0;
        int practiceCount = resume.getPracticeList() != null ? resume.getPracticeList().size() : 0;
        int skillCount = resume.getTalentList() != null ? resume.getTalentList().size() : 0;
        
        validationResult.append("教育:").append(educationCount).append("条 ");
        validationResult.append("工作:").append(workCount).append("条 ");
        validationResult.append("项目:").append(projectCount).append("条 ");
        validationResult.append("练手:").append(practiceCount).append("条 ");
        validationResult.append("技能:").append(skillCount).append("条 ");
        
        boolean hasCertificate = resume.getCertificate() != null && 
            resume.getCertificate().getCertificateName() != null;
        boolean hasCampus = resume.getCampus() != null && 
            resume.getCampus().getCampusExperience() != null;
        boolean hasInterest = resume.getInterest() != null && 
            resume.getInterest().getInterest() != null;
        boolean hasEvaluation = resume.getEvaluate() != null && 
            resume.getEvaluate().getSelfEvaluation() != null;
        
        validationResult.append("证书:").append(hasCertificate ? "✓" : "✗").append(" ");
        validationResult.append("校园:").append(hasCampus ? "✓" : "✗").append(" ");
        validationResult.append("兴趣:").append(hasInterest ? "✓" : "✗").append(" ");
        validationResult.append("评价:").append(hasEvaluation ? "✓" : "✗");
        
        pdfLogger.logDataValidation(resumeId, validationResult.toString());
        
        // 检查是否有足够的数据生成简历
        if (!hasBasicInfo && educationCount == 0 && workCount == 0 && projectCount == 0) {
            throw new PDFGenerationException("INSUFFICIENT_DATA", 
                "简历数据不足，至少需要基本信息或教育/工作/项目经验之一", "3", resumeId);
        }
        
        log.info("简历数据验证通过 - resumeId: {}", resumeId);
    }
}