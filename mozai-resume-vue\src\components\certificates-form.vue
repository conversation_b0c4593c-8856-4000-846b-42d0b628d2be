<template>
  <div class="form-container">
    <div class="editor-header">
      <h3>证书奖项</h3>
      <div class="editor-actions">
        <el-button type="success" size="small" @click="polishContent" :disabled="!formData.content">
          <el-icon class="icon"><magic-stick /></el-icon> AI润色
        </el-button>
      </div>
    </div>
    
    <div class="editor-content">
      <el-input
        v-model="formData.content"
        type="textarea"
        :rows="10"
        placeholder="请输入您的证书奖项信息，如：各类专业证书、获得的奖项荣誉等，建议按时间倒序排列，描述清晰具体。"
      />
    </div>
    
    <div class="tips">
      <el-alert
        title="填写建议"
        type="info"
        :closable="false"
        show-icon
      >
        <div class="tips-content">
          <p>1. 列出与应聘岗位相关的重要证书和奖项</p>
          <p>2. 包含证书/奖项名称、颁发机构和获得时间</p>
          <p>3. 对于重要的奖项，可简述获奖原因或评选范围</p>
          <p>4. 按时间倒序排列，最新的放在最前面</p>
        </div>
      </el-alert>
    </div>
    
    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box">{{ formData.content }}</div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box">{{ polishedContent }}</div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- AI生成对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="AI生成证书奖项建议"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="generateLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在生成内容...</div>
      </div>
      <div v-else>
        <el-form :model="generateParams" label-width="100px">
          <el-form-item label="目标职位">
            <el-input v-model="generateParams.position" placeholder="例如：前端开发工程师" />
          </el-form-item>
          
          <el-form-item label="行业领域">
            <el-select v-model="generateParams.industry" placeholder="请选择行业领域">
              <el-option label="IT/互联网" value="IT/互联网" />
              <el-option label="金融/银行" value="金融/银行" />
              <el-option label="教育培训" value="教育培训" />
              <el-option label="医疗健康" value="医疗健康" />
              <el-option label="设计/创意" value="设计/创意" />
              <el-option label="咨询/管理" value="咨询/管理" />
              <el-option label="其他行业" value="其他行业" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="教育水平">
            <el-select v-model="generateParams.education" placeholder="请选择您的最高学历">
              <el-option label="高中/中专" value="高中/中专" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
            </el-select>
          </el-form-item>
        </el-form>
        
        <div v-if="generatedContent" class="generated-content">
          <h4>AI生成的证书奖项建议</h4>
          <div class="content-box">{{ generatedContent }}</div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="doGenerateContent" :disabled="generateLoading" v-if="!generatedContent">
            生成
          </el-button>
          <el-button type="success" @click="useGeneratedContent" v-if="generatedContent">
            使用此内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { polishCertificate } from '../api/resume'
import { MagicStick } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: [String, Object],
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref({
  content: ''
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// AI生成相关
const generateDialogVisible = ref(false)
const generateLoading = ref(false)
const generatedContent = ref('')
const generateParams = ref({
  position: '',
  industry: '',
  education: ''
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 处理对象形式的数据
    if (typeof newVal === 'object' && newVal.certificateName) {
      formData.value.content = newVal.certificateName;
    } else {
      // 处理字符串形式的数据
      formData.value.content = typeof newVal === 'string' ? newVal : '';
    }
  } else {
    formData.value.content = '';
  }
}, { immediate: true, deep: true })

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  // 保持原有的对象结构
  if (props.data && typeof props.data === 'object') {
    emit('update', {
      ...props.data,
      certificateName: newVal
    });
  } else {
    emit('update', newVal);
  }
}, { immediate: false })

// 润色内容
const polishContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }
  
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 调用后端AI润色API
  polishCertificate(formData.value.content).then(res => {
    if (res.code === 0 || res.code === 200) {
      polishedContent.value = res.data
    } else {
      // 显示后端返回的具体错误消息
      const errorMsg = res.message || res.msg || '润色失败'
      ElMessage.error(errorMsg)
      polishedContent.value = formData.value.content
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    // 显示具体的错误消息
    ElMessage.error(error.message || '润色请求失败，请稍后重试')
    polishedContent.value = formData.value.content
    polishLoading.value = false
  })
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value
  }
  polishDialogVisible.value = false
  ElMessage.success('内容已更新')
}

// 显示生成对话框
const generateContent = () => {
  generateDialogVisible.value = true
  generatedContent.value = ''
}

// 执行生成
const doGenerateContent = () => {
  if (!generateParams.value.position) {
    ElMessage.warning('请至少填写目标职位信息')
    return
  }
  
  generateLoading.value = true
  
  // 模拟API请求
  setTimeout(() => {
    // 这里应该是调用后端AI生成API的地方
    // 为演示目的，使用模拟数据
    generatedContent.value = simulateGeneration(generateParams.value)
    generateLoading.value = false
  }, 1500)
}

// 使用生成的内容
const useGeneratedContent = () => {
  formData.value.content = generatedContent.value
  generateDialogVisible.value = false
  ElMessage.success('内容已应用')
}

// 模拟生成逻辑（实际项目中应替换为API调用）
const simulateGeneration = (params) => {
  const { position, industry, education } = params
  
  // 根据不同职位和行业生成不同建议
  let content = ''
  
  if (industry === 'IT/互联网') {
    content = `1. 计算机技术与软件专业资格证书（中级），工业和信息化部，2023年6月
2. 阿里云认证云计算专业工程师（ACP），阿里云，2022年9月
3. 全国大学生计算机系统与程序设计竞赛，三等奖，2021年5月
4. 校级程序设计大赛，一等奖，2020年11月
5. CCF CSP（计算机软件能力认证）优秀证书，中国计算机学会，分数300（满分400），2019年9月`
  } else if (industry === '金融/银行') {
    content = `1. 金融风险管理师（FRM）证书，全球风险管理协会(GARP)，2023年3月
2. 特许金融分析师（CFA）二级，CFA协会，2022年11月
3. 证券从业资格证书，中国证券业协会，2021年7月
4. 银行从业资格证书，中国银行业协会，2020年10月
5. 校级金融模拟交易大赛，二等奖，2019年12月`
  } else if (industry === '教育培训') {
    content = `1. 教师资格证书（高级中学），教育部，2023年4月
2. 普通话水平测试证书，二级甲等，国家语委，2022年5月
3. 剑桥英语教学能力证书（CELTA），剑桥大学考试委员会，2021年8月
4. 全国高校教育教学技能大赛，优秀奖，2020年10月
5. 校级优秀学生干部，2019年6月`
  } else {
    content = `1. ${position}专业技能证书，行业协会颁发，2023年
2. 校级学科竞赛三等奖，2022年
3. 团队合作奖，企业实习项目，2021年
4. 优秀学生干部证书，2020年
5. 英语专业四级证书，2019年`
  }
  
  return content
}

// 保存表单
const saveForm = () => {
  // 保持原有的对象结构
  if (props.data && typeof props.data === 'object') {
    emit('update', {
      ...props.data,
      certificateName: formData.value.content
    });
  } else {
    emit('update', formData.value.content);
  }
  ElMessage.success('证书奖项信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin-bottom: 20px;
}

.tips-content {
  font-size: 13px;
  line-height: 1.5;
}

.tips-content p {
  margin: 5px 0;
}

.loading-container {
  padding: 20px 0;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #909399;
}

.generated-content,
.polish-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.generated-content h4,
.polish-result h4,
.polish-options h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.content-box {
  padding: 10px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-line;
}

.original-content, .polished-content {
  margin-bottom: 15px;
}

.polish-options {
  margin-top: 20px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 