{"groups": [{"name": "ai", "type": "com.bimowu.resume.config.AIConfig", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "ai.usage-limit", "type": "com.bimowu.resume.config.AIConfig$UsageLimit", "sourceType": "com.bimowu.resume.config.AIConfig", "sourceMethod": "getUsageLimit()"}, {"name": "feature.toggle", "type": "com.bimowu.resume.config.FeatureToggleConfig", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig"}, {"name": "feature.toggle.ab-test", "type": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig", "sourceMethod": "getAbTest()"}, {"name": "feature.toggle.pdf", "type": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig", "sourceMethod": "getPdf()"}, {"name": "pdf", "type": "com.bimowu.resume.config.PDFConfig", "sourceType": "com.bimowu.resume.config.PDFConfig"}, {"name": "pdf.debug", "type": "com.bimowu.resume.config.PDFConfig$Debug", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getDebug()"}, {"name": "pdf.error", "type": "com.bimowu.resume.config.PDFConfig$Error", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getError()"}, {"name": "pdf.font", "type": "com.bimowu.resume.config.PDFConfig$Font", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getFont()"}, {"name": "pdf.fonts", "type": "com.bimowu.resume.config.FontConfig", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.generation", "type": "com.bimowu.resume.config.PDFConfig$Generation", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getGeneration()"}, {"name": "pdf.generation", "type": "com.bimowu.resume.config.PDFGenerationConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig"}, {"name": "pdf.generation.cache", "type": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getCache()"}, {"name": "pdf.generation.page", "type": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getPage()"}, {"name": "pdf.generation.performance", "type": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getPerformance()"}, {"name": "pdf.generation.template", "type": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getTemplate()"}, {"name": "pdf.image", "type": "com.bimowu.resume.config.PDFConfig$Image", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getImage()"}, {"name": "pdf.memory", "type": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig"}, {"name": "pdf.page", "type": "com.bimowu.resume.config.PDFConfig$Page", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getPage()"}, {"name": "pdf.performance", "type": "com.bimowu.resume.config.PDFConfig$Performance", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getPerformance()"}, {"name": "pdf.performance", "type": "com.bimowu.resume.config.PDFPerformanceConfig", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig"}, {"name": "pdf.template", "type": "com.bimowu.resume.config.PDFConfig$Template", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getTemplate()"}], "properties": [{"name": "ai.api-key", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "ai.model-name", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "pdf.fonts.aliases", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "字体别名映射", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.fonts.default-family", "type": "java.lang.String", "description": "默认字体族", "sourceType": "com.bimowu.resume.config.FontConfig", "defaultValue": "Sim<PERSON>un"}, {"name": "pdf.fonts.fonts", "type": "java.util.List<com.bimowu.resume.config.FontConfig.FontInfo>", "description": "字体配置列表", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.generation.engine", "type": "java.lang.String", "description": "PDF生成引擎 (flying-saucer, itext-xmlworker)", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": "flying-saucer"}, {"name": "pdf.generation.max-concurrent", "type": "java.lang.Integer", "description": "最大并发生成数", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": 10}, {"name": "pdf.generation.timeout-seconds", "type": "java.lang.Integer", "description": "生成超时时间（秒）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": 30}, {"name": "pdf.memory.enable-memory-monitoring", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内存监控", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.memory.estimated-memory-per-p-d-f", "type": "java.lang.Integer", "description": "PDF生成内存预估（MB）", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 100}, {"name": "pdf.memory.font-cache-size", "type": "java.lang.Integer", "description": "字体缓存大小限制（MB）", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 2}, {"name": "pdf.memory.gc-before-generation", "type": "java.lang.Bo<PERSON>an", "description": "是否在PDF生成前执行GC", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.memory.gc-threshold", "type": "java.lang.Double", "description": "GC触发阈值", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 70}, {"name": "pdf.memory.max-concurrent-generations", "type": "java.lang.Integer", "description": "最大并发PDF生成数", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 3}, {"name": "pdf.memory.max-heap-usage-percent", "type": "java.lang.Double", "description": "最大堆内存使用百分比", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 80}, {"name": "pdf.memory.use-lightweight-fonts", "type": "java.lang.Bo<PERSON>an", "description": "是否使用轻量级字体方案", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.performance.enable-html-cache", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTML内容缓存", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": false}, {"name": "pdf.performance.enable-memory-optimization", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内存优化", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.enable-performance-monitoring", "type": "java.lang.Bo<PERSON>an", "description": "是否启用性能监控", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.enable-template-cache", "type": "java.lang.Bo<PERSON>an", "description": "是否启用模板缓存", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.html-cache-expire-minutes", "type": "java.lang.Integer", "description": "HTML缓存过期时间（分钟）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 5}, {"name": "pdf.performance.large-file-threshold-bytes", "type": "java.lang.Long", "description": "大文件处理阈值（字节）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 0}, {"name": "pdf.performance.max-concurrent-generations", "type": "java.lang.Integer", "description": "最大并发PDF生成数量", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 10}, {"name": "pdf.performance.pdf-generation-timeout-seconds", "type": "java.lang.Integer", "description": "PDF生成超时时间（秒）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 30}, {"name": "pdf.performance.slow-query-threshold-ms", "type": "java.lang.Long", "description": "慢查询阈值（毫秒）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 5000}, {"name": "pdf.performance.template-cache-expire-minutes", "type": "java.lang.Integer", "description": "模板缓存过期时间（分钟）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 30}], "hints": []}