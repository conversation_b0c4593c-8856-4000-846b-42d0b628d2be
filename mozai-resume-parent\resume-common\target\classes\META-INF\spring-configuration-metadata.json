{"groups": [{"name": "ai", "type": "com.bimowu.resume.config.AIConfig", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "ai.usage-limit", "type": "com.bimowu.resume.config.AIConfig$UsageLimit", "sourceType": "com.bimowu.resume.config.AIConfig", "sourceMethod": "getUsageLimit()"}, {"name": "feature.toggle", "type": "com.bimowu.resume.config.FeatureToggleConfig", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig"}, {"name": "feature.toggle.ab-test", "type": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig", "sourceMethod": "getAbTest()"}, {"name": "feature.toggle.pdf", "type": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig", "sourceMethod": "getPdf()"}, {"name": "pdf", "type": "com.bimowu.resume.config.PDFConfig", "sourceType": "com.bimowu.resume.config.PDFConfig"}, {"name": "pdf.debug", "type": "com.bimowu.resume.config.PDFConfig$Debug", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getDebug()"}, {"name": "pdf.error", "type": "com.bimowu.resume.config.PDFConfig$Error", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getError()"}, {"name": "pdf.font", "type": "com.bimowu.resume.config.PDFConfig$Font", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getFont()"}, {"name": "pdf.fonts", "type": "com.bimowu.resume.config.FontConfig", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.generation", "type": "com.bimowu.resume.config.PDFConfig$Generation", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getGeneration()"}, {"name": "pdf.generation", "type": "com.bimowu.resume.config.PDFGenerationConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig"}, {"name": "pdf.generation.cache", "type": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getCache()"}, {"name": "pdf.generation.page", "type": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getPage()"}, {"name": "pdf.generation.page.margin", "type": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig$MarginConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "sourceMethod": "getMargin()"}, {"name": "pdf.generation.performance", "type": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getPerformance()"}, {"name": "pdf.generation.template", "type": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "sourceMethod": "getTemplate()"}, {"name": "pdf.image", "type": "com.bimowu.resume.config.PDFConfig$Image", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getImage()"}, {"name": "pdf.memory", "type": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig"}, {"name": "pdf.page", "type": "com.bimowu.resume.config.PDFConfig$Page", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getPage()"}, {"name": "pdf.performance", "type": "com.bimowu.resume.config.PDFConfig$Performance", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getPerformance()"}, {"name": "pdf.performance", "type": "com.bimowu.resume.config.PDFPerformanceConfig", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig"}, {"name": "pdf.template", "type": "com.bimowu.resume.config.PDFConfig$Template", "sourceType": "com.bimowu.resume.config.PDFConfig", "sourceMethod": "getTemplate()"}], "properties": [{"name": "ai.api-key", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "ai.model-name", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.AIConfig"}, {"name": "ai.usage-limit.default", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.AIConfig$UsageLimit"}, {"name": "ai.usage-limit.default-", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.AIConfig$UsageLimit"}, {"name": "feature.toggle.ab-test.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用A/B测试", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "defaultValue": false}, {"name": "feature.toggle.ab-test.new-system-traffic-percent", "type": "java.lang.Integer", "description": "新系统流量比例（0-100）", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "defaultValue": 50}, {"name": "feature.toggle.ab-test.test-user-ids", "type": "java.lang.String", "description": "测试用户ID列表（强制使用新系统）", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "defaultValue": ""}, {"name": "feature.toggle.ab-test.whitelist-user-ids", "type": "java.lang.String", "description": "白名单用户ID列表（强制使用旧系统）", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$AbTestConfig", "defaultValue": ""}, {"name": "feature.toggle.pdf.enable-error-recovery", "type": "java.lang.Bo<PERSON>an", "description": "是否启用错误恢复机制", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": true}, {"name": "feature.toggle.pdf.enable-flying-saucer", "type": "java.lang.Bo<PERSON>an", "description": "是否启用新的Flying Saucer PDF生成器", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": true}, {"name": "feature.toggle.pdf.enable-performance-monitoring", "type": "java.lang.Bo<PERSON>an", "description": "是否启用性能监控", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": true}, {"name": "feature.toggle.pdf.enable-style-sync", "type": "java.lang.Bo<PERSON>an", "description": "是否启用样式同步功能", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": true}, {"name": "feature.toggle.pdf.enable-style-validation", "type": "java.lang.Bo<PERSON>an", "description": "是否启用样式验证", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": false}, {"name": "feature.toggle.pdf.fallback-threshold", "type": "java.lang.Integer", "description": "降级阈值：连续失败次数超过此值时降级到旧系统", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": 3}, {"name": "feature.toggle.pdf.fallback-window-minutes", "type": "java.lang.Integer", "description": "降级时间窗口（分钟）", "sourceType": "com.bimowu.resume.config.FeatureToggleConfig$PdfFeatures", "defaultValue": 10}, {"name": "pdf.debug.detailed", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Debug", "defaultValue": false}, {"name": "pdf.debug.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Debug", "defaultValue": false}, {"name": "pdf.debug.log-level", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Debug", "defaultValue": "INFO"}, {"name": "pdf.debug.output-html", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Debug", "defaultValue": false}, {"name": "pdf.debug.output-path", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Debug", "defaultValue": "/tmp/pdf-debug"}, {"name": "pdf.error.enable-detailed-messages", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Error", "defaultValue": true}, {"name": "pdf.error.enable-error-recovery", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Error", "defaultValue": true}, {"name": "pdf.error.log-stack-trace", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Error", "defaultValue": true}, {"name": "pdf.error.max-error-count", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Error", "defaultValue": 5}, {"name": "pdf.font.default-family", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Font", "defaultValue": "Sim<PERSON>un"}, {"name": "pdf.font.enable-font-embedding", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Font", "defaultValue": true}, {"name": "pdf.font.fallback-fonts", "type": "java.lang.String[]", "sourceType": "com.bimowu.resume.config.PDFConfig$Font", "defaultValue": ["SimHei", "Microsoft YaHei", "<PERSON><PERSON>"]}, {"name": "pdf.font.fallback-fonts", "type": "java.lang.String[]", "sourceType": "com.bimowu.resume.config.PDFConfig$Font", "defaultValue": ["SimHei", "Microsoft YaHei", "<PERSON><PERSON>"]}, {"name": "pdf.font.font-path", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Font", "defaultValue": "classpath:fonts"}, {"name": "pdf.fonts.aliases", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "字体别名映射", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.fonts.default-family", "type": "java.lang.String", "description": "默认字体族", "sourceType": "com.bimowu.resume.config.FontConfig", "defaultValue": "Sim<PERSON>un"}, {"name": "pdf.fonts.fonts", "type": "java.util.List<com.bimowu.resume.config.FontConfig.FontInfo>", "description": "字体配置列表", "sourceType": "com.bimowu.resume.config.FontConfig"}, {"name": "pdf.generation.cache-ttl", "type": "java.lang.Long", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": 3600}, {"name": "pdf.generation.cache.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "defaultValue": true}, {"name": "pdf.generation.cache.max-size", "type": "java.lang.Integer", "description": "最大缓存大小", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "defaultValue": 1000}, {"name": "pdf.generation.cache.ttl", "type": "java.lang.Long", "description": "缓存TTL（秒）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "defaultValue": 3600}, {"name": "pdf.generation.cache.type", "type": "java.lang.String", "description": "缓存类型 (memory, redis)", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$CacheConfig", "defaultValue": "memory"}, {"name": "pdf.generation.enable-cache", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": true}, {"name": "pdf.generation.enable-fallback", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": true}, {"name": "pdf.generation.engine", "type": "java.lang.String", "description": "PDF生成引擎 (flying-saucer, itext-xmlworker)", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": "flying-saucer"}, {"name": "pdf.generation.max-concurrent", "type": "java.lang.Integer", "description": "最大并发生成数", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": 10}, {"name": "pdf.generation.max-html-size", "type": "java.lang.Long", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": 10485760}, {"name": "pdf.generation.page.dpi", "type": "java.lang.Float", "description": "DPI设置", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "defaultValue": 300}, {"name": "pdf.generation.page.margin.bottom", "type": "java.lang.Float", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig$MarginConfig", "defaultValue": 20}, {"name": "pdf.generation.page.margin.left", "type": "java.lang.Float", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig$MarginConfig", "defaultValue": 20}, {"name": "pdf.generation.page.margin.right", "type": "java.lang.Float", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig$MarginConfig", "defaultValue": 20}, {"name": "pdf.generation.page.margin.top", "type": "java.lang.Float", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig$MarginConfig", "defaultValue": 20}, {"name": "pdf.generation.page.orientation", "type": "java.lang.String", "description": "页面方向 (portrait, landscape)", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "defaultValue": "portrait"}, {"name": "pdf.generation.page.size", "type": "java.lang.String", "description": "页面大小 (A4, A3, LETTER等)", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PageConfig", "defaultValue": "A4"}, {"name": "pdf.generation.performance.async-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用异步生成", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "defaultValue": false}, {"name": "pdf.generation.performance.memory-limit-m-b", "type": "java.lang.Integer", "description": "内存限制（MB）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "defaultValue": 512}, {"name": "pdf.generation.performance.pool-core-size", "type": "java.lang.Integer", "description": "对象池核心大小", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "defaultValue": 5}, {"name": "pdf.generation.performance.pool-idle-timeout", "type": "java.lang.Integer", "description": "对象池空闲超时（秒）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "defaultValue": 60}, {"name": "pdf.generation.performance.pool-max-size", "type": "java.lang.Integer", "description": "对象池最大大小", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$PerformanceConfig", "defaultValue": 10}, {"name": "pdf.generation.retry-count", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": 2}, {"name": "pdf.generation.template.base-path", "type": "java.lang.String", "description": "模板根路径", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig", "defaultValue": "templates/pdf"}, {"name": "pdf.generation.template.custom-c-s-s", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "自定义CSS", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig"}, {"name": "pdf.generation.template.sync-interval", "type": "java.lang.Integer", "description": "样式同步间隔（秒）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig", "defaultValue": 3600}, {"name": "pdf.generation.template.validation-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用样式验证", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig$TemplateConfig", "defaultValue": true}, {"name": "pdf.generation.timeout-ms", "type": "java.lang.Long", "sourceType": "com.bimowu.resume.config.PDFConfig$Generation", "defaultValue": 30000}, {"name": "pdf.generation.timeout-seconds", "type": "java.lang.Integer", "description": "生成超时时间（秒）", "sourceType": "com.bimowu.resume.config.PDFGenerationConfig", "defaultValue": 30}, {"name": "pdf.image.enable-compression", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Image", "defaultValue": true}, {"name": "pdf.image.max-height", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Image", "defaultValue": 600}, {"name": "pdf.image.max-width", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Image", "defaultValue": 800}, {"name": "pdf.image.quality", "type": "java.lang.Double", "sourceType": "com.bimowu.resume.config.PDFConfig$Image", "defaultValue": 0.8}, {"name": "pdf.memory.enable-memory-monitoring", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内存监控", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.memory.estimated-memory-per-p-d-f", "type": "java.lang.Integer", "description": "PDF生成内存预估（MB）", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 100}, {"name": "pdf.memory.font-cache-size", "type": "java.lang.Integer", "description": "字体缓存大小限制（MB）", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 2}, {"name": "pdf.memory.gc-before-generation", "type": "java.lang.Bo<PERSON>an", "description": "是否在PDF生成前执行GC", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.memory.gc-threshold", "type": "java.lang.Double", "description": "GC触发阈值", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 70}, {"name": "pdf.memory.max-concurrent-generations", "type": "java.lang.Integer", "description": "最大并发PDF生成数", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 3}, {"name": "pdf.memory.max-heap-usage-percent", "type": "java.lang.Double", "description": "最大堆内存使用百分比", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": 80}, {"name": "pdf.memory.use-lightweight-fonts", "type": "java.lang.Bo<PERSON>an", "description": "是否使用轻量级字体方案", "sourceType": "com.bimowu.resume.config.MemoryOptimizedPDFConfig", "defaultValue": true}, {"name": "pdf.page.format", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": "A4"}, {"name": "pdf.page.margin-bottom", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": 20}, {"name": "pdf.page.margin-left", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": 20}, {"name": "pdf.page.margin-right", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": 20}, {"name": "pdf.page.margin-top", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": 20}, {"name": "pdf.page.orientation", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Page", "defaultValue": "PORTRAIT"}, {"name": "pdf.performance.enable-html-cache", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTML内容缓存", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": false}, {"name": "pdf.performance.enable-memory-optimization", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内存优化", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.enable-monitoring", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Performance", "defaultValue": true}, {"name": "pdf.performance.enable-performance-monitoring", "type": "java.lang.Bo<PERSON>an", "description": "是否启用性能监控", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.enable-statistics", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Performance", "defaultValue": true}, {"name": "pdf.performance.enable-template-cache", "type": "java.lang.Bo<PERSON>an", "description": "是否启用模板缓存", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": true}, {"name": "pdf.performance.html-cache-expire-minutes", "type": "java.lang.Integer", "description": "HTML缓存过期时间（分钟）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 5}, {"name": "pdf.performance.large-file-threshold-bytes", "type": "java.lang.Long", "description": "大文件处理阈值（字节）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 0}, {"name": "pdf.performance.max-concurrent-generations", "type": "java.lang.Integer", "sourceType": "com.bimowu.resume.config.PDFConfig$Performance", "defaultValue": 10}, {"name": "pdf.performance.max-concurrent-generations", "type": "java.lang.Integer", "description": "最大并发PDF生成数量", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 10}, {"name": "pdf.performance.pdf-generation-timeout-seconds", "type": "java.lang.Integer", "description": "PDF生成超时时间（秒）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 30}, {"name": "pdf.performance.slow-query-threshold-ms", "type": "java.lang.Long", "sourceType": "com.bimowu.resume.config.PDFConfig$Performance", "defaultValue": 5000}, {"name": "pdf.performance.slow-query-threshold-ms", "type": "java.lang.Long", "description": "慢查询阈值（毫秒）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 5000}, {"name": "pdf.performance.statistics-reset-interval", "type": "java.lang.Long", "sourceType": "com.bimowu.resume.config.PDFConfig$Performance", "defaultValue": 3600}, {"name": "pdf.performance.template-cache-expire-minutes", "type": "java.lang.Integer", "description": "模板缓存过期时间（分钟）", "sourceType": "com.bimowu.resume.config.PDFPerformanceConfig", "defaultValue": 30}, {"name": "pdf.template.base-path", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Template", "defaultValue": "classpath:templates/resume"}, {"name": "pdf.template.enable-validation", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Template", "defaultValue": true}, {"name": "pdf.template.encoding", "type": "java.lang.String", "sourceType": "com.bimowu.resume.config.PDFConfig$Template", "defaultValue": "UTF-8"}, {"name": "pdf.template.preload-templates", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.bimowu.resume.config.PDFConfig$Template", "defaultValue": true}], "hints": []}