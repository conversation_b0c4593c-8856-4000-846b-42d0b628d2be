<template>
  <div class="form-container">
    <div class="work-list">
      <div v-for="(work, index) in formData" :key="index" class="work-item">
        <div class="work-header">
          <h3>工作经验 #{{ index + 1 }}</h3>
          <div class="work-actions">
            <el-button 
              type="success" 
              size="small" 
              @click="polishWorkDescription(work, index)" 
              :disabled="!work.description"
            >
              <el-icon class="icon"><magic-stick /></el-icon> AI润色
            </el-button>
            <el-button 
              v-if="formData.length > 1" 
              type="danger" 
              size="small" 
              icon="Delete" 
              circle 
              @click="removeWork(index)"
            ></el-button>
          </div>
        </div>
        
        <el-form :model="work" label-width="100px">
          <el-form-item label="公司名称" required>
            <el-input v-model="work.company" placeholder="请输入公司名称" />
          </el-form-item>
          
          <el-form-item label="职位" required>
            <el-input v-model="work.position" placeholder="请输入您的职位" />
          </el-form-item>
          
          <el-form-item label="工作地点">
            <el-input v-model="work.location" placeholder="请输入工作城市" />
          </el-form-item>
          
          <el-form-item label="在职时间" required>
            <el-date-picker
              v-model="work.startDate"
              type="month"
              placeholder="开始时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="work.endDate"
              type="month"
              placeholder="结束时间（在职则留空）"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 160px;"
            />
            <el-checkbox v-model="work.currentJob" style="margin-left: 10px">至今</el-checkbox>
          </el-form-item>
          
          <el-form-item label="部门">
            <el-input v-model="work.department" placeholder="请输入您所在的部门" />
          </el-form-item>
          
          <el-form-item label="工作性质">
            <el-select v-model="work.jobType" placeholder="请选择工作性质">
              <el-option label="全职" value="全职" />
              <el-option label="兼职" value="兼职" />
              <el-option label="实习" value="实习" />
              <el-option label="自由职业" value="自由职业" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="工作描述" required>
            <el-input
              type="textarea"
              v-model="work.description"
              placeholder="请描述您的主要工作职责、成就和贡献"
              :rows="4"
            />
          </el-form-item>
          
          <el-form-item label="离职原因">
            <el-input
              v-model="work.leaveReason"
              placeholder="可选填写，如个人发展、家庭原因等"
            />
          </el-form-item>
        </el-form>
        
        <el-divider v-if="index < formData.length - 1" />
      </div>
    </div>
    
    <div class="add-work">
      <el-button type="dashed" @click="addWork" icon="Plus">添加工作经验</el-button>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
    
    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色工作描述"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box">{{ workToPolish?.description }}</div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box">{{ polishedContent }}</div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { polishWork } from '../api/resume'
import { MagicStick } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref([])

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)
const workToPolish = ref(null)
const polishIndex = ref(-1)

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && newVal.length) {
    formData.value = JSON.parse(JSON.stringify(newVal))
  } else {
    // 如果没有数据，创建一个空白的工作经验
    formData.value = [createEmptyWork()]
  }
}, { immediate: true, deep: true })

// 创建空白的工作经验对象
function createEmptyWork() {
  return {
    company: '',
    position: '',
    location: '',
    startDate: '',
    endDate: '',
    currentJob: false,
    department: '',
    jobType: '全职',
    description: '',
    leaveReason: ''
  }
}

// 添加工作经验
const addWork = () => {
  formData.value.push(createEmptyWork())
}

// 移除工作经验
const removeWork = (index) => {
  formData.value.splice(index, 1)
}

// 润色工作描述
const polishWorkDescription = (work, index) => {
  if (!work.description) {
    ElMessage.warning('请先输入工作描述再使用AI润色功能')
    return
  }
  
  workToPolish.value = work
  polishIndex.value = index
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 构建完整的工作经验描述
  const fullDescription = `公司：${work.company}\n职位：${work.position}\n部门：${work.department || '未填写'}\n工作描述：\n${work.description}`
  
  // 调用后端AI润色API
  polishWork(fullDescription).then(res => {
    if (res.code === 0) {
      // 只提取工作描述部分
      const polishedText = res.data
      const descriptionPart = extractWorkDescription(polishedText)
      polishedContent.value = descriptionPart || work.description
    } else {
      ElMessage.error(res.msg || '润色失败')
      polishedContent.value = work.description
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    ElMessage.error('润色请求失败，请稍后重试')
    polishedContent.value = work.description
    polishLoading.value = false
  })
}

// 从润色结果中提取工作描述部分
const extractWorkDescription = (text) => {
  // 尝试提取"工作描述："之后的内容
  const match = text.match(/工作描述：\s*\n*([\s\S]*)/i)
  if (match && match[1]) {
    return match[1].trim()
  }
  return text // 如果无法提取，返回原文本
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1 && workToPolish.value && polishIndex.value >= 0) {
    formData.value[polishIndex.value].description = polishedContent.value
  }
  polishDialogVisible.value = false
  workToPolish.value = null
  polishIndex.value = -1
  ElMessage.success('内容已更新')
}

// 保存表单
const saveForm = () => {
  // 基本验证
  let isValid = true
  formData.value.forEach((work, index) => {
    if (!work.company || !work.position || !work.startDate || !work.description) {
      ElMessage.warning(`工作经验 #${index + 1} 中的公司名称、职位、开始时间和工作描述为必填项`)
      isValid = false
    }
  })
  
  if (!isValid) return
  
  // 处理"至今"情况
  const processedData = formData.value.map(work => {
    const processed = { ...work }
    if (processed.currentJob) {
      processed.endDate = '至今'
    }
    return processed
  })
  
  emit('update', processedData)
  ElMessage.success('工作经验信息已保存')
}

// 重置表单
const resetForm = () => {
  if (props.data && props.data.length) {
    formData.value = JSON.parse(JSON.stringify(props.data))
  } else {
    formData.value = [createEmptyWork()]
  }
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.work-list {
  margin-bottom: 20px;
}

.work-item {
  margin-bottom: 20px;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.work-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.work-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 5px;
}

.date-separator {
  margin: 0 10px;
}

.add-work {
  margin: 20px 0;
  text-align: center;
}

.add-work .el-button {
  width: 100%;
  border-style: dashed;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.loading-container {
  padding: 20px 0;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #409eff;
  font-size: 14px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content, .polished-content {
  flex: 1;
}

.content-box {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.polish-options {
  margin-top: 20px;
}
</style> 