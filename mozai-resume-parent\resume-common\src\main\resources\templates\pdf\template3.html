<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 12px;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 20px;
            color: #777777;
            line-height: 1.5;
            font-size: 12px;
            background: white;
        }

        .resume-template {
            width: 100%;
            max-width: 800px;
            background: white;
            color: #777777;
            font-family: inherit !important;
            margin: 0 auto;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif !important;
            }
        }

        .header-content {
            margin-bottom: 15px; /* 减少底部间距 */
            padding-bottom: 8px; /* 减少内边距 */
        }



        .resume-title {
            font-size: 20px;
            font-weight: bold;
            color: #222222;
            margin-bottom: 10px; /* 减少标题底部间距 */
            text-align: left;
        }

        /* 个人信息和头像区域 - 使用表格布局 */
        .info-photo-section {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .basic-info-section {
            display: table-cell;
            width: 65%;
            vertical-align: top;
            padding-right: 15px;
        }

        /* 信息列样式 - 使用浮动布局 */
        .info-column {
            float: left;
            width: 45%;
            margin-right: 5%;
        }

        .info-column:last-child {
            margin-right: 0;
        }

        /* 清除浮动 */
        .basic-info-section:after {
            content: "";
            display: table;
            clear: both;
        }

        .info-item {
            margin-bottom: 8px;
            white-space: nowrap;
            font-size: 12px;
            line-height: 1.4;
        }

        .info-label {
            font-weight: bold !important;
            margin-right: 3px;
            color: #333;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
        }

        .info-value {
            color: #777777;
        }

        .photo-container {
            display: table-cell;
            width: 100px;
            min-width: 100px;
            height: 94px;
            vertical-align: top;
            text-align: center;
            border: none;
            padding: 0;
            overflow: visible;
            position: relative;
        }

        .photo {
            width: 95px;
            height: 134px;
            display: block;
            margin: -40px auto 0 auto;
            object-fit: cover;
            object-position: center;
            border-radius: 3px;
        }

        .resume-section {
            margin-bottom: 18px;
        }

        .section-header {
            margin-bottom: 12px;
            background-color: #ffffff;
            padding: 0;
            display: flex;
            align-items: center;
        }

        .section-header h2 {
            font-size: 14px;
            color: #FF0000;
            margin: 0;
            font-weight: bold;
            flex-grow: 1;
            border-bottom: 1px solid #FF0000;
            padding-bottom: 2px;
        }

        .evaluation-content,
        .certificate-content,
        .campus-content,
        .interests-content,
        .work-content,
        .project-content,
        .skills-content,
        .education-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-left: 0;
        }

        .work-item,
        .education-item,
        .project-item,
        .practice-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
            margin-bottom: 8px;
        }

        .project-header,
        .work-header,
        .edu-header,
        .practice-header {
            display: table;
            width: 100%;
            margin-bottom: 5px;
            table-layout: fixed;
        }

        .edu-date,
        .work-time,
        .project-time,
        .practice-time {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: left;
            vertical-align: middle;
        }

        .edu-school,
        .work-company,
        .project-name,
        .practice-name {
            display: table-cell;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
        }

        .edu-degree,
        .work-position,
        .project-role,
        .practice-role {
            display: table-cell;
            color: #333;
            font-size: 12px;
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            width: 33.33%;
            text-align: right;
            vertical-align: middle;
        }

        .edu-info {
            color: #777777;
            font-size: 12px;
            margin-top: 2px;
        }

        .edu-courses {
            margin-top: 0;
            line-height: 1.4;
        }

        .edu-courses p {
            margin: 0 !important;
            padding: 0 !important;
            display: inline;
        }

        .courses-label {
            font-weight: bold !important;
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #333;
            font-size: 12px;
            margin-right: 0;
            display: inline;
        }



        .skills-description,
        .project-description,
        .work-description {
            line-height: 1.6;
            text-align: justify;
            margin-left: 0;
            font-size: 12px;
            color: #777777;
        }

        .work-description ul,
        .project-description ul {
            margin: 3px 0;
            padding-left: 15px;
        }

        .work-description li,
        .project-description li {
            margin-bottom: 2px;
            font-size: 12px;
        }

        .project-item {
            margin-bottom: 12px;
        }



        .project-url {
            font-weight: normal;
            color: #0066cc;
            text-decoration: underline;
        }

        .skill-item {
            margin-bottom: 10px;
        }

        .skill-name {
            font-weight: bold;
            margin-bottom: 2px;
            font-size: 12px;
        }

        .skill-description {
            color: #777777;
            line-height: 1.6;
            font-size: 12px;
        }

        .certificate-content ul,
        .campus-content ul,
        .interests-content ul,
        .evaluation-content ul {
            margin: 3px 0;
            padding-left: 15px;
        }

        .certificate-content li,
        .campus-content li,
        .interests-content li,
        .evaluation-content li {
            margin-bottom: 2px;
            font-size: 12px;
        }

        .certificate-content p,
        .campus-content p,
        .interests-content p,
        .evaluation-content p {
            margin: 0 0 3px 0;
            font-size: 12px;
            line-height: 1.6;
            color: #777777;
        }

        /* 加粗文本样式 - 支持**文本**格式 */
        .bold-text {
            font-weight: bold !important;
        }

        /* 通用加粗样式 - 使用SourceHanSerifSC-Bold字体，纯黑色突出显示 */
        strong, b,
        .work-description strong,
        .project-description strong,
        .campus-content strong,
        .interests-content strong,
        .self-evaluation-content strong {
            font-family: 'SourceHanSerifSC-Bold', 'SimSun-Bold', 'SimHei', '黑体' !important;
            color: #000000 !important;
        }
    </style>
</head>
<body>
<div class="resume-template template-3">
    <div class="resume-section">
        <!-- 简历头部 -->
        <div class="header-content">
            <!-- 姓名单独一行 -->
            <div class="resume-title">
                ${name}
            </div>

            <!-- 个人信息和头像区域 -->
            <div class="info-photo-section">
                <div class="basic-info-section">
                    <!-- 左列信息 -->
                    <div class="info-column">
                        <div class="info-item">
                            <span class="info-label">年龄：</span>
                            <span class="info-value">${age}</span>
                            <span class="info-label">&nbsp;&nbsp;电话：</span>
                            <span class="info-value">${phone}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">邮箱：</span>
                            <span class="info-value">${email}</span>
                        </div>
                    </div>

                    <!-- 右列信息 -->
                    <div class="info-column">

                        <div class="info-item">
                            <span class="info-label">性别：</span>
                            <span class="info-value">${gender}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">求职意向：</span>
                            <span class="info-value">${jobObjective}</span>
                        </div>
                    </div>
                </div>

                <!-- 头像容器 -->
                <div class="photo-container">
                    ${avatar}
                </div>
            </div>
        </div>
    </div>

    <!-- 教育经历 -->
    ${education}

    <!-- 工作经验 -->
    ${work}

    <!-- 项目经验 -->
    ${projects}

    <!-- 练手项目 -->
    ${practices}

    <!-- 技能特长 -->
    ${skills}

    <!-- 证书奖项 -->
    ${certificates}

    <!-- 校园经历 -->
    ${campus}

    <!-- 兴趣爱好 -->
    ${interests}

    <!-- 自我评价 -->
    ${selfEvaluation}
</div>
</body>
</html> 