package com.bimowu.resume.common.service;

import com.bimowu.resume.dto.ExtractedStyles;

import java.util.Map;

/**
 * 增强HTML模板生成器接口
 * 负责生成优化的HTML模板用于PDF生成
 */
public interface EnhancedTemplateGenerator {
    
    /**
     * 生成增强的HTML模板
     * @param templateId 模板ID
     * @param data 数据
     * @return HTML内容
     */
    String generateEnhancedTemplate(Long templateId, Map<String, Object> data);

    /**
     * 应用样式到HTML模板
     * @param htmlTemplate HTML模板
     * @param styles 样式信息
     * @return 应用样式后的HTML
     */
    String applyStylesToTemplate(String htmlTemplate, ExtractedStyles styles);

    /**
     * 生成内联样式的HTML
     * @param templateId 模板ID
     * @param data 数据
     * @param styles 样式信息
     * @return 内联样式的HTML
     */
    String generateInlineStyledHTML(Long templateId, Map<String, Object> data, ExtractedStyles styles);
    
    /**
     * 处理响应式模板
     * @param htmlTemplate HTML模板
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 处理后的HTML
     */
    String processResponsiveTemplate(String htmlTemplate, int targetWidth, int targetHeight);
    
    /**
     * 优化HTML用于PDF生成
     * @param html HTML内容
     * @return 优化后的HTML
     */
    String optimizeHTMLForPDF(String html);
    
    /**
     * 验证HTML模板
     * @param html HTML内容
     * @return 是否有效
     */
    boolean validateHTMLTemplate(String html);
    
    /**
     * 合并HTML片段
     * @param htmlFragments HTML片段数组
     * @return 合并后的HTML
     */
    String mergeHTMLFragments(String... htmlFragments);
    
    /**
     * 添加PDF特定的HTML结构
     * @param html HTML内容
     * @return 添加结构后的HTML
     */
    String addPDFSpecificStructure(String html);
    
    /**
     * 处理图片资源
     * @param html HTML内容
     * @param basePath 基础路径
     * @return 处理后的HTML
     */
    String processImageResources(String html, String basePath);
    
    /**
     * 清理HTML内容
     * @param html HTML内容
     * @return 清理后的HTML
     */
    String sanitizeHTML(String html);
    
    /**
     * 生成页面分页标记
     * @param html HTML内容
     * @return 添加分页标记后的HTML
     */
    String addPageBreakMarkers(String html);
    
    /**
     * 处理表格分页
     * @param html HTML内容
     * @return 处理后的HTML
     */
    String processTablePagination(String html);
}