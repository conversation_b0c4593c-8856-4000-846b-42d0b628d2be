package com.bimowu.resume.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 中文字体修复工具
 * 专门用于解决PDF生成中的中文字体显示问题
 */
@Component
@Slf4j
public class ChineseFontFixer {
    
    /**
     * 修复HTML中的中文字体问题（内存优化版本）
     * 
     * @param html 原始HTML内容
     * @return 修复后的HTML内容
     */
    public static String fixChineseFontInHtml(String html) {
        log.info("开始修复HTML中的中文字体问题（内存优化版本）");
        
        if (html == null || html.trim().isEmpty()) {
            log.warn("HTML内容为空，无法修复字体");
            return html;
        }
        
        // 检查内存使用情况
        if (MemoryManager.isMemoryUsageHigh(75)) {
            log.warn("内存使用率过高，使用轻量级字体方案");
            return fixChineseFontInHtmlLightweight(html);
        }
        
        // 检查是否已经包含完整的字体定义（包含PDF生成器优化版本标记）
        if (html.contains("/* 中文字体支持 - PDF生成器优化版本 */")) {
            log.debug("HTML已包含PDF生成器优化的中文字体定义，跳过修复");
            return html;
        }
        
        // 生成强化的中文字体CSS
        String enhancedFontCSS = generateEnhancedChineseFontCSS();
        
        // 查找head标签位置
        int headEndIndex = html.toLowerCase().indexOf("</head>");
        if (headEndIndex == -1) {
            // 如果没有head标签，在html开始处添加
            log.warn("HTML缺少head标签，在开始处添加字体CSS");
            return "<style>" + enhancedFontCSS + "</style>" + html;
        }
        
        // 在head标签结束前插入字体CSS
        String beforeHead = html.substring(0, headEndIndex);
        String afterHead = html.substring(headEndIndex);
        
        String fixedHtml = beforeHead + 
                          "<style type=\"text/css\">\n" + 
                          enhancedFontCSS + 
                          "\n</style>\n" + 
                          afterHead;
        
        log.info("中文字体修复完成，CSS长度: {}", enhancedFontCSS.length());
        return fixedHtml;
    }
    
    /**
     * 轻量级字体修复方案（内存不足时使用）
     */
    private static String fixChineseFontInHtmlLightweight(String html) {
        log.info("使用轻量级字体修复方案");
        
        // 使用优化的字体加载器生成轻量级CSS
        String lightweightCSS = OptimizedFontLoader.generateLightweightFontCSS();
        
        // 查找head标签位置
        int headEndIndex = html.toLowerCase().indexOf("</head>");
        if (headEndIndex == -1) {
            return "<style>" + lightweightCSS + "</style>" + html;
        }
        
        String beforeHead = html.substring(0, headEndIndex);
        String afterHead = html.substring(headEndIndex);
        
        return beforeHead + 
               "<style type=\"text/css\">\n" + 
               lightweightCSS + 
               "\n</style>\n" + 
               afterHead;
    }
    
    /**
     * 生成增强的中文字体CSS（PDF生成器兼容版本）
     */
    private static String generateEnhancedChineseFontCSS() {
        StringBuilder css = new StringBuilder();
        
        log.info("生成PDF兼容的中文字体CSS方案");
        
        // 使用系统字体的强化方案，专门针对PDF生成器优化
        css.append("/* 中文字体支持 - PDF生成器优化版本 */\n");
        
        // 不使用@font-face，直接使用系统字体名称
        // PDF生成器更容易识别系统字体
        css.append("* {\n")
           .append("  font-family: ");
        
        // 根据操作系统选择最佳字体
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            css.append("'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体'");
        } else if (os.contains("mac")) {
            css.append("'STSong', 'STHeiti', 'PingFang SC', 'Hiragino Sans GB'");
        } else {
            css.append("'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN'");
        }
        
        css.append(", 'Arial Unicode MS', Arial, sans-serif !important;\n")
           .append("  font-size: 14px;\n")
           .append("  font-weight: normal;\n")
           .append("}\n");
        
        // 添加更具体的选择器，确保所有元素都应用中文字体
        css.append("body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {\n")
           .append("  font-family: inherit !important;\n")
           .append("}\n");
        
        // 特别针对中文字符的样式
        css.append("body {\n")
           .append("  text-rendering: auto;\n")  // PDF生成器兼容性更好
           .append("  -webkit-font-smoothing: auto;\n")
           .append("}\n");
        
        // 添加打印媒体查询，确保PDF生成时字体正确
        css.append("@media print {\n")
           .append("  * {\n")
           .append("    font-family: ");
        
        if (os.contains("windows")) {
            css.append("'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑'");
        } else if (os.contains("mac")) {
            css.append("'STSong', 'STHeiti'");
        } else {
            css.append("'WenQuanYi Micro Hei', 'Noto Sans CJK SC'");
        }
        
        css.append(", Arial, sans-serif !important;\n")
           .append("  }\n")
           .append("}\n");
        
        log.info("PDF兼容中文字体CSS生成完成，长度: {} 字符", css.length());
        return css.toString();
    }
    
    /**
     * 验证HTML中是否包含中文字符
     */
    public static boolean containsChineseCharacters(String html) {
        if (html == null) return false;
        
        // 检查是否包含中文字符（Unicode范围：\u4e00-\u9fff）
        return html.matches(".*[\\u4e00-\\u9fff].*");
    }
    
    /**
     * 提取HTML中的中文字符进行分析
     */
    public static String extractChineseCharacters(String html) {
        if (html == null) return "";
        
        StringBuilder chineseChars = new StringBuilder();
        for (char c : html.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseChars.append(c);
            }
        }
        
        return chineseChars.toString();
    }
    
    /**
     * 生成字体测试HTML
     */
    public static String generateFontTestHtml() {
        String testText = "中文字体测试：你好世界！Hello World! 123456";
        String enhancedCSS = generateEnhancedChineseFontCSS();
        
        return "<!DOCTYPE html>\n" +
               "<html>\n" +
               "<head>\n" +
               "    <meta charset=\"UTF-8\">\n" +
               "    <title>字体测试</title>\n" +
               "    <style>\n" +
               enhancedCSS +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <h1>字体测试页面</h1>\n" +
               "    <p>" + testText + "</p>\n" +
               "    <div>测试内容：</div>\n" +
               "    <ul>\n" +
               "        <li>姓名：张三</li>\n" +
               "        <li>电话：13800138000</li>\n" +
               "        <li>邮箱：<EMAIL></li>\n" +
               "        <li>地址：北京市朝阳区</li>\n" +
               "    </ul>\n" +
               "</body>\n" +
               "</html>";
    }
}