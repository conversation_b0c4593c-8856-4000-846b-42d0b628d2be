package com.bimowu.resume.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 内存管理工具类
 * 用于监控和管理PDF生成过程中的内存使用
 */
@Component
@Slf4j
public class MemoryManager {
    
    private static final long MB = 1024 * 1024;
    
    /**
     * 检查内存使用情况
     */
    public static MemoryInfo getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        
        MemoryInfo info = new MemoryInfo();
        info.setTotalMemory(totalMemory);
        info.setFreeMemory(freeMemory);
        info.setMaxMemory(maxMemory);
        info.setUsedMemory(usedMemory);
        info.setUsagePercent((double) usedMemory / maxMemory * 100);
        
        return info;
    }
    
    /**
     * 检查是否有足够内存进行PDF生成
     */
    public static boolean hasEnoughMemory(long requiredMemory) {
        MemoryInfo info = getMemoryInfo();
        long availableMemory = info.getMaxMemory() - info.getUsedMemory();
        
        log.debug("内存检查 - 需要: {}MB, 可用: {}MB, 使用率: {:.1f}%", 
            requiredMemory / MB, availableMemory / MB, info.getUsagePercent());
        
        return availableMemory > requiredMemory;
    }
    
    /**
     * 强制垃圾回收
     */
    public static void forceGC() {
        MemoryInfo beforeGC = getMemoryInfo();
        
        System.gc();
        System.runFinalization();
        
        // 等待GC完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        MemoryInfo afterGC = getMemoryInfo();
        
        long freedMemory = beforeGC.getUsedMemory() - afterGC.getUsedMemory();
        log.info("强制GC完成 - 释放内存: {}MB, GC前使用率: {:.1f}%, GC后使用率: {:.1f}%", 
            freedMemory / MB, beforeGC.getUsagePercent(), afterGC.getUsagePercent());
    }
    
    /**
     * 检查内存使用率是否过高
     */
    public static boolean isMemoryUsageHigh(double threshold) {
        MemoryInfo info = getMemoryInfo();
        return info.getUsagePercent() > threshold;
    }
    
    /**
     * 记录内存使用情况
     */
    public static void logMemoryUsage(String operation) {
        MemoryInfo info = getMemoryInfo();
        log.info("内存使用情况 [{}] - 已用: {}MB, 可用: {}MB, 最大: {}MB, 使用率: {}%",
            operation,
            info.getUsedMemory() / MB,
            info.getFreeMemory() / MB,
            info.getMaxMemory() / MB,
            info.getUsagePercent());
    }
    
    /**
     * 在PDF生成前检查内存
     */
    public static boolean checkMemoryBeforePDFGeneration() {
        return checkMemoryBeforePDFGeneration(100 * MB); // 默认预估需要100MB
    }
    
    /**
     * 在PDF生成前检查内存（指定所需内存）
     */
    public static boolean checkMemoryBeforePDFGeneration(long estimatedMemoryNeeded) {
        MemoryInfo info = getMemoryInfo();
        
        log.info("PDF生成前内存检查 - 当前使用率: {:.1f}%, 预估需要: {}MB", 
            info.getUsagePercent(), estimatedMemoryNeeded / MB);
        
        // 如果内存使用率超过75%，先执行GC
        if (info.getUsagePercent() > 75) {
            log.warn("内存使用率过高: {:.1f}%，执行GC", info.getUsagePercent());
            forceGC();
            
            // 重新检查
            info = getMemoryInfo();
            if (info.getUsagePercent() > 80) {
                log.error("GC后内存使用率仍然过高: {:.1f}%，可能导致OutOfMemoryError", info.getUsagePercent());
                return false;
            }
        }
        
        // 检查是否有足够内存
        if (!hasEnoughMemory(estimatedMemoryNeeded)) {
            log.error("可用内存不足，无法进行PDF生成 - 需要: {}MB, 可用: {}MB", 
                estimatedMemoryNeeded / MB, (info.getMaxMemory() - info.getUsedMemory()) / MB);
            return false;
        }
        
        log.info("内存检查通过，可以进行PDF生成 - 使用率: {:.1f}%", info.getUsagePercent());
        return true;
    }
    
    /**
     * 抛出内存不足异常
     */
    public static void throwMemoryInsufficientException(String operation, long requiredMemory) {
        MemoryInfo info = getMemoryInfo();
        long availableMemory = info.getMaxMemory() - info.getUsedMemory();
        
        String message = String.format("执行%s时内存不足", operation);
        throw new com.bimowu.resume.exception.MemoryInsufficientException(
            message, requiredMemory, availableMemory, info.getUsagePercent());
    }
    
    /**
     * 内存信息类
     */
    public static class MemoryInfo {
        private long totalMemory;
        private long freeMemory;
        private long maxMemory;
        private long usedMemory;
        private double usagePercent;
        
        // Getters and Setters
        public long getTotalMemory() { return totalMemory; }
        public void setTotalMemory(long totalMemory) { this.totalMemory = totalMemory; }
        
        public long getFreeMemory() { return freeMemory; }
        public void setFreeMemory(long freeMemory) { this.freeMemory = freeMemory; }
        
        public long getMaxMemory() { return maxMemory; }
        public void setMaxMemory(long maxMemory) { this.maxMemory = maxMemory; }
        
        public long getUsedMemory() { return usedMemory; }
        public void setUsedMemory(long usedMemory) { this.usedMemory = usedMemory; }
        
        public double getUsagePercent() { return usagePercent; }
        public void setUsagePercent(double usagePercent) { this.usagePercent = usagePercent; }
    }
}