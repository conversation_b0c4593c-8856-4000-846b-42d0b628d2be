package com.bimowu.resume.common.service;

import com.bimowu.resume.dto.ExtractedStyles;

/**
 * 样式提取服务接口
 * 负责从Vue模板中提取CSS样式和布局信息
 */
public interface StyleExtractorService {
    
    /**
     * 从模板ID提取样式
     * @param templateId 模板ID
     * @return 提取的样式信息
     */
    ExtractedStyles extractStyles(int templateId);
    
    /**
     * 从Vue文件内容提取样式
     * @param vueContent Vue文件内容
     * @param templateId 模板ID
     * @return 提取的样式信息
     */
    ExtractedStyles extractStylesFromVueContent(String vueContent, int templateId);
    
    /**
     * 从CSS内容提取样式信息
     * @param cssContent CSS内容
     * @return 提取的样式信息
     */
    ExtractedStyles extractStylesFromCSS(String cssContent);
    
    /**
     * 验证提取的样式
     * @param styles 样式信息
     * @return 是否有效
     */
    boolean validateExtractedStyles(ExtractedStyles styles);
    
    /**
     * 合并多个样式信息
     * @param stylesList 样式信息列表
     * @return 合并后的样式信息
     */
    ExtractedStyles mergeStyles(ExtractedStyles... stylesList);
}