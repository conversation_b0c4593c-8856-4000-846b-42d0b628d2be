package com.bimowu.resume.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeWork;
import com.bimowu.resume.vo.ResumeWorkVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 工作经验表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeWorkMapper extends BaseMapper<ResumeWork> {

    List<ResumeWorkVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
