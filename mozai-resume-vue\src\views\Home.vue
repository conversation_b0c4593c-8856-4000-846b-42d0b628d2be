<template>
  <MainLayout>
    <div class="home-page">
      <!-- 英雄区域 -->
      <section class="hero">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title">专业简历制作服务平台</h1>
            <p class="hero-subtitle">超多简历模板 · 10分钟制作简历 · 服务超900万用户</p>
            <div class="hero-actions">
              <router-link to="/templates" class="btn btn-primary">免费制作简历</router-link>
              <router-link to="/user" class="btn btn-secondary">我的简历</router-link>
            </div>
            <div class="hero-features">
              <div class="feature">
                <el-icon><Document /></el-icon>
                <span>自动排版</span>
                <p>输入内容，自动排版生成简历</p>
              </div>
              <div class="feature">
                <el-icon><Brush /></el-icon>
                <span>风格定制</span>
                <p>支持调色盘，万种皮肤随心换</p>
              </div>
              <div class="feature">
                <el-icon><Lock /></el-icon>
                <span>隐私安全</span>
                <p>简历加密，零骚扰</p>
              </div>
              <div class="feature">
                <el-icon><Upload /></el-icon>
                <span>云端保存</span>
                <p>电脑、手机随时编辑下载</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 推荐模板区域 -->
      <section class="section templates-section">
        <div class="container">
          <h2 class="section-title">简历模板推荐</h2>

          <div class="template-list">
            <div v-for="template in templates" :key="template.id" class="template-item">
              <div class="template-card">
                <div class="template-image">
                  <img :src="template.thumbnail" :alt="template.name" />
                </div>
                <div class="template-info">
                  <h3>{{ template.name }}</h3>
                  <div class="template-features">
                    <span>支持：</span>
                    <span class="feature-item">7种语言</span>
                    <span class="feature-item">16种颜色</span>
                    <span class="feature-item">封面</span>
                    <span class="feature-item">自荐信</span>
                  </div>
                  <router-link :to="`/editor?template=${template.id}`" class="use-template-btn">立即使用</router-link>
                </div>
              </div>
            </div>
          </div>

          <div class="more-templates">
            <router-link to="/templates" class="btn btn-secondary">更多简历模板</router-link>
          </div>
        </div>
      </section>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import { Document, Brush, Lock, Upload } from '@element-plus/icons-vue'
import { useResumeStore } from '@/stores/resume'

const resumeStore = useResumeStore()
// 只显示前6个模板
const templates = ref(resumeStore.templates.slice(0, 6))
</script>

<style scoped>
.hero {
  background-color: #f0f7ff;
  background-image: linear-gradient(135deg, #f0f7ff 0%, #e8f5fe 100%);
  padding: 90px 0;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hero-title {
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 18px;
  color: #303133;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
}

.hero-subtitle {
  font-size: 18px;
  color: #606266;
  margin-bottom: 35px;
}

.hero-actions {
  margin-bottom: 55px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.hero-actions .btn {
  padding: 14px 32px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.hero-actions .btn-primary {
  background-color: #1e88e5;
  color: white;
  box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.hero-actions .btn-primary:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(30, 136, 229, 0.4);
}

.hero-actions .btn-secondary {
  background-color: #fff;
  color: #1e88e5;
  border: 2px solid #1e88e5;
}

.hero-actions .btn-secondary:hover {
  background-color: rgba(30, 136, 229, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.hero-features {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 45px;
  max-width: 900px;
  margin: 0 auto;
}

.hero-features .feature {
  text-align: center;
}

.hero-features .feature .el-icon {
  font-size: 35px;
  color: #1e88e5;
  margin-bottom: 12px;
}

.hero-features .feature span {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #303133;
}

.hero-features .feature p {
  font-size: 14px;
  color: #606266;
}

.section {
  padding: 70px 0;
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 40px;
  text-align: center;
  color: #303133;
  position: relative;
  display: inline-block;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #1e88e5;
  border-radius: 3px;
}

.templates-section {
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 35px;
  margin-bottom: 40px;
}

.template-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  border: 1px solid #eaecef;
}

.template-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 22px rgba(0, 0, 0, 0.12);
}

.template-image {
  height: 380px;
  overflow: hidden;
}

.template-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s;
  background-color: #f8f9fa;
  padding: 10px;
}

.template-card:hover .template-image img {
  transform: scale(1.02);
}

.template-info {
  padding: 16px;
  border-top: 1px solid #eaecef;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #303133;
}

.template-features {
  font-size: 12px;
  color: #909399;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.feature-item {
  margin-right: 8px;
  color: #1e88e5;
  background: rgba(30, 136, 229, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.use-template-btn {
  display: block;
  text-align: center;
  padding: 10px 0;
  margin-top: 12px;
  background-color: #1e88e5;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
  font-weight: 500;
}

.use-template-btn:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(30, 136, 229, 0.3);
}

.more-templates {
  text-align: center;
  margin-top: 25px;
}

.btn {
  display: inline-block;
  text-decoration: none;
  padding: 10px 25px;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 500;
}

.btn-secondary {
  background-color: #fff;
  color: #1e88e5;
  border: 1px solid #1e88e5;
}

.btn-secondary:hover {
  background-color: rgba(30, 136, 229, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .hero {
    padding: 70px 0;
  }

  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .hero-features {
    gap: 25px;
  }

  .template-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
  }

  .section {
    padding: 50px 0;
  }

  .section-title {
    font-size: 26px;
  }

  .template-image {
    height: 340px;
  }
}
</style> 