package com.bimowu.resume.common.service.impl;

import com.bimowu.resume.common.service.FontManager;
import com.bimowu.resume.common.service.ResourceResolver;
import com.bimowu.resume.config.FontConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字体管理器实现
 */
@Slf4j
@Service
public class FontManagerImpl implements FontManager {
    
    @Autowired
    private FontConfig fontConfig;
    
    @Autowired
    private ResourceResolver resourceResolver;
    
    // 字体缓存
    private final Map<String, byte[]> fontCache = new ConcurrentHashMap<>();
    
    // 字体信息缓存
    private final Map<String, FontConfig.FontInfo> fontInfoCache = new ConcurrentHashMap<>();
    
    // 字体族映射
    private final Map<String, String> familyMapping = new ConcurrentHashMap<>();
    
    @PostConstruct
    @Override
    public void initialize() {
        log.info("初始化字体管理器...【修复版本】");
        
        // 初始化默认字体族映射
        initializeDefaultFamilyMapping();
        
        // 加载配置的字体
        loadConfiguredFonts();
        
        log.info("字体管理器初始化完成，共加载 {} 个字体", fontCache.size());
    }
    
    @Override
    public boolean loadFont(FontConfig.FontInfo fontInfo) {
        if (fontInfo == null || !StringUtils.hasText(fontInfo.getName())) {
            log.warn("字体信息无效");
            return false;
        }
        
        try {
            String fontPath = fontInfo.getPath();
            InputStream fontStream = resourceResolver.resolveFont(fontPath);
            
            if (fontStream == null) {
                log.warn("无法加载字体文件: {}", fontPath);
                return false;
            }
            
            // 检查内存使用情况，避免加载大字体文件
            Runtime runtime = Runtime.getRuntime();
            long freeMemory = runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double)(maxMemory - freeMemory) / maxMemory * 100;

            // 暂时禁用内存检查以确保字体能够加载
            if (false && memoryUsage > 99.5) {
                log.warn("内存使用率过高 ({:.1f}%)，跳过字体文件加载: {}", memoryUsage, fontInfo.getName());
                fontStream.close();

                // 仅记录字体信息，不缓存数据
                fontInfoCache.put(fontInfo.getName(), fontInfo);
                if (StringUtils.hasText(fontInfo.getFamily())) {
                    familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
                }

                log.info("字体信息已记录（未缓存数据）: {} ({})", fontInfo.getName(), fontInfo.getFamily());
                return true;
            }

            log.info("当前内存使用率: {:.1f}%，继续加载字体: {}", memoryUsage, fontInfo.getName());
            
            // 检查可用字节数，避免读取过大的文件
            int availableBytes = fontStream.available();
            if (availableBytes > 50 * 1024 * 1024) { // 50MB限制
                log.warn("字体文件过大 ({}MB)，跳过缓存: {}", availableBytes / (1024 * 1024), fontInfo.getName());
                fontStream.close();
                
                // 仅记录字体信息，不缓存数据
                fontInfoCache.put(fontInfo.getName(), fontInfo);
                if (StringUtils.hasText(fontInfo.getFamily())) {
                    familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
                }
                
                log.info("字体信息已记录（文件过大未缓存）: {} ({})", fontInfo.getName(), fontInfo.getFamily());
                return true;
            }
            
            // 读取字体数据（仅对小文件）
            byte[] fontData = null;
            try {
                fontData = IOUtils.toByteArray(fontStream);
            } catch (OutOfMemoryError e) {
                log.error("读取字体文件时内存不足: {}", fontInfo.getName(), e);
                fontStream.close();
                
                // 仅记录字体信息，不缓存数据
                fontInfoCache.put(fontInfo.getName(), fontInfo);
                if (StringUtils.hasText(fontInfo.getFamily())) {
                    familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
                }
                
                return true;
            } finally {
                fontStream.close();
            }
            
            if (fontData == null) {
                log.warn("无法读取字体数据: {}", fontInfo.getName());
                return false;
            }
            
            // 验证字体（仅对缓存的数据）
            FontValidationResult validation = validateFontData(fontData);
            if (!validation.isValid()) {
                log.warn("字体验证失败: {} - {}", fontInfo.getName(), validation.getErrorMessage());
                // 即使验证失败，也记录字体信息
                fontInfoCache.put(fontInfo.getName(), fontInfo);
                if (StringUtils.hasText(fontInfo.getFamily())) {
                    familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
                }
                return true; // 返回true，允许使用字体路径
            }
            
            // 缓存字体数据
            fontCache.put(fontInfo.getName(), fontData);
            fontInfoCache.put(fontInfo.getName(), fontInfo);
            
            // 添加字体族映射
            if (StringUtils.hasText(fontInfo.getFamily())) {
                familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
            }
            
            log.info("成功加载并缓存字体: {} ({}) - 大小: {}KB", 
                fontInfo.getName(), fontInfo.getFamily(), fontData.length / 1024);
            return true;

        } catch (IOException e) {
            log.error("加载字体失败: {}", fontInfo.getName(), e);
            return false;
        } catch (OutOfMemoryError e) {
            log.error("加载字体时内存不足: {}", fontInfo.getName(), e);
            
            // 强制GC
            System.gc();
            
            // 仅记录字体信息
            fontInfoCache.put(fontInfo.getName(), fontInfo);
            if (StringUtils.hasText(fontInfo.getFamily())) {
                familyMapping.put(fontInfo.getFamily(), fontInfo.getName());
            }
            
            return true;
        }
    }
    
    @Override
    @Cacheable(value = "fontStreams", key = "#fontName")
    public InputStream getFontStream(String fontName) {
        // 首先尝试从缓存获取
        byte[] fontData = fontCache.get(fontName);
        if (fontData != null) {
            return new ByteArrayInputStream(fontData);
        }
        
        // 如果缓存中没有，但有字体信息，尝试直接从文件读取
        FontConfig.FontInfo fontInfo = fontInfoCache.get(fontName);
        if (fontInfo != null) {
            try {
                InputStream fontStream = resourceResolver.resolveFont(fontInfo.getPath());
                if (fontStream != null) {
                    log.debug("直接从文件读取字体流: {}", fontName);
                    return fontStream;
                }
            } catch (Exception e) {
                log.warn("直接读取字体文件失败: {}", fontName, e);
            }
        }
        
        log.warn("字体不存在: {}", fontName);
        return null;
    }
    
    @Override
    @Cacheable(value = "fontBytes", key = "#fontName")
    public byte[] getFontData(String fontName) {
        return fontCache.get(fontName);
    }
    
    @Override
    public boolean isFontAvailable(String fontName) {
        return fontCache.containsKey(fontName);
    }
    
    @Override
    public String[] getAvailableFonts() {
        return fontCache.keySet().toArray(new String[0]);
    }
    
    @Override
    public String getFontNameByFamily(String fontFamily) {
        String fontName = familyMapping.get(fontFamily);
        if (fontName != null) {
            return fontName;
        }
        
        // 如果没有找到，返回默认字体
        return getDefaultFontName();
    }
    
    @Override
    public String getDefaultFontName() {
        return fontConfig.getDefaultFamily();
    }
    
    @Override
    @CacheEvict(value = {"fontStreams", "fontBytes"}, allEntries = true)
    public void clearCache() {
        fontCache.clear();
        fontInfoCache.clear();
        log.info("字体缓存已清除");
    }
    
    @Override
    public void reloadFonts() {
        clearCache();
        familyMapping.clear();
        
        // 重新初始化
        initializeDefaultFamilyMapping();
        loadConfiguredFonts();
        
        log.info("字体已重新加载");
    }
    
    @Override
    public FontConfig.FontInfo getFontInfo(String fontName) {
        return fontInfoCache.get(fontName);
    }
    
    @Override
    public FontValidationResult validateFont(String fontPath) {
        try {
            InputStream fontStream = resourceResolver.resolveFont(fontPath);
            if (fontStream == null) {
                FontValidationResult result = new FontValidationResult(false);
                result.setErrorMessage("字体文件不存在: " + fontPath);
                return result;
            }
            
            // 检查文件大小，避免加载过大的文件
            int availableBytes = fontStream.available();
            if (availableBytes > 100 * 1024 * 1024) { // 100MB限制
                fontStream.close();
                FontValidationResult result = new FontValidationResult(false);
                result.setErrorMessage("字体文件过大: " + (availableBytes / (1024 * 1024)) + "MB");
                return result;
            }
            
            // 只读取文件头进行基本验证，不读取整个文件
            byte[] header = new byte[12];
            int bytesRead = fontStream.read(header);
            fontStream.close();
            
            if (bytesRead < 4) {
                FontValidationResult result = new FontValidationResult(false);
                result.setErrorMessage("字体文件太小或无法读取");
                return result;
            }
            
            return validateFontHeader(header);
            
        } catch (IOException e) {
            FontValidationResult result = new FontValidationResult(false);
            result.setErrorMessage("读取字体文件失败: " + e.getMessage());
            return result;
        } catch (OutOfMemoryError e) {
            FontValidationResult result = new FontValidationResult(false);
            result.setErrorMessage("内存不足，无法验证字体文件");
            return result;
        }
    }
    
    /**
     * 验证字体文件头
     */
    private FontValidationResult validateFontHeader(byte[] header) {
        FontValidationResult result = new FontValidationResult(true);
        
        if (header == null || header.length < 4) {
            result.setValid(false);
            result.setErrorMessage("字体文件头无效");
            return result;
        }
        
        // 检查TTF/OTF文件头
        String headerStr = new String(Arrays.copyOf(header, 4));
        boolean isValidFont = headerStr.equals("OTTO") || 
                             headerStr.equals("true") || 
                             headerStr.equals("\0\1\0\0") ||
                             (header[0] == 't' && header[1] == 't' && header[2] == 'c' && header[3] == 'f');
        
        if (!isValidFont) {
            result.setValid(false);
            result.setErrorMessage("不支持的字体格式");
            return result;
        }
        
        // 设置基本信息
        result.setFontName("Unknown");
        result.setFontFamily("Unknown");
        result.setSupportedCharsets(new String[]{"UTF-8", "GBK"});
        
        return result;
    }
    
    /**
     * 初始化默认字体族映射
     */
    private void initializeDefaultFamilyMapping() {
        log.info("初始化默认字体族映射");
        // 中文字体映射
        familyMapping.put("SimSun", "simsun");
        familyMapping.put("宋体", "simsun");
        familyMapping.put("Microsoft YaHei", "simhei");
        familyMapping.put("微软雅黑", "simhei");
        familyMapping.put("SimHei", "simhei");
        familyMapping.put("黑体", "simhei");

        // 英文字体映射
        familyMapping.put("Arial", "simsun");
        familyMapping.put("Times New Roman", "simsun");
        familyMapping.put("serif", "simsun");
        familyMapping.put("sans-serif", "simsun");
        familyMapping.put("monospace", "simsun");

        // 添加用户自定义映射
        if (fontConfig.getAliases() != null) {
            familyMapping.putAll(fontConfig.getAliases());
        }
    }
    
    /**
     * 加载配置的字体
     */
    private void loadConfiguredFonts() {
        if (fontConfig.getFonts() == null) {
            log.warn("没有配置字体信息");
            return;
        }
        
        for (FontConfig.FontInfo fontInfo : fontConfig.getFonts()) {
            loadFont(fontInfo);
        }
    }
    
    /**
     * 验证字体数据
     */
    private FontValidationResult validateFontData(byte[] fontData) {
        FontValidationResult result = new FontValidationResult(true);
        
        if (fontData == null || fontData.length == 0) {
            result.setValid(false);
            result.setErrorMessage("字体数据为空");
            return result;
        }
        
        // 检查字体文件头
        if (fontData.length < 4) {
            result.setValid(false);
            result.setErrorMessage("字体文件太小");
            return result;
        }
        
        // 检查TTF/OTF文件头
        String header = new String(Arrays.copyOf(fontData, 4));
        if (!header.equals("OTTO") && !header.equals("true") && !header.equals("\0\1\0\0")) {
            // 检查是否是TTC文件
            if (fontData.length >= 4) {
                byte[] ttcHeader = Arrays.copyOf(fontData, 4);
                if (!(ttcHeader[0] == 't' && ttcHeader[1] == 't' && ttcHeader[2] == 'c' && ttcHeader[3] == 'f')) {
                    result.setValid(false);
                    result.setErrorMessage("不支持的字体格式");
                    return result;
                }
            }
        }
        
        // 设置基本信息
        result.setFontName("Unknown");
        result.setFontFamily("Unknown");
        result.setSupportedCharsets(new String[]{"UTF-8", "GBK"});
        
        return result;
    }
}