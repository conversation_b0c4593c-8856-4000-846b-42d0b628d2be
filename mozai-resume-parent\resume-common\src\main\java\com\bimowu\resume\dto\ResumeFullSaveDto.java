package com.bimowu.resume.dto;

import com.bimowu.resume.entity.Resume;
import com.bimowu.resume.vo.*;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
/**保存或更新简历*/
@Data
public class ResumeFullSaveDto {
    private ResumeVo resumeVo;
    private ResumeInformationVo information; // 基本信息
    private List<ResumeEducationalVo> educationList; // 教育经历
    private List<ResumeWorkVo> workList; // 工作经验
    private List<ResumeProjectExperienceVo> projectList; // 项目经验
    private List<ResumePracticeVo> practiceList; // 练手项目
    private List<ResumeTalentVo> talentList; // 技能特长
    private ResumeCertificateVo certificate; // 证书奖项
    private ResumeCampusVo campus; // 校园经历
    private ResumeInterestVo interest; // 兴趣爱好
    private ResumeEvaluateVo evaluate; // 自我评价
    // 你可以根据实际表结构和字段名调整Vo类名
}