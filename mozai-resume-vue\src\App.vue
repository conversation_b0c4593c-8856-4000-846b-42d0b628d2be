<template>
  <router-view />
</template>

<script setup>
import { useUserStore } from './stores/user'
import { onMounted, onBeforeMount } from 'vue'

const userStore = useUserStore()

// 应用启动前获取用户信息
onBeforeMount(() => {
  console.log('App.vue - onBeforeMount')
  console.log('当前token:', userStore.token)
  console.log('当前userInfo:', userStore.userInfo)
})

// 应用启动时获取用户信息
onMounted(async () => {
  console.log('App.vue - onMounted')
  
  // 不管是否有token，都调用后端获取用户信息，触发SSO状态校验
  try {
    console.log('App.vue - 调用后端获取用户信息，触发SSO校验')
    const userInfo = await userStore.fetchUserInfo()
    console.log('App.vue - 获取到用户信息:', userInfo)
    if (userInfo) {
      console.log('App.vue - 当前nickname:', userStore.nickname)
    }
  } catch (error) {
    console.error('App.vue - 获取用户信息失败:', error)
  }
})
</script>

<style>
:root {
  --primary-color: #1e88e5;
  --primary-light: #42a5f5;
  --primary-dark: #1976d2;
  --primary-bg: rgba(30, 136, 229, 0.1);
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --text-color: #303133;
  --text-secondary: #606266;
  --text-light: #909399;
  --text-lighter: #c0c4cc;
  --border-color: #eaecef;
  --bg-color: #f8fafc;
  --white: #ffffff;
  --box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
  background-color: var(--white);
  font-size: 14px;
  line-height: 1.5;
  scroll-behavior: smooth;
}

a {
  text-decoration: none;
  color: inherit;
}

ul, li {
  list-style: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section {
  padding: 70px 0;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  text-decoration: none;
  padding: 10px 25px;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(30, 136, 229, 0.4);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .section {
    padding: 50px 0;
  }
}
</style> 