<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .resume-template {
            width: 100%;
            font-family: inherit !important;
            color: #333;
            background-color: #fff;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* 头部个人信息样式 */
        .header-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #2e8b57;
        }
        
        .personal-info {
            flex: 1;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .info-item {
            flex: 1;
            min-width: 0;
        }
        
        .label {
            font-weight: bold;
            color: #2e8b57;
        }
        
        .avatar-container {
            width: 120px;
            height: 160px;
            margin-left: 20px;
        }
        
        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 1px solid #ccc;
        }
        
        /* 内容区域布局 */
        .content-section {
            display: flex;
            gap: 20px;
        }
        
        .left-column {
            flex: 1;
            padding-right: 10px;
        }
        
        .right-column {
            flex: 1;
            padding-left: 10px;
            border-left: 1px solid #ddd;
        }
        
        /* 各部分通用样式 */
        .section {
            margin-bottom: 25px;
        }
        
        .section-header {
            margin-bottom: 15px;
            border-bottom: 2px solid #2e8b57;
        }
        
        .section-header h2 {
            display: inline-block;
            margin: 0;
            padding: 5px 0;
            font-size: 16px;
            font-weight: bold;
            color: #2e8b57;
        }
        
        .section-content {
            padding: 0 5px;
        }
        
        /* 教育经历样式 */
        .education-item {
            margin-bottom: 15px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .item-school, .item-company, .item-name {
            font-weight: bold;
        }
        
        .item-date {
            color: #666;
        }
        
        .item-details {
            margin-left: 10px;
        }
        
        .item-major, .item-degree {
            margin-bottom: 5px;
        }
        
        /* 技能列表样式 */
        .skills-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .skill-item {
            margin-bottom: 8px;
            line-height: 1.7;
        }
        
        /* 工作经历样式 */
        .work-item {
            margin-bottom: 20px;
        }
        
        .item-position {
            margin: 5px 0 10px 0;
            font-weight: bold;
            color: #555;
        }
        
        /* 项目经验样式 */
        .project-item {
            margin-bottom: 20px;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .project-title {
            font-weight: bold;
        }
        
        .project-date {
            color: #666;
        }
        
        .project-role {
            margin: 5px 0 10px 0;
            font-style: italic;
            color: #555;
        }
        
        /* 描述内容样式 */
        .project-description {
            margin-top: 8px;
        }
        
        .description-line {
            margin-bottom: 5px;
            line-height: 1.7;
        }
        
        /* 校园经历和自我评价样式 */
        .campus-content, .evaluation-content {
            line-height: 1.7;
        }
        
        .text-line {
            margin-bottom: 8px;
        }
        
        /* 样式覆盖 */
        p {
            margin: 0 0 8px 0;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        strong {
            font-weight: bold;
        }
        
        em {
            font-style: italic;
        }
        
        h1 {
            font-size: 1.5em;
            margin: 0.5em 0;
        }
        
        h2 {
            font-size: 1.3em;
            margin: 0.5em 0;
        }
        
        h3 {
            font-size: 1.1em;
            margin: 0.5em 0;
        }
    </style>
</head>
<body>
    <div class="resume-template template-6">
        <div class="resume-container">
            <!-- 个人信息部分 -->
            <div class="header-section">
                <div class="personal-info">
                    <div class="info-row">
                        <div class="info-item"><span class="label">姓名：</span>${name}</div>
                        <div class="info-item"><span class="label">性别：</span>${gender}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-item"><span class="label">年龄：</span>${age}</div>
                        <div class="info-item"><span class="label">电话：</span>${phone}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-item"><span class="label">邮箱：</span>${email}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-item"><span class="label">求职意向：</span>${jobObjective}</div>
                    </div>
                </div>
                <div class="avatar-container">
                    ${avatar}
                </div>
            </div>

            <div class="content-section">
                <!-- 左侧栏 -->
                <div class="left-column">
                    <!-- 教育经历 -->
                    ${educationLeft}
                    
                    <!-- 专业技能 -->
                    ${skillsLeft}
                    
                    <!-- 实习经历 -->
                    ${workLeft}
                </div>

                <!-- 右侧栏 -->
                <div class="right-column">
                    <!-- 项目经验 -->
                    ${projectsRight}
                    
                    <!-- 练手项目 -->
                    ${practicesRight}
                    
                    <!-- 校园经历 -->
                    ${campusRight}
                    
                    <!-- 自我评价 -->
                    ${evaluationRight}
                </div>
            </div>
        </div>
    </div>
</body>
</html> 