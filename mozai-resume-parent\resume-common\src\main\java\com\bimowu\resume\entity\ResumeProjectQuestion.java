package com.bimowu.resume.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目问题表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume_project_question")
public class ResumeProjectQuestion implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "que_id", type = IdType.AUTO)
    private Long queId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 问题顺序
     */
    private Integer questionOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
