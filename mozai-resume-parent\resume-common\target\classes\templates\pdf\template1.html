<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.5;
            background: #fafafa;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }
        
        .resume-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
            box-sizing: border-box;
        }
        
        .header {
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            color: white;
            padding: 25px 30px;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 80px;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 2px;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: flex-end;
        }

        .contact-row {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: white;
        }
        
        .section {
            margin-bottom: 25px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-left: 30px;
            margin-right: 30px;
        }

        .section-title {
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 8px;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }
        
        .item {
            margin-bottom: 0;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .item:last-child {
            border-bottom: none;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            align-items: center;
        }

        .item-title {
            font-weight: bold;
            color: #333;
            font-size: 16px;
            flex: 1;
            text-align: center;
        }

        .item-date {
            color: #666;
            font-weight: bold;
            font-size: 14px;
            min-width: 140px;
        }

        .item-subtitle {
            margin-bottom: 12px;
            font-weight: bold;
            color: #4A90E2;
            font-size: 14px;
            min-width: 120px;
            text-align: right;
        }

        .item-content {
            text-align: justify;
            font-size: 14px;
            line-height: 1.8;
            color: #555;
        }
        
        ul {
            margin-top: 5px;
            margin-bottom: 5px;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 3px;
        }
        
        .skill-item {
            margin-bottom: 8px;
        }
        
        .skill-name {
            font-weight: bold;
        }
        
        .certificate-list,
        .campus-experience,
        .interests,
        .self-evaluation {
            text-align: justify;
            font-size: 14px;
            line-height: 1.8;
            color: #555;
        }

        /* 内容区域整体样式 */
        .content-area {
            padding: 25px 0;
            background: #fafafa;
        }

        /* 响应式和打印优化 */
        @media print {
            body {
                background: white;
            }
            .section {
                box-shadow: none;
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 页眉部分 -->
        <div class="header">
            <div class="header-left">
                <h1>${name}</h1>
            </div>
            <div class="contact-info">
                <div class="contact-row">
                    <div class="contact-item">
                        <span>👤 ${age}岁</span>
                    </div>
                    <div class="contact-item">
                        <span>📞 ${phone}</span>
                    </div>
                </div>
                <div class="contact-row">
                    <div class="contact-item">
                        <span>✉️ ${email}</span>
                    </div>
                    <div class="contact-item">
                        <span>📍 ${hometown}</span>
                    </div>
                </div>
                <div class="contact-row">
                    <div class="contact-item">
                        <span>🎯 ${jobObjective}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 教育经历 -->
            ${education}

            <!-- 工作经历 -->
            ${work}

            <!-- 项目经验 -->
            ${projects}

            <!-- 技能特长 -->
            ${skills}

            <!-- 证书奖项 -->
            ${certificates}

            <!-- 校园经历 -->
            ${campus}

            <!-- 兴趣爱好 -->
            ${interests}

            <!-- 自我评价 -->
            ${selfEvaluation}
        </div>
    </div>
</body>
</html> 