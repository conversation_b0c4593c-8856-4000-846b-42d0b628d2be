<template>
  <div class="form-container">
    <el-form :model="formData" label-width="100px">
      <el-form-item label="求职意向岗位">
        <el-input v-model="formData.position" placeholder="请输入求职意向岗位" />
      </el-form-item>
      
      <el-form-item label="意向城市">
        <el-select
          v-model="formData.cities"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请选择意向城市（可多选）"
        >
          <el-option label="北京" value="北京" />
          <el-option label="上海" value="上海" />
          <el-option label="广州" value="广州" />
          <el-option label="深圳" value="深圳" />
          <el-option label="杭州" value="杭州" />
          <el-option label="成都" value="成都" />
          <el-option label="南京" value="南京" />
          <el-option label="武汉" value="武汉" />
          <el-option label="西安" value="西安" />
          <el-option label="重庆" value="重庆" />
          <el-option label="苏州" value="苏州" />
          <el-option label="天津" value="天津" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="期望薪资">
        <el-select v-model="formData.salaryType" style="width: 120px;">
          <el-option label="月薪" value="月薪" />
          <el-option label="年薪" value="年薪" />
        </el-select>
        
        <el-select v-model="formData.salaryRange" placeholder="请选择期望薪资范围" style="margin-left: 10px; width: 200px;">
          <el-option label="3k-5k" value="3k-5k" />
          <el-option label="5k-8k" value="5k-8k" />
          <el-option label="8k-10k" value="8k-10k" />
          <el-option label="10k-15k" value="10k-15k" />
          <el-option label="15k-20k" value="15k-20k" />
          <el-option label="20k-30k" value="20k-30k" />
          <el-option label="30k以上" value="30k以上" />
          <el-option label="面议" value="面议" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="入职时间">
        <el-select v-model="formData.availableTime" placeholder="请选择最快入职时间">
          <el-option label="随时" value="随时" />
          <el-option label="一周内" value="一周内" />
          <el-option label="两周内" value="两周内" />
          <el-option label="一个月内" value="一个月内" />
          <el-option label="三个月内" value="三个月内" />
          <el-option label="待定" value="待定" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="求职状态">
        <el-select v-model="formData.jobStatus" placeholder="请选择当前求职状态">
          <el-option label="在职-暂不考虑" value="在职-暂不考虑" />
          <el-option label="在职-考虑机会" value="在职-考虑机会" />
          <el-option label="在职-积极找工作" value="在职-积极找工作" />
          <el-option label="离职-正在找工作" value="离职-正在找工作" />
          <el-option label="应届毕业生" value="应届毕业生" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="工作性质">
        <el-radio-group v-model="formData.jobType">
          <el-radio label="全职">全职</el-radio>
          <el-radio label="兼职">兼职</el-radio>
          <el-radio label="实习">实习</el-radio>
          <el-radio label="校企合作">校企合作</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="行业方向">
        <el-select 
          v-model="formData.industries" 
          multiple 
          filterable 
          placeholder="请选择行业方向（可多选）"
          style="width: 100%;"
        >
          <el-option label="互联网/IT" value="互联网/IT" />
          <el-option label="金融" value="金融" />
          <el-option label="教育" value="教育" />
          <el-option label="医疗健康" value="医疗健康" />
          <el-option label="电子商务" value="电子商务" />
          <el-option label="房地产" value="房地产" />
          <el-option label="制造业" value="制造业" />
          <el-option label="文化传媒" value="文化传媒" />
          <el-option label="服务业" value="服务业" />
          <el-option label="政府/非盈利机构" value="政府/非盈利机构" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button type="primary" @click="saveForm">保存信息</el-button>
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 表单数据
const formData = ref({
  position: '',
  cities: [],
  salaryType: '月薪',
  salaryRange: '',
  availableTime: '随时',
  jobStatus: '',
  jobType: '全职',
  industries: []
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    Object.keys(formData.value).forEach(key => {
      if (newVal[key] !== undefined) {
        formData.value[key] = newVal[key]
      }
    })
  }
}, { immediate: true, deep: true })

// 保存表单
const saveForm = () => {
  emit('update', { ...formData.value })
  ElMessage.success('求职意向信息已保存')
}

// 重置表单
const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    if (props.data[key] !== undefined) {
      formData.value[key] = props.data[key]
    } else {
      // 设置默认值
      switch(key) {
        case 'cities':
        case 'industries':
          formData.value[key] = []
          break
        case 'salaryType':
          formData.value[key] = '月薪'
          break
        case 'availableTime':
          formData.value[key] = '随时'
          break
        case 'jobType':
          formData.value[key] = '全职'
          break
        default:
          formData.value[key] = ''
      }
    }
  })
  ElMessage.info('已重置为上次保存的内容')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 