<template>
  <div class="resume-template template-8">
    <div class="resume-container">
      <!-- 个人信息部分 -->
      <div class="header-section">
        <div class="personal-info">
          <h1 class="name">{{ resume.modules.basic?.name || '未填写' }}</h1>
          <div class="basic-info">
            <div class="info-item" v-if="resume.modules.basic?.age">
              <span class="label">年龄：</span>
              <span>{{ resume.modules.basic.age }}岁</span>
            </div>
            <div class="info-item" v-if="resume.modules.basic?.birthDate">
              <span class="label">出生年月：</span>
              <span>{{ resume.modules.basic?.birthDate }}</span>
            </div>
            <div class="info-item" v-if="resume.modules.basic?.phone">
              <span class="label">联系电话：</span>
              <span>{{ resume.modules.basic?.phone }}</span>
            </div>
            <div class="info-item" v-if="resume.modules.basic?.email">
              <span class="label">电子邮箱：</span>
              <span>{{ resume.modules.basic?.email }}</span>
            </div>
            <div class="info-item" v-if="resume.modules.basic?.address">
              <span class="label">所在地：</span>
              <span>{{ resume.modules.basic?.address }}</span>
            </div>
            <div class="info-item" v-if="resume.modules.basic?.jobObjective">
              <span class="label">求职意向：</span>
              <span>{{ resume.modules.basic.jobObjective }}</span>
            </div>
          </div>
        </div>
        <div class="avatar-container">
          <img v-if="resume.modules.basic?.avatar" :src="resume.modules.basic.avatar" alt="头像" class="avatar" />
          <div v-else class="avatar-placeholder"></div>
        </div>
      </div>

      <!-- 教育背景 -->
      <div v-if="hasEducation" class="section education-section">
        <div class="section-header">
          <h2>教育背景</h2>
        </div>
        <div class="section-content">
          <div v-for="(edu, index) in resume.modules.education" :key="index" class="education-item">
            <div class="item-left">{{ edu.startDate || formatDate(edu.time?.[0]) }} - {{ edu.endDate || formatDate(edu.time?.[1]) }}</div>
            <div class="item-right">
              <div class="item-title">{{ edu.school }} | {{ edu.major }} | {{ edu.degree }}</div>
              <div class="item-description" v-if="edu.courses">
                <ul>
                  <li v-for="(item, i) in formatListItems(edu.courses)" :key="i">{{ item }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业技能 -->
      <div v-if="hasSkills" class="section skills-section">
        <div class="section-header">
          <h2>专业技能</h2>
        </div>
        <div class="section-content">
          <ul class="skills-list">
            <li v-for="(skill, index) in resume.modules.skills" :key="index" class="skill-item">
              <div class="skill-description-text">
                <MdPreview :modelValue="skill.description" />
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 工作经历 -->
      <div v-if="hasWork" class="section work-section">
        <div class="section-header">
          <h2>工作经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(work, index) in resume.modules.work" :key="index" class="work-item">
            <div class="item-left">{{ work.startDate || formatDate(work.time?.[0]) }} - {{ work.endDate || formatDate(work.time?.[1]) }}</div>
            <div class="item-right">
              <div class="item-title">{{ work.company }} | {{ work.position }}</div>
              <div class="item-description">
                <div class="work-description">
                  <MdPreview :modelValue="work.description" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目经验 -->
      <div v-if="hasProjects" class="section">
        <div class="section-header">
          <h2>项目经验</h2>
        </div>
        <div class="section-content">
          <div v-for="(project, index) in resume.modules.projects" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <div class="project-date">{{ project.startDate }} - {{ project.endDate || '至今' }}</div>
            </div>
            <div class="project-role">{{ project.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="project.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 练手项目 -->
      <div v-if="hasPractices" class="section">
        <div class="section-header">
          <h2>练手项目</h2>
        </div>
        <div class="section-content">
          <div v-for="(practice, index) in resume.modules.practices" :key="index" class="project-item">
            <div class="project-header">
              <div class="project-title">{{ practice.name }}</div>
              <div class="project-date">{{ practice.startDate }} - {{ practice.endDate || '至今' }}</div>
            </div>
            <div class="project-role">{{ practice.role }}</div>
            <div class="project-description">
              <MdPreview :modelValue="practice.description" />
            </div>
          </div>
        </div>
      </div>

      <!-- 实习经历 -->
      <div v-if="hasInternship" class="section internship-section">
        <div class="section-header">
          <h2>实习经历</h2>
        </div>
        <div class="section-content">
          <div v-for="(internship, index) in resume.modules.internship" :key="index" class="internship-item">
            <div class="item-left">{{ internship.startDate || formatDate(internship.time?.[0]) }} - {{ internship.endDate || formatDate(internship.time?.[1]) }}</div>
            <div class="item-right">
              <div class="item-title">{{ internship.company }} | {{ internship.position }}</div>
              <div class="item-description">
                <div v-for="(line, i) in formatListItems(internship.description)" :key="i" class="description-line">
                  {{ line }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 自我评价 -->
      <div v-if="hasEvaluation" class="section evaluation-section">
        <div class="section-header">
          <h2>自我评价</h2>
        </div>
        <div class="section-content">
          <div class="evaluation-content">
            <MdPreview :modelValue="resume.modules.evaluation" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';

const props = defineProps({
  resume: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  // Extract year and month from Chinese date format (e.g., "2021年9月22日")
  const year = date.match(/(\d{4})年/)?.[1];
  const month = date.match(/(\d{1,2})月/)?.[1];
  if (!year || !month) return '';
  // Pad month with leading zero if needed
  const paddedMonth = month.padStart(2, '0');
  return `${year}.${paddedMonth}`;
};

// 判断各模块是否有内容
const hasEducation = computed(() => props.resume.modules && props.resume.modules.education && props.resume.modules.education.length > 0);
const hasWork = computed(() => props.resume.modules && props.resume.modules.work && props.resume.modules.work.length > 0);
const hasInternship = computed(() => props.resume.modules && props.resume.modules.internship && props.resume.modules.internship.length > 0);
const hasProjects = computed(() => props.resume.modules && props.resume.modules.projects && props.resume.modules.projects.length > 0);
const hasSkills = computed(() => props.resume.modules && props.resume.modules.skills && props.resume.modules.skills.length > 0);
const hasEvaluation = computed(() => props.resume.modules && props.resume.modules.evaluation && typeof props.resume.modules.evaluation === 'string' && props.resume.modules.evaluation.trim() !== '');
const hasPractices = computed(() => props.resume.modules && props.resume.modules.practices && props.resume.modules.practices.length > 0);

// 格式化列表项
const formatListItems = (text) => {
  if (!text) return [];
  return text.split('\n')
      .filter(item => item.trim() !== '')
      .map(item => item.trim().replace(/^[•·-]\s*/, ''));
};

// 格式化段落
const formatParagraphs = (text) => {
  if (!text) return [];
  return text.split('\n').filter(p => p.trim() !== '');
};
</script>

<style scoped>
.resume-template {
  width: 100%;
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  color: #333;
  background-color: #fff;
}

.resume-container {
  max-width: 210mm;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

/* 头部样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0px;
}

.personal-info {
  flex: 1;
}

.name {
  font-size: 24px;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.basic-info {
  display: block;
  flex-wrap: wrap;
}

.info-item {
  font-size: 20px;
  margin-right: 20px;
  margin-bottom: 8px;
}

.label {
  font-weight: bold;
}

.avatar-container {
  width: 120px;
  height: 160px;
  margin-left: 20px;
}

.avatar, .avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #ccc;
}

.avatar-placeholder {
  background-color: #f0f0f0;
}

/* 各部分通用样式 */
.section {
  margin-bottom: 25px;
}

.section-header {
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #3498db;
}

.section-content {
  padding: 0 5px;
}

/* 两栏布局样式 */
.education-item, .work-item, .project-item, .internship-item {
  display: flex;
  margin-bottom: 15px;
  position: relative;
}

.item-left {
  position: absolute;
  top: 0;
  right: 0;
  font-weight: bold;
  color: #666;
}

.item-right {
  flex: 1;
  padding-right: 120px;
}

.item-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.item-subtitle {
  color: #666;
  margin-bottom: 5px;
}

.item-tech {
  color: #666;
  margin-bottom: 8px;
  font-style: italic;
}

.item-description {
  margin-top: 5px;
}

.description-line {
  margin-bottom: 5px;
  position: relative;
  padding-left: 15px;
}

.description-line::before {
  content: "•";
  position: absolute;
  left: 0;
}

/* 技能样式 */
.skills-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.skill-item {
  margin-bottom: 8px;
  position: relative;
  padding-left: 15px;
}

.skill-item::before {
  content: "•";
  position: absolute;
  left: 0;
}

/* 自我评价样式 */
.evaluation-content {
  line-height: 1.7;
}

.evaluation-paragraph {
  margin-bottom: 8px;
  text-indent: 2em;
}

/* 添加 MdPreview 相关样式 */
:deep(.md-preview) {
  background: none;
  padding: 0;
}

:deep(.md-preview-html) {
  padding: 0;
}

:deep(.md-preview-html p) {
  margin: 0;
}

:deep(.md-preview-html ul) {
  margin: 0;
  padding-left: 20px;
}

:deep(.md-preview-html li) {
  margin: 0;
}

:deep(.md-preview-html strong) {
  font-weight: bold;
}

:deep(.md-preview-html em) {
  font-style: italic;
}

:deep(.md-preview-html h1) {
  font-size: 1.5em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h2) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

:deep(.md-preview-html h3) {
  font-size: 1.1em;
  margin: 0.5em 0;
}
</style> 