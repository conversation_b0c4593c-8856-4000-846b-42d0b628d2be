<template>
  <MainLayout>
    <div class="user-center">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">我的简历</h1>
        </div>

        <div class="user-main">
          <!-- 骨架屏加载动画 -->
          <div v-if="loading" style="padding: 32px 0;">
            <el-skeleton :rows="4" animated style="margin-bottom: 24px;" />
            <el-skeleton :rows="2" animated style="margin-bottom: 24px;" />
            <el-skeleton :rows="2" animated />
          </div>
          <!-- 我的简历 -->
          <div v-else-if="activeTab === 'resumes'" class="tab-content">
            <div class="section-header">
              <el-button type="primary" @click="createNewResume">
                <el-icon>
                  <Plus/>
                </el-icon>
                创建新简历
              </el-button>
            </div>

            <div v-if="resumeList.length > 0" class="resume-list">
              <div v-for="resume in resumeList" :key="resume.id" class="resume-item">
                <div class="resume-card">
                  <div class="resume-info">
                    <div class="resume-title-container">
                      <h3>{{ resume.name }}</h3>
                      <el-tooltip v-if="resume.status === 2 && resume.auditOpinion" :content="resume.auditOpinion" placement="top">
                        <span class="status-badge" :class="getStatusClass(resume.status)">{{ getStatusLabel(resume.status) }}</span>
                      </el-tooltip>
                      <span v-else class="status-badge" :class="getStatusClass(resume.status)">{{ getStatusLabel(resume.status) }}</span>
                    </div>
                    <div class="resume-meta">
                      <span>更新时间：{{ formatDate(resume.updatedAt) }}</span>
                      <span>模板：{{ getTemplateName(resume.templateId) }}</span>
                    </div>
                  </div>
                  <div class="resume-actions">
                    <el-button @click="previewResume(resume.id)">
                      <el-icon>
                        <View/>
                      </el-icon>
                    </el-button>
                    <el-tooltip v-if="resume.status !== 1" content="只有已通过审核的简历才能下载" placement="top">
                      <el-button type="primary" :disabled="true">下载</el-button>
                    </el-tooltip>
                    <el-button
                      v-else
                      type="primary"
                      @click="downloadResume(resume.id)"
                      :loading="downloadingResumeId === resume.id"
                      :disabled="downloadingResumeId === resume.id"
                    >
                      {{ downloadingResumeId === resume.id ? '下载中...' : '下载' }}
                    </el-button>
                    <el-dropdown>
                      <el-button>更多<el-icon style="margin-left:4px"><ArrowDown/></el-icon></el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="editResume(resume.id)">
                            <el-button type="default" style="width:100%">编辑</el-button>
                          </el-dropdown-item>
                          <el-dropdown-item @click="deleteResume(resume.id)">
                            <el-button type="danger" style="width:100%">删除</el-button>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="empty-state">
              <el-empty description="您还没有创建简历">
                <el-button type="primary" @click="createNewResume">立即创建</el-button>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下载进度对话框 -->
    <el-dialog
      v-model="showProgressDialog"
      title="下载简历"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      center
    >
      <div class="download-progress-container">
        <div class="progress-icon">
          <el-icon class="rotating-icon" size="40" color="#409eff">
            <Download />
          </el-icon>
        </div>

        <div class="progress-content">
          <div class="progress-text">{{ downloadStage }}</div>

          <el-progress
            :percentage="downloadProgress"
            :stroke-width="8"
            :show-text="false"
            class="progress-bar"
          />

          <div class="progress-percentage">{{ downloadProgress }}%</div>
        </div>

        <div class="progress-tips">
          <el-icon><InfoFilled /></el-icon>
          <span>请耐心等待，正在为您生成高质量PDF简历...</span>
        </div>
      </div>
    </el-dialog>
  </MainLayout>
</template>

<script setup>
import {ref, reactive, onMounted, h, createApp, nextTick} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage, ElMessageBox,ElLoading} from 'element-plus'
import {Document, User, Setting, Plus, ArrowDown, View, Download, InfoFilled} from '@element-plus/icons-vue'
import MainLayout from '@/layouts/MainLayout.vue'
import {useUserStore} from '@/stores/user'
import {useResumeStore} from '@/stores/resume'
import {userApi, resumeApi} from '@/api'
import { exportResumeToPdf } from '@/api/resume'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createVNode, render } from 'vue'
import Avatar from '@/components/Avatar.vue'
// 导入html2pdf
import html2pdf from 'html2pdf.js'
// 导入html2canvas和jsPDF
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
// 导入MdPreview组件
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/preview.css'

// 导入所有模板组件
import Template1 from '@/components/templates/Template1.vue'
import Template2 from '@/components/templates/Template2.vue'
import Template3 from '@/components/templates/Template3.vue'
import Template4 from '@/components/templates/Template4.vue'
import Template5 from '@/components/templates/Template5.vue'
import Template6 from '@/components/templates/Template6.vue'
import Template7 from '@/components/templates/Template7.vue'
import Template8 from '@/components/templates/Template8.vue'
import Template9 from '@/components/templates/Template9.vue'
import Template10 from '@/components/templates/Template10.vue'

const router = useRouter()
const userStore = useUserStore()
const resumeStore = useResumeStore()

// 选项卡
const activeTab = ref('resumes')

// 用户信息
const userInfo = ref({
  username: userStore.user?.username || '用户',
  email: userStore.user?.email || '',
  avatar: userStore.user?.avatar || ''
})

// 简历列表
const resumeList = ref([])

// 个人资料表单
const profileForm = reactive({
  username: userInfo.value.username,
  email: userInfo.value.email,
  avatar: userInfo.value.avatar,
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单校验规则
const passwordRules = {
  currentPassword: [
    {required: true, message: '请输入当前密码', trigger: 'blur'}
  ],
  newPassword: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {min: 6, message: '密码长度不能少于6个字符', trigger: 'blur'}
  ],
  confirmPassword: [
    {required: true, message: '请确认新密码', trigger: 'blur'},
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取模板名称
const getTemplateName = (templateId) => {
  const template = resumeStore.templates.find(t => t.id === templateId)
  return template ? template.name : '未知模板'
}

// 创建新简历
const createNewResume = () => {
  router.push('/editor/new')
}

const deleteResume = async (id) => {
  try {
    console.log('准备删除简历，ID:', id);
    // 1. 显示确认对话框
    await ElMessageBox.confirm(
        '此操作将永久删除该简历，无法恢复',
        '确认删除',
        {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning',
          center: true, // 居中显示
          closeOnClickModal: false, // 点击背景不关闭
          closeOnPressEscape: false, // 按ESC不关闭
        }
    );
    console.log('用户确认删除简历');

    // 2. 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.1)',
    });

    try {
      // 3. 执行删除API
      console.log('开始调用删除API，参数:', id);
      const response = await resumeApi.deleteResume(id);
      console.log('删除API响应:', response);

      if (response.code === 0) {
        // 4. 删除成功
        console.log('删除成功，准备刷新列表');
        ElMessage({
          message: '简历删除成功',
          type: 'success',
          duration: 2000,
        });
        // 刷新列表
        refreshResumeList(); // 封装刷新逻辑
      } else {
        // 业务错误
        console.error('删除失败，业务错误:', response);
        throw new Error(response.message || '删除失败，请重试');
      }
    } catch (error) {
      // 5. 处理异常
      console.error('删除过程中发生异常:', error);
      ElMessage({
        message: error.message || '删除失败，服务器异常',
        type: 'error',
        duration: 3000,
      });
    } finally {
      // 6. 隐藏加载状态
      loadingInstance.close();
      console.log('删除操作完成');
    }
  } catch (e) {
    console.log('用户取消删除或发生其他错误:', e);
    // 用户取消删除，无需提示
  }
};

// 封装刷新列表逻辑，提高代码复用
const refreshResumeList = async () => {
  try {
    const { data } = await resumeApi.getResumeList();
    resumeList.value = formatResumeList(data);
  } catch (error) {
    ElMessage({
      message: '刷新列表失败，请手动刷新',
      type: 'warning',
    });
  }
};

// 编辑简历
const editResume = async (id) => {
  try {
    const response = await resumeApi.getResumeById(id)
    if (response.code === 0 && response.data) {
      const resumeData = response.data

      // 验证数据格式
      if (!resumeData || typeof resumeData !== 'object') {
        throw new Error('Invalid resume data format')
      }

      // 将简历数据存储到 localStorage 中，以便在编辑页面使用
      const dataToStore = {
        resumeVo: resumeData.resumeVo || null,
        information: resumeData.information || null,
        educationList: resumeData.educationList || [],
        workList: resumeData.workList || [],
        projectList: resumeData.projectList || [],
        practiceList: resumeData.practiceList || [],
        talentList: resumeData.talentList || [],
        certificateList: resumeData.certificateList || [],
        campusList: resumeData.campusList || [],
        interestList: resumeData.interestList || [],
        evaluateList: resumeData.evaluateList || []
      }

      // 清除旧的简历数据
      localStorage.removeItem('resumeData')

      // 存储新的简历数据
      localStorage.setItem('resumeData', JSON.stringify(dataToStore))

      // 跳转到编辑页面
      router.push(`/editor/${id}`)
    } else {
      throw new Error(response.data?.message || '获取简历详情失败')
    }
  } catch (error) {
    console.error('获取简历详情失败:', error)
    ElMessage.error(error.message || '获取简历详情失败')
  }
}

// 预览简历
const previewResume = (id) => {
  router.push(`/preview/${id}`)
}

const categoryOptions = ref([])

// 获取职位类型列表
const fetchCategoryList = async () => {
  try {
    const res = await resumeApi.getCategoryList()
    if (!res || !res.data) {
      console.warn('Invalid category list response:', res);
      categoryOptions.value = [];
      return;
    }

    categoryOptions.value = (res.data || []).map(item => ({
      label: item?.name || '未知类别',
      value: item?.catId || ''
    })).filter(item => item.value !== '');
  } catch (error) {
    console.error('获取职位类型列表失败:', error)
    ElMessage.error('获取职位类型列表失败')
    categoryOptions.value = [];
  }
}


// 获取分类标签
const getCategoryLabel = (category) => {
  const categoryMap = {
    0: '通用简历',
    1: '校园简历',
    2: '社会招聘',
    3: '实习简历'
  }
  return categoryMap[category] || '未分类'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    0: '待审核',
    1: '审核通过',
    2: '审核拒绝'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusClassMap = {
    0: 'status-pending',
    1: 'status-approved',
    2: 'status-rejected'
  }
  return statusClassMap[status] || ''
}

// 处理头像上传
const handleAvatarChange = (file) => {
  // 实际项目中应该上传到服务器
  const reader = new FileReader()
  reader.onload = (e) => {
    profileForm.avatar = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 保存个人资料
const saveProfile = () => {
  // 实际项目中应该调用API
  userInfo.value = {
    ...userInfo.value,
    username: profileForm.username,
    avatar: profileForm.avatar
  }

  ElMessage.success('个人资料已更新')
}

// PDF预览组件引用
const pdfRef = ref(null);
// PDF数据引用
const pdfResumeData = ref(null);

// 下载状态管理
const downloadingResumeId = ref(null);
const downloadProgress = ref(0);
const downloadStage = ref('');
const showProgressDialog = ref(false);

// 下载阶段配置
const downloadStages = [
  { stage: 'preparing', text: '正在准备简历数据...', progress: 10 },
  { stage: 'fetching', text: '正在获取简历数据...', progress: 25 },
  { stage: 'generating', text: '正在服务器端生成PDF...', progress: 60 },
  { stage: 'optimizing', text: '正在优化PDF格式...', progress: 85 },
  { stage: 'downloading', text: '正在准备下载...', progress: 95 },
  { stage: 'complete', text: '下载完成！', progress: 100 }
];

// 更新下载进度
const updateDownloadProgress = (stage) => {
  const stageInfo = downloadStages.find(s => s.stage === stage);
  if (stageInfo) {
    downloadStage.value = stageInfo.text;
    downloadProgress.value = stageInfo.progress;
  }
};

// 下载简历
const downloadResume = async (id) => {
  try {
    // 防止重复下载
    if (downloadingResumeId.value === id) {
      ElMessage.warning('该简历正在下载中，请稍候...');
      return;
    }

    downloadingResumeId.value = id;
    downloadProgress.value = 0;
    showProgressDialog.value = true;

    // 开始第一阶段
    updateDownloadProgress('preparing');

    // 模拟进度更新
    await new Promise(resolve => setTimeout(resolve, 800));
    updateDownloadProgress('fetching');

    // 获取简历数据
    const response = await resumeApi.getResumeById(id);
    if (response.code === 0 && response.data) {
      const apiData = response.data;
      const resumeData = {
        resumeId: apiData.resumeVo?.resumeId || '',
        templateId: apiData.resumeVo?.templateId || 1,
        category: apiData.resumeVo?.category || 0,
        status: apiData.resumeVo?.status || 0,
        name: apiData.resumeVo?.title,
        modules: {
          basic: {
            id: apiData.information?.inforId || '',
            resumeId: apiData.information?.resumeId || '',
            name: apiData.information?.name || '',
            gender: apiData.information?.gender || '',
            birthday: apiData.information?.birthDate || '',
            phone: apiData.information?.phone || '',
            email: apiData.information?.email || '',
            address: apiData.information?.hometown || '',
            avatar: apiData.information?.avatar || '',
            age: apiData.information?.age || '',
            jobObjective: apiData.information?.jobObjective || '',
            nationality: apiData.information?.nationality || ''
          },
          education: apiData.educationList?.map(edu => ({
            id: edu.eduId || '',
            resumeId: edu.resumeId || '',
            school: edu.school || '',
            major: edu.major || '',
            degree: edu.education || '',
            time: (edu.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: edu.timePeriod?.split('至')[0] || '',
            endDate: edu.timePeriod?.split('至')[1] || '',
            courses: edu.mainCourses || ''
          })) || [],
          work: apiData.workList?.map(work => ({
            id: work.workId || '',
            resumeId: work.resumeId || '',
            company: work.company || '',
            position: work.position || '',
            time: (work.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: work.timePeriod?.split('至')[0] || '',
            endDate: work.timePeriod?.split('至')[1] || '',
            description: work.workDescription || ''
          })) || [],
          projects: apiData.projectList?.map(project => ({
            id: project.expId || '',
            resumeId: project.resumeId || '',
            name: project.projectName || '',
            role: project.role || '',
            time: (project.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: project.timePeriod?.split('至')[0] || '',
            endDate: project.timePeriod?.split('至')[1] || '',
            description: project.projectDescription || '',
            technologies: project.technologies || ''
          })) || [],
          practices: apiData.practiceList?.map(practice => ({
            id: practice.praId || '',
            resumeId: practice.resumeId || '',
            name: practice.practiceName || '',
            role: practice.role || '',
            url: practice.url || '',
            time: (practice.timePeriod || '').split('至').map(part => part.trim()).filter(Boolean) || [],
            startDate: practice.timePeriod?.split('至')[0] || '',
            endDate: practice.timePeriod?.split('至')[1] || '',
            description: practice.practiceDescription || ''
          })) || [],
          skills: apiData.talentList?.map(talent => ({
            name: talent.skillName || '',
            description: talent.skillDescription || '',
            level: talent.proficiency || '',
            id: talent.talId || ''
          })) || [],
          certificates: {
            id: apiData?.certificate?.cerId || '',
            resumeId: apiData?.certificate?.resumeId || '',
            certificateName: apiData?.certificate?.certificateName || ''
          },
          campus: {
            id: apiData.campus?.camId || '',
            resumeId: apiData.campus?.resumeId || '',
            description: apiData.campus?.campusExperience || ''
          },
          interests: {
            id: apiData?.interest?.intId || '',
            resumeId: apiData?.interest?.resumeId || '',
            description: apiData?.interest?.interest || ''
          },
          selfEvaluation: {
            id: apiData?.evaluate?.evaId || '',
            resumeId: apiData?.evaluate?.resumeId || '',
            description: apiData?.evaluate?.selfEvaluation || ''
          }
        }
      };

      console.log('获取的简历数据:', apiData);
      console.log('简历模板ID:', apiData.resumeVo?.templateId);

      try {
        // 模拟进度更新
        await new Promise(resolve => setTimeout(resolve, 1000));
        updateDownloadProgress('generating');

        console.log('使用后端API方式下载PDF...');

        try {
          // 调用后端PDF导出接口
          const startTime = Date.now();
          const response = await exportResumeToPdf(id);
          const duration = Date.now() - startTime;

          // 更新进度
          updateDownloadProgress('optimizing');
          await new Promise(resolve => setTimeout(resolve, 500));

          updateDownloadProgress('downloading');
          await new Promise(resolve => setTimeout(resolve, 300));

          // 创建下载链接
          const blob = new Blob([response], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);

          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = `${resumeData.name || '简历'}_${new Date().toISOString().slice(0, 10)}.pdf`;
          document.body.appendChild(a);
          a.click();

          // 清理
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);

          // 完成进度
          updateDownloadProgress('complete');
          await new Promise(resolve => setTimeout(resolve, 800));

          // 显示成功消息
          ElMessage({
            message: 'PDF下载成功！',
            type: 'success',
            duration: 3000,
            showClose: true
          });

        } catch (error) {
          console.error('后端PDF下载失败:', error);
          ElMessage({
            message: `PDF下载失败: ${error.message || '服务器错误，请稍后重试'}`,
            type: 'error',
            duration: 5000,
            showClose: true
          });
        } finally {
          showProgressDialog.value = false;
          downloadingResumeId.value = null;
        }
      } catch (error) {
        console.error('PDF下载失败:', error);
        ElMessage({
          message: `下载失败: ${error.message || '网络错误，请检查网络连接'}`,
          type: 'error',
          duration: 5000,
          showClose: true
        });
        showProgressDialog.value = false;
        downloadingResumeId.value = null;
      }
    } else {
      showProgressDialog.value = false;
      downloadingResumeId.value = null;
      throw new Error(response.data?.message || '获取简历详情失败');
    }
  } catch (error) {
    console.error('下载PDF失败:', error);
    ElMessage({
      message: `下载失败: ${error.message || '系统错误，请稍后重试'}`,
      type: 'error',
      duration: 5000,
      showClose: true
    });
    showProgressDialog.value = false;
    downloadingResumeId.value = null;
  }
};

// 获取用户数据
const loading = ref(true)
onMounted(async () => {
  loading.value = true
  try {
    // 实际项目中应该调用API
    // const res = await userApi.getUserInfo()
    // userInfo.value = res.data
    // profileForm.username = res.data.username
    // profileForm.email = res.data.email
    // profileForm.avatar = res.data.avatar
    // profileForm.phone = res.data.phone

    // 获取简历列表
    const resumeRes = await resumeApi.getResumeList()
    resumeList.value = formatResumeList(resumeRes.data)
    await fetchCategoryList()
  } catch (error) {
    console.error('获取用户数据失败:', error)
    ElMessage.error('获取用户数据失败')
  } finally {
    loading.value = false
  }
})

const resumeData = ref(resumeStore.currentResume || {
  templateId: '',
  modules: {
    basic: {
      name: '',
      gender: '',
      birthDate: '',
      phone: '',
      email: '',
      age: '',
      hometown: '',
      jobObjective: '',
      avatar: ''
    },
    education: [],
    work: [],
    projects: [],
    practices: [],
    skills: [],
    certificates: '',
    campus: '',
    interests: '',
    evaluation: ''
  }
})

const formatResumeList = (data) => {
  if (!data || !Array.isArray(data)) {
    console.warn('Invalid resume list data:', data);
    return [];
  }

  return data.map(item => {
    // 确保item不为null
    if (!item) return null;

    const {
      resumeVo = {},
      information = {},
      educationList = [],
      workList = [],
      projectList = [],
      practiceList = [],
      talentList = [],
      certificate = {},
      campus = {},
      interest = {},
      evaluate = {}
    } = item;

    return {
      id: resumeVo.resumeId,
      templateId: resumeVo.templateId,
      name: resumeVo.title,
      updatedAt: resumeVo.updateTime,
      category: resumeVo.category,
      status: resumeVo.status,
      auditOpinion: resumeVo.auditOpinion,
      modules: {
        basic: {
          id: information.inforId || '',
          resumeId: information.resumeId || '',
          name: information.name || '',
          gender: information.gender || '',
          birthDate: information.birthDate || '',
          phone: information.phone || '',
          email: information.email || '',
          age: information.age || '',
          hometown: information.hometown || '',
          jobObjective: information.jobObjective || '',
          avatar: information.avatar || '',
          nationality: information.nationality || ''
        },
        education: educationList.map(edu => ({
          school: edu.school || '',
          major: edu.major || '',
          degree: edu.education || '',
          timePeriod: edu.timePeriod || '',
          courses: edu.mainCourses || ''
        })),
        work: workList.map(work => ({
          company: work.company || '',
          position: work.position || '',
          timePeriod: work.timePeriod || '',
          description: work.workDescription || ''
        })),
        internship: practiceList.map(practice => ({
          company: practice.projectName || '',
          position: practice.role || '',
          timePeriod: practice.timePeriod || '',
          description: practice.projectDescription || ''
        })),
        projects: projectList.map(project => ({
          name: project.projectName || '',
          role: project.role || '',
          timePeriod: project.timePeriod || '',
          description: project.projectDescription || ''
        })),
        skills: talentList.map(talent => ({
          name: talent.skillName || '',
          description: talent.skillDescription || '',
          level: talent.proficiency || '',
          id: talent.talId || ''
        })) || [],
        certificates: {
          id: certificate?.cerId || '',
          resumeId: certificate?.resumeId || '',
          certificateName: certificate?.certificateName || ''
        },
        campus: {
          id: campus?.camId || '',
          resumeId: campus?.resumeId || '',
          description: campus?.campusExperience || ''
        },
        interests: {
          id: interest?.intId || '',
          resumeId: interest?.resumeId || '',
          description: interest?.interest || ''
        },
        selfEvaluation: {
          id: evaluate?.evaId || '',
          resumeId: evaluate?.resumeId || '',
          description: evaluate?.selfEvaluation || ''
        }
      }
    };
  }).filter(Boolean); // Remove any null items
};

// 根据模板ID获取组件
const getTemplateComponent = (templateId) => {
  switch (templateId) {
    case 1:
      return Template1;
    case 2:
      return Template2;
    case 3:
      return Template3;
    case 4:
      return Template4;
    case 5:
      return Template5;
    case 6:
      return Template6;
    case 7:
      return Template7;
    case 8:
      return Template8;
    case 9:
      return Template9;
    case 10:
      return Template10;
    default:
      return Template1; // 默认模板
  }
};
</script>

<style scoped>
.user-center {
  padding: 40px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
}

.user-content {
  display: flex;
  gap: 30px;
}

.user-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.user-info {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 15px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.email {
  font-size: 14px;
  color: var(--light-text);
}

.nav-menu {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.nav-item:hover {
  background-color: #f5f7fa;
}

.nav-item.active {
  background-color: #ecf5ff;
  color: var(--primary-color);
}

.user-main {
  flex: 1;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
}

.resume-list {
  display: grid;
  gap: 15px;
}

.resume-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
}

.resume-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.resume-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.resume-meta {
  font-size: 14px;
  color: var(--light-text);
}

.resume-meta span {
  margin-right: 15px;
}

.resume-actions {
  display: flex;
  gap: 3px;
}

.empty-state {
  margin: 50px 0;
  text-align: center;
}

.profile-form,
.password-form {
  max-width: 500px;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader:hover {
  border-color: var(--primary-color);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .user-content {
    flex-direction: column;
  }

  .user-sidebar {
    width: 100%;
  }
}

.category-badge {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 13px;
  margin-left: 6px;
  margin-right: 6px;
  vertical-align: middle;
  background: #f0f7ff;
  color: #3498db;
}
.category-badge-1 {
  background: #e6f7ff;
  color: #1890ff;
}
.category-badge-2 {
  background: #f6ffed;
  color: #52c41a;
}
.category-badge-3 {
  background: #fffbe6;
  color: #faad14;
}
.category-badge-4 {
  background: #fff0f6;
  color: #eb2f96;
}
.category-badge-5 {
  background: #f9f0ff;
  color: #722ed1;
}
.category-badge-6 {
  background: #f0f5ff;
  color: #2f54eb;
}
.category-badge-7 {
  background: #f0fffb;
  color: #13c2c2;
}

.status-badge {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 13px;
  margin-left: 6px;
  margin-right: 6px;
  vertical-align: middle;
}
.status-pending {
  background: #fffbe6;
  color: #faad14;
}
.status-approved {
  background: #f6ffed;
  color: #52c41a;
}
.status-rejected {
  background: #fff0f6;
  color: #eb2f96;
}

.resume-title-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.resume-title-container h3 {
  margin: 0;
}

:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

/* 分页符样式 */
.page-break {
  page-break-after: always;
  height: 0;
  display: block;
  clear: both;
  margin: 0;
  padding: 0;
}

/* 下载加载动画优化 */
:deep(.download-loading) {
  .el-loading-text {
    color: #fff !important;
    font-size: 16px !important;
    margin-top: 10px !important;
    font-weight: 500 !important;
  }

  .el-loading-spinner {
    .path {
      stroke: #409eff !important;
    }
  }
}

/* 下载按钮状态优化 */
.resume-actions .el-button.is-loading {
  position: relative;
  pointer-events: none;
}

.resume-actions .el-button.is-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  z-index: 1;
}

/* 下载按钮动画效果 */
.resume-actions .el-button {
  transition: all 0.3s ease;
}

.resume-actions .el-button:hover:not(.is-loading):not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 下载进度对话框样式 */
.download-progress-container {
  text-align: center;
  padding: 20px 0;
}

.progress-icon {
  margin-bottom: 20px;
}

.rotating-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.progress-content {
  margin-bottom: 20px;
}

.progress-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 500;
}

.progress-bar {
  margin-bottom: 10px;
}

.progress-bar :deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
  border-radius: 10px;
}

.progress-bar :deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 10px;
  transition: width 0.6s ease;
}

.progress-percentage {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.progress-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
  font-size: 13px;
  margin-top: 15px;
}

.progress-tips .el-icon {
  color: #409eff;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 0 24px 24px 24px;
}
</style>