package com.bimowu.resume.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 优化的字体加载器
 * 避免内存溢出，提供多种字体加载策略
 */
@Component
@Slf4j
public class OptimizedFontLoader {
    
    private static final ConcurrentHashMap<String, String> fontPathCache = new ConcurrentHashMap<>();
    private static final AtomicBoolean initialized = new AtomicBoolean(false);
    
    /**
     * 初始化字体加载器
     */
    public static void initialize() {
        if (initialized.compareAndSet(false, true)) {
            log.info("初始化优化字体加载器");
            detectAvailableFonts();
        }
    }
    
    /**
     * 检测可用字体
     */
    private static void detectAvailableFonts() {
        // 检测classpath中的字体文件
        String[] fontFiles = {"simsun.ttf", "simhei.ttf", "msyh.ttf"};
        String[] fontNames = {"SimSun", "SimHei", "Microsoft YaHei"};
        
        for (int i = 0; i < fontFiles.length; i++) {
            String fontPath = "fonts/" + fontFiles[i];
            ClassPathResource resource = new ClassPathResource(fontPath);
            
            if (resource.exists()) {
                try {
                    // 获取实际文件路径
                    String actualPath = resource.getURL().toString();
                    fontPathCache.put(fontNames[i], actualPath);
                    log.info("检测到字体: {} -> {}", fontNames[i], actualPath);
                } catch (Exception e) {
                    log.warn("无法获取字体路径: {}", fontPath, e);
                }
            }
        }
        
        // 检测系统字体
        detectSystemFonts();
    }
    
    /**
     * 检测系统字体
     */
    private static void detectSystemFonts() {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("windows")) {
            // Windows系统字体路径
            String[] windowsFonts = {
                "C:/Windows/Fonts/simsun.ttc",
                "C:/Windows/Fonts/simhei.ttf", 
                "C:/Windows/Fonts/msyh.ttc"
            };
            String[] fontNames = {"SimSun", "SimHei", "Microsoft YaHei"};
            
            for (int i = 0; i < windowsFonts.length; i++) {
                File fontFile = new File(windowsFonts[i]);
                if (fontFile.exists()) {
                    fontPathCache.put(fontNames[i] + "_system", fontFile.getAbsolutePath());
                    log.info("检测到系统字体: {} -> {}", fontNames[i], fontFile.getAbsolutePath());
                }
            }
        } else if (os.contains("mac")) {
            // macOS系统字体路径
            String[] macFonts = {
                "/System/Library/Fonts/STSong.ttc",
                "/System/Library/Fonts/STHeiti.ttc"
            };
            String[] fontNames = {"STSong", "STHeiti"};
            
            for (int i = 0; i < macFonts.length; i++) {
                File fontFile = new File(macFonts[i]);
                if (fontFile.exists()) {
                    fontPathCache.put(fontNames[i] + "_system", fontFile.getAbsolutePath());
                    log.info("检测到系统字体: {} -> {}", fontNames[i], fontFile.getAbsolutePath());
                }
            }
        } else {
            // Linux系统字体路径
            String[] linuxFonts = {
                "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            };
            String[] fontNames = {"WenQuanYi", "DejaVu"};
            
            for (int i = 0; i < linuxFonts.length; i++) {
                File fontFile = new File(linuxFonts[i]);
                if (fontFile.exists()) {
                    fontPathCache.put(fontNames[i] + "_system", fontFile.getAbsolutePath());
                    log.info("检测到系统字体: {} -> {}", fontNames[i], fontFile.getAbsolutePath());
                }
            }
        }
    }
    
    /**
     * 获取最佳字体路径
     */
    public static String getBestFontPath(String fontName) {
        initialize();
        
        // 优先使用classpath中的字体
        String classpathFont = fontPathCache.get(fontName);
        if (classpathFont != null) {
            return classpathFont;
        }
        
        // 其次使用系统字体
        String systemFont = fontPathCache.get(fontName + "_system");
        if (systemFont != null) {
            return systemFont;
        }
        
        // 返回null表示未找到
        return null;
    }
    
    /**
     * 检查字体是否可用
     */
    public static boolean isFontAvailable(String fontName) {
        return getBestFontPath(fontName) != null;
    }
    
    /**
     * 获取字体输入流（安全方式，检查内存）
     */
    public static InputStream getFontStream(String fontName) {
        String fontPath = getBestFontPath(fontName);
        if (fontPath == null) {
            log.warn("字体不可用: {}", fontName);
            return null;
        }
        
        try {
            // 检查内存使用情况
            if (MemoryManager.isMemoryUsageHigh(70)) {
                log.warn("内存使用率过高，跳过字体文件加载: {}", fontName);
                return null;
            }
            
            if (fontPath.startsWith("file:") || fontPath.startsWith("jar:")) {
                // classpath资源
                ClassPathResource resource = new ClassPathResource(fontPath.substring(fontPath.lastIndexOf("/") + 1));
                return resource.getInputStream();
            } else {
                // 系统文件
                File fontFile = new File(fontPath);
                if (fontFile.exists() && fontFile.length() < 100 * 1024 * 1024) { // 限制100MB
                    return new java.io.FileInputStream(fontFile);
                } else {
                    log.warn("字体文件过大或不存在: {}", fontPath);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("获取字体流失败: {}", fontName, e);
            return null;
        }
    }
    
    /**
     * 生成轻量级字体CSS
     */
    public static String generateLightweightFontCSS() {
        initialize();
        
        StringBuilder css = new StringBuilder();
        css.append("/* 轻量级中文字体CSS */\n");
        
        // 使用系统字体，不嵌入文件
        css.append("* {\n");
        css.append("  font-family: ");
        
        // 按优先级添加字体
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            css.append("'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', 'SimHei', '黑体'");
        } else if (os.contains("mac")) {
            css.append("'PingFang SC', 'Hiragino Sans GB', 'STSong', 'STHeiti'");
        } else {
            css.append("'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans CN'");
        }
        
        css.append(", Arial, sans-serif !important;\n");
        css.append("  font-size: 14px;\n");
        css.append("}\n");
        
        // 确保中文字符渲染
        css.append("body {\n");
        css.append("  text-rendering: optimizeLegibility;\n");
        css.append("  -webkit-font-smoothing: antialiased;\n");
        css.append("  -moz-osx-font-smoothing: grayscale;\n");
        css.append("}\n");
        
        log.info("生成轻量级字体CSS，长度: {} 字符", css.length());
        return css.toString();
    }
    
    /**
     * 获取可用字体列表
     */
    public static String[] getAvailableFonts() {
        initialize();
        return fontPathCache.keySet().toArray(new String[0]);
    }
    
    /**
     * 清理字体缓存
     */
    public static void clearCache() {
        fontPathCache.clear();
        initialized.set(false);
        log.info("字体缓存已清理");
    }
}