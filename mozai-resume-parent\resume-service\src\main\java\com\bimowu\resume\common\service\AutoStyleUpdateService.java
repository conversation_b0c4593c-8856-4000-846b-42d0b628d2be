package com.bimowu.resume.common.service;

/**
 * 自动样式更新服务接口
 * 负责定时检查和更新模板样式
 */
public interface AutoStyleUpdateService {
    
    /**
     * 启动自动更新服务
     */
    void startAutoUpdate();
    
    /**
     * 停止自动更新服务
     */
    void stopAutoUpdate();
    
    /**
     * 检查并更新所有模板样式
     */
    void checkAndUpdateAllTemplates();
    
    /**
     * 检查并更新指定模板样式
     * @param templateId 模板ID
     */
    void checkAndUpdateTemplate(Integer templateId);
    
    /**
     * 获取自动更新状态
     * @return 是否正在运行
     */
    boolean isRunning();
    
    /**
     * 设置更新间隔
     * @param intervalSeconds 间隔秒数
     */
    void setUpdateInterval(long intervalSeconds);
}