
DROP TABLE IF EXISTS `resume`;
CREATE TABLE `resume` (
  `resume_id` bigint NOT NULL AUTO_INCREMENT COMMENT '简历ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简历标题',
  `status` tinyint DEFAULT '0' COMMENT '简历状态(0-待审核,1-已通过)',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认简历(0-否,1-是)',
  `template_id` bigint DEFAULT NULL COMMENT '简历模板ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`resume_id`) USING BTREE,
  KEY `idx_resume_user` (`user_id`) USING BTREE,
  KEY `idx_resume_status` (`status`) USING BTREE,
  KEY `idx_resume_default` (`is_default`) USING BTREE,
  KEY `idx_resume_template` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='简历主表';




DROP TABLE IF EXISTS `resume_campus`;
CREATE TABLE `resume_campus` (
  `cam_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `campus_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '校园经历',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cam_id`) USING BTREE,
  KEY `idx_campus_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='校园经历表';


DROP TABLE IF EXISTS `resume_category`;

CREATE TABLE `resume_category` (
  `cat_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位类别名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位类别编码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '职位类别描述',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cat_id`) USING BTREE,
  UNIQUE KEY `uk_category_code` (`code`) USING BTREE,
  KEY `idx_category_name` (`name`) USING BTREE,
  KEY `idx_category_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='职位类别表';


DROP TABLE IF EXISTS `resume_category_relation`;
CREATE TABLE `resume_category_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `cat_id` bigint NOT NULL COMMENT '职位类别ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_cat_id` (`cat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历与职位类别关联表';


DROP TABLE IF EXISTS `resume_certificate`;
CREATE TABLE `resume_certificate` (
  `cer_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `certificate_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书奖项',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cer_id`) USING BTREE,
  KEY `idx_cert_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='证书奖项表';


DROP TABLE IF EXISTS `resume_educational`;
CREATE TABLE `resume_educational` (
  `edu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `school` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学校',
  `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '专业',
  `education` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学历',
  `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
  `main_courses` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '主修课程',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`edu_id`) USING BTREE,
  KEY `idx_edu_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教育经历表';


DROP TABLE IF EXISTS `resume_evaluate`;
CREATE TABLE `resume_evaluate` (
  `eva_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `self_evaluation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自我评价',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`eva_id`) USING BTREE,
  KEY `idx_evaluate_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='自我评价表';

DROP TABLE IF EXISTS `resume_hr_questions`;
CREATE TABLE `resume_hr_questions` (
  `que_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `question_type` enum('HR问题') NOT NULL COMMENT '问题类型',
  `question` text NOT NULL COMMENT '题目内容',
  `answer` text COMMENT '题目答案',
  `create_at` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`que_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HR问题表';


DROP TABLE IF EXISTS `resume_information`;
CREATE TABLE `resume_information` (
  `infor_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `avatar` text COMMENT '头像',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性别',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系邮箱',
  `hometown` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '籍贯',
  `nationality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '民族',
  `age` int DEFAULT NULL COMMENT '年龄',
  `job_objective` varchar(20) DEFAULT NULL COMMENT '求职意向',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`infor_id`) USING BTREE,
  KEY `idx_info_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基本信息表';

DROP TABLE IF EXISTS `resume_interest`;
CREATE TABLE `resume_interest` (
  `int_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `interest` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '兴趣爱好',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`int_id`) USING BTREE,
  KEY `idx_interest_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='兴趣爱好表';

DROP TABLE IF EXISTS `resume_practice`;
CREATE TABLE `resume_practice` (
  `pra_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
  `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `role` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '担任角色',
  `project_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '项目描述',
  `project_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`pra_id`) USING BTREE,
  KEY `idx_practice_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='练手项目表';

DROP TABLE IF EXISTS `resume_project`;
CREATE TABLE `resume_project` (
  `pro_id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `cat_id` bigint NOT NULL COMMENT '职位类别id',
  `content` text COMMENT '项目内容（字符串形式，用于兼容）',
  `create_at` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`pro_id`) USING BTREE,
  KEY `idx_project_name` (`name`) USING BTREE,
  KEY `idx_project_role` (`cat_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目表';


DROP TABLE IF EXISTS `resume_project_content`;
CREATE TABLE `resume_project_content` (
  `con_id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容文本',
  `content_order` int NOT NULL COMMENT '内容顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`con_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目内容表';

DROP TABLE IF EXISTS `resume_project_content_segment`;
CREATE TABLE `resume_project_content_segment` (
  `seg_id` bigint NOT NULL AUTO_INCREMENT,
  `content_id` bigint NOT NULL COMMENT '项目内容ID',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '段落文本',
  `is_bold` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加粗：0-否，1-是',
  `segment_order` int NOT NULL COMMENT '段落顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`seg_id`) USING BTREE,
  KEY `content_id` (`content_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目内容段落表';



DROP TABLE IF EXISTS `resume_project_experience`;
CREATE TABLE `resume_project_experience` (
  `exp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `project_id` bigint DEFAULT NULL COMMENT '项目ID',
  `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
  `position_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位类别',
  `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `role` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '担任角色',
  `project_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '项目描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`exp_id`) USING BTREE,
  KEY `idx_exp_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目经验表';

DROP TABLE IF EXISTS `resume_project_question`;
CREATE TABLE `resume_project_question` (
  `que_id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `con_id` bigint NOT NULL,
  `question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `answer` text NOT NULL COMMENT '答案',
  `question_order` int NOT NULL COMMENT '问题顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`que_id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `con_id` (`con_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目问题表';


DROP TABLE IF EXISTS `resume_skill`;
CREATE TABLE `resume_skill` (
    `ski_id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '技能名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`ski_id`),
    KEY `idx_skill_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能表';


DROP TABLE IF EXISTS `resume_skill_segment`;
CREATE TABLE `resume_skill_segment` (
    `seg_id` bigint NOT NULL AUTO_INCREMENT,
    `skill_id` bigint NOT NULL COMMENT '技能ID',
    `seg_name` varchar(50) NOT NULL COMMENT '技能点名称',
    `proficiency` varchar(20) NOT NULL COMMENT '熟练度（一般、良好、熟练、擅长、精通）',
    `text` text NOT NULL COMMENT '段落文本',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`seg_id`),
    KEY `skill_id` (`skill_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能段落表';



DROP TABLE IF EXISTS `resume_talent`;
CREATE TABLE `resume_talent` (
  `tal_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `skill_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '技能名称',
  `proficiency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '熟练度',
  `skill_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '技能描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`tal_id`) USING BTREE,
  KEY `idx_talent_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='技能特长表';

DROP TABLE IF EXISTS `resume_work`;

CREATE TABLE `resume_work` (
  `res_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
  `work_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '工作描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`res_id`) USING BTREE,
  KEY `idx_work_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='工作经验表';


-- 创建AI使用次数记录表
DROP TABLE IF EXISTS `resume_ai_usage`;
CREATE TABLE `resume_ai_usage` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `user_id` bigint NOT NULL COMMENT '用户ID',
   `remaining_count` int NOT NULL DEFAULT '10' COMMENT '剩余可用次数',
   `total_used` int NOT NULL DEFAULT '0' COMMENT '已使用总次数',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户AI使用次数记录表';
-- 进度管理表
DROP TABLE IF EXISTS `resume_progress`;
CREATE TABLE `resume_progress` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `user_id` int NOT NULL COMMENT '用户ID',
   `current_stage` varchar(10) NOT NULL COMMENT '当前进行到的阶段（0：创建简历；1：知识学习；2：hr面试；3：技术面试；4:正式面试）',
   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
   `is_deleted` tinyint DEFAULT '0' COMMENT '删除标识（0：未删除；1：已删除）',
   PRIMARY KEY (`id`),
   KEY `idx_user_id` (`user_id`),
   KEY `idx_current_stage` (`current_stage`),
   KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历进度管理表';

-- 知识管理表
DROP TABLE IF EXISTS `resume_knowledge`;
CREATE TABLE `resume_knowledge` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `position_type` tinyint NOT NULL COMMENT '职位类别（1：开发；2：技术支持）',
    `knowledge_catalog` varchar(10) NOT NULL COMMENT '知识目录（1.1，1.2等）',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT '0' COMMENT '删除标识（0：未删除；1：已删除）',
    PRIMARY KEY (`id`),
    KEY `idx_position_type` (`position_type`),
    KEY `idx_knowledge_catalog` (`knowledge_catalog`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历知识管理表';

-- 为resume表添加audit_opinion字段
ALTER TABLE `resume` ADD COLUMN `audit_opinion` varchar(500) DEFAULT NULL COMMENT '审核意见';