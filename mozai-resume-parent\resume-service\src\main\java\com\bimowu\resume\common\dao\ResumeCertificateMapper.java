package com.bimowu.resume.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumeCertificate;
import com.bimowu.resume.vo.ResumeCertificateVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 证书奖项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeCertificateMapper extends BaseMapper<ResumeCertificate> {

    ResumeCertificateVo selectByResumeId(@Param("resumeId") Long resumeId);
}
