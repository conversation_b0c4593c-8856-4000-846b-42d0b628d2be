package com.bimowu.resume.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.resume.entity.ResumePractice;
import com.bimowu.resume.vo.ResumePracticeVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 练手项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumePracticeMapper extends BaseMapper<ResumePractice> {

    List<ResumePracticeVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
