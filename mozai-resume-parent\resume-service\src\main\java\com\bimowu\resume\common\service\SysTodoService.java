package com.bimowu.resume.common.service;

import com.bimowu.resume.entity.SysTodo;

import java.util.List;

/**
 * 系统待办服务接口
 */
public interface SysTodoService {
    
    /**
     * 创建待办事项
     * 
     * @param sysTodo 待办事项信息
     * @return 是否成功
     */
    boolean createTodo(SysTodo sysTodo);
    
    /**
     * 更新待办事项
     * 
     * @param sysTodo 待办事项信息
     * @return 是否成功
     */
    boolean updateTodo(SysTodo sysTodo);
    
    /**
     * 根据ID获取待办事项
     * 
     * @param id 待办事项ID
     * @return 待办事项信息
     */
    SysTodo getTodoById(Long id);
    
    /**
     * 获取用户未完成的待办事项列表
     * 
     * @param userId 用户ID
     * @return 未完成的待办事项列表
     */
    List<SysTodo> getUnfinishedTodosByUserId(Long userId);
    
    /**
     * 获取用户指定简历的未完成待办事项列表
     * 
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @return 未完成的待办事项列表
     */
    List<SysTodo> getUnfinishedTodosByUserIdAndResumeId(Long userId, Long resumeId);
    
    /**
     * 获取用户的所有待办事项
     * 
     * @param userId 用户ID
     * @return 待办事项列表
     */
    List<SysTodo> getTodosByUserId(Long userId);
    
    /**
     * 获取用户指定简历的所有待办事项
     * 
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @return 待办事项列表
     */
    List<SysTodo> getTodosByUserIdAndResumeId(Long userId, Long resumeId);
    
    /**
     * 完成待办事项
     * 
     * @param id 待办事项ID
     * @return 是否成功
     */
    boolean completeTodo(Long id);
    
    /**
     * 根据用户ID和待办类型更新待办事项状态为已完成
     * 
     * @param userId 用户ID
     * @param todoType 待办类型
     * @return 是否成功
     */
    boolean completeTodoByUserIdAndType(Long userId, Integer todoType);
    
    /**
     * 根据用户ID、简历ID和待办类型更新待办事项状态为已完成
     * 
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @param todoType 待办类型
     * @return 是否成功
     */
    boolean completeTodoByUserIdResumeIdAndType(Long userId, Long resumeId, Integer todoType);
    
    /**
     * 根据userId和resumeId批量删除待办事项
     * @param userId 用户ID
     * @param resumeId 简历ID
     * @return 是否成功
     */
    boolean deleteByUserIdAndResumeId(Long userId, Long resumeId);
} 