<template>
  <div class="form-container">
    <div class="project-select">
        <el-option
          v-for="project in projectList"
          :key="project.proId"
          :label="project.name"
          :value="project.proId"
        >
          <span>{{ project.name }}</span>
          <span class="project-date">{{ formatDate(project.startTime) }} - {{ formatDate(project.endTime) }}</span>
        </el-option>
    </div>

    <div v-if="showEditor">
      <div class="editor-header">
        <div class="editor-actions">
          <el-button type="primary" size="small" @click="$emit('select-content-items')">
            <el-icon class="icon"><document /></el-icon> 选择项目内容条款
          </el-button>
          <el-button type="success" size="small" @click="polishContent" :disabled="!formData.content">
            <el-icon class="icon"><magic-stick /></el-icon> AI润色
          </el-button>
        </div>
      </div>
      
      <div class="editor-content">
        <MdEditor
          v-model="formData.content"
          height="300px"
          :toolbars="toolbars"
          :preview="true"
          :previewTheme="'default'"
          :showCodeRowNumber="true"
          :previewOnly="false"
          :previewWidth="'50%'"
          @onSave="saveForm"
          @change="handleContentChange"
          placeholder="请描述项目背景、职责、技术栈和成果..."
        />
      </div>
      
      <div class="tips">
        <el-alert
          title="撰写建议"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="tips-content">
            <p>1. 描述项目背景和目标</p>
            <p>2. 说明您在项目中的角色和职责</p>
            <p>3. 列举使用的技术栈和工具</p>
            <p>4. 强调您的贡献和项目成果</p>
            <p>5. 可以使用加粗、斜体等富文本格式增强表现力</p>
          </div>
        </el-alert>
      </div>
    </div>
    
    <!-- AI润色对话框 -->
    <el-dialog
      v-model="polishDialogVisible"
      title="AI润色"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="polishLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">AI正在润色您的内容...</div>
      </div>
      <div v-else>
        <div class="polish-result">
          <div class="original-content">
            <h4>原始内容</h4>
            <div class="content-box">{{ formData.content }}</div>
          </div>
          <div class="polished-content">
            <h4>润色后内容</h4>
            <div class="content-box">{{ polishedContent }}</div>
          </div>
        </div>
        <div class="polish-options">
          <h4>请选择要使用的版本</h4>
          <el-radio-group v-model="selectedVersion">
            <el-radio :label="0">使用原始内容</el-radio>
            <el-radio :label="1">使用润色后内容</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="polishDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyPolishedContent" :disabled="polishLoading">
            应用所选内容
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { MdEditor } from 'md-editor-v3'
import { MagicStick, Document } from '@element-plus/icons-vue'
import { polishProjectExperience } from '../api/resume'
import 'md-editor-v3/lib/style.css'

const props = defineProps({
  data: {
    type: String,
    required: true
  },
  projectName: {
    type: String,
    default: ''
  },
  projectCategory: {
    type: String,
    default: ''
  },
  showEditor: {
    type: Boolean,
    default: false
  },
  projectList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update', 'select-content-items', 'polish-content', 'project-select'])

// 控制编辑器显示/隐藏
const showEditor = ref(props.showEditor)

// 监听props.showEditor的变化
watch(() => props.showEditor, (newVal) => {
  showEditor.value = newVal
}, { immediate: true })

// 暴露showEditor变量，让父组件可以访问
defineExpose({
  showEditor
})

// AI润色相关
const polishDialogVisible = ref(false)
const polishLoading = ref(false)
const polishedContent = ref('')
const selectedVersion = ref(1)

// 富文本编辑器配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'codeRow',
  'link',
  'save',
  'pageFullscreen',
  'fullscreen',
  'preview',
]

const preview = ref(true)

// 表单数据
const formData = ref({
  content: ''
})

// 初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal) {
    // 确保content始终是字符串类型
    formData.value.content = typeof newVal === 'object' ? '' : newVal || ''
  } else {
    formData.value.content = ''
  }
}, { immediate: true, deep: true })

// 监听内容变化，实时更新
watch(() => formData.value.content, (newVal) => {
  emit('update', newVal)
}, { immediate: false })

// 隐藏编辑器
const hideEditor = () => {
  showEditor.value = false
  saveForm()
}

// 保存表单
const saveForm = () => {
  emit('update', formData.value.content)
  ElMessage.success('项目描述信息已保存')
}

// 重置表单
const resetForm = () => {
  formData.value.content = props.data || ''
  ElMessage.info('已重置为上次保存的内容')
}

// 处理内容变化
const handleContentChange = (newContent) => {
  formData.value.content = newContent
  emit('update', newContent)
}

// 润色内容
const polishContent = () => {
  if (!formData.value.content) {
    ElMessage.warning('请先输入内容再使用AI润色功能')
    return
  }
  
  polishDialogVisible.value = true
  polishLoading.value = true
  
  // 调用后端AI润色API
  polishProjectExperience(formData.value.content).then(res => {
    if (res.code === 0 || res.code === 200) {
      polishedContent.value = res.data
    } else {
      // 显示后端返回的具体错误消息
      const errorMsg = res.message || res.msg || '润色失败'
      ElMessage.error(errorMsg)
      polishedContent.value = formData.value.content
    }
    polishLoading.value = false
  }).catch(error => {
    console.error('润色请求失败', error)
    // 显示具体的错误消息
    ElMessage.error(error.message || '润色请求失败，请稍后重试')
    polishedContent.value = formData.value.content
    polishLoading.value = false
  })
}

// 应用润色后的内容
const applyPolishedContent = () => {
  if (selectedVersion.value === 1) {
    formData.value.content = polishedContent.value
    emit('update', polishedContent.value)
  }
  polishDialogVisible.value = false
  ElMessage.success('内容已更新')
}

// 选中的项目
const selectedProject = ref('')

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
}

// 处理项目选择
const handleProjectSelect = (projectId) => {
  const project = props.projectList.find(p => p.proId === projectId)
  if (project) {
    emit('project-select', project)
    // 如果项目有内容，自动填充到编辑器中
    if (project.content) {
      formData.value.content = project.content
    }
  }
}
</script>

<style scoped>
.form-container {
  /* 删除padding属性，使宽度与项目特长模块一致 */
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.icon {
  margin-right: 5px;
}

.editor-content {
  margin-bottom: 20px;
}

.tips {
  margin: 20px 0;
}

.tips-content p {
  margin: 5px 0;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.project-select {
  margin-bottom: 20px;
}

.project-date {
  float: right;
  color: #999;
  font-size: 12px;
}

.loading-container {
  padding: 20px 0;
  text-align: center;
}

.loading-text {
  margin-top: 15px;
  color: #409eff;
  font-size: 14px;
}

.polish-result {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.original-content, .polished-content {
  flex: 1;
}

.content-box {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.polish-options {
  margin-top: 20px;
}
</style> 