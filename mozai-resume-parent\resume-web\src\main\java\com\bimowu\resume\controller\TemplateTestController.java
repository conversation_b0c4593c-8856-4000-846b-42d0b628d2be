package com.bimowu.resume.controller;

import com.bimowu.resume.utils.Result;
import com.bimowu.resume.utils.ChineseFontFixer;
import com.bimowu.resume.utils.HtmlTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 模板测试控制器
 * 用于测试简历模板的中文字体显示效果
 */
@RestController
@RequestMapping("/api/debug/template")
@Slf4j
public class TemplateTestController {

    /**
     * 获取模板的HTML内容（用于调试）
     */
    @GetMapping("/html/{templateId}")
    public ResponseEntity<String> getTemplateHtml(@PathVariable Long templateId) {
        try {
            log.info("获取模板{}的HTML内容", templateId);

            // 读取模板
            String templateHtml = HtmlTemplateUtil.readTemplate(templateId);

            // 填充测试数据
            String testHtml = fillTestData(templateHtml);

            // 应用中文字体修复
            String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(testHtml);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            headers.add("Content-Disposition", "inline; filename=template" + templateId + "_test.html");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fixedHtml);

        } catch (Exception e) {
            log.error("获取模板{}HTML失败", templateId, e);
            return ResponseEntity.status(500).body("获取HTML失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试所有模板的状态
     */
    @GetMapping("/test-all")
    public Result<Map<String, Object>> testAllTemplates() {
        Map<String, Object> results = new HashMap<>();
        
        try {
            for (Long templateId = 1L; templateId <= 10; templateId++) {
                Map<String, Object> templateResult = new HashMap<>();
                
                try {
                    // 读取模板
                    String templateHtml = HtmlTemplateUtil.readTemplate(templateId);
                    templateResult.put("template_loaded", true);
                    templateResult.put("template_length", templateHtml.length());
                    
                    // 检查是否包含中文字体设置
                    boolean hasChineseFontCSS = templateHtml.contains("/* 中文字体支持 - PDF生成器优化版本 */");
                    templateResult.put("has_chinese_font_css", hasChineseFontCSS);
                    
                    // 检查是否包含中文字符
                    boolean containsChinese = ChineseFontFixer.containsChineseCharacters(templateHtml);
                    templateResult.put("contains_chinese", containsChinese);
                    
                    // 填充测试数据
                    String testHtml = fillTestData(templateHtml);
                    
                    // 应用字体修复
                    String fixedHtml = ChineseFontFixer.fixChineseFontInHtml(testHtml);
                    templateResult.put("font_fix_applied", fixedHtml.length() > testHtml.length());
                    templateResult.put("final_length", fixedHtml.length());
                    
                    templateResult.put("status", "success");
                    
                } catch (Exception e) {
                    templateResult.put("status", "error");
                    templateResult.put("error", e.getMessage());
                }
                
                results.put("template" + templateId, templateResult);
            }
            
            return Result.success(results);
            
        } catch (Exception e) {
            log.error("测试所有模板失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 填充测试数据
     */
    private String fillTestData(String template) {
        // 替换常见的占位符
        String result = template;
        result = result.replace("${name}", "张三");
        result = result.replace("${age}", "25");
        result = result.replace("${gender}", "男");
        result = result.replace("${phone}", "138-0000-0000");
        result = result.replace("${email}", "<EMAIL>");
        result = result.replace("${hometown}", "北京市朝阳区");
        result = result.replace("${jobObjective}", "Java开发工程师");
        result = result.replace("${education}", "本科 | 计算机科学与技术 | 北京大学 | 2018-2022");
        result = result.replace("${workExperience}", "软件开发工程师 | 腾讯科技 | 2022-至今<br/>负责后端系统开发和维护");
        result = result.replace("${projects}", "电商系统开发 | 2023年<br/>使用Spring Boot开发微服务架构的电商平台");
        result = result.replace("${skills}", "Java、Spring、MySQL、Redis、Docker");
        result = result.replace("${selfEvaluation}", "具有良好的团队合作精神，热爱学习新技术，有较强的问题解决能力。");
        
        // 替换其他可能的占位符
        result = result.replaceAll("\\$\\{[^}]+\\}", "测试内容");
        
        return result;
    }
}
