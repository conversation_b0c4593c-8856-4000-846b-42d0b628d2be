package com.bimowu.resume.utils;

import com.bimowu.resume.dto.ResumeFullSaveDto;
import com.bimowu.resume.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.HashMap;

@Component
@Slf4j
public class HtmlTemplateUtil {

    private static final String TEMPLATE_PATH = "templates/pdf/template";
    
    private static ResumeDataMapper dataMapper;

    /**
     * 模板检测结果类
     */
    public static class TemplateDetectionResult {
        private final boolean isTemplate1;
        private final String detectionMethod;
        private final java.util.List<String> detectionFeatures;
        private final double confidence;

        public TemplateDetectionResult(boolean isTemplate1, String detectionMethod, 
                                     java.util.List<String> detectionFeatures, double confidence) {
            this.isTemplate1 = isTemplate1;
            this.detectionMethod = detectionMethod;
            this.detectionFeatures = detectionFeatures;
            this.confidence = confidence;
        }

        public boolean isTemplate1() { return isTemplate1; }
        public String getDetectionMethod() { return detectionMethod; }
        public java.util.List<String> getDetectionFeatures() { return detectionFeatures; }
        public double getConfidence() { return confidence; }
    }

    @Autowired
    public void setDataMapper(ResumeDataMapper dataMapper) {
        HtmlTemplateUtil.dataMapper = dataMapper;
    }

    /**
     * 改进的模板1检测方法 - 结合templateId和HTML内容特征进行双重验证
     * 
     * @param templateHtml HTML模板内容
     * @param templateId 模板ID（可选，用于双重验证）
     * @return 模板检测结果
     */
    public static TemplateDetectionResult detectTemplate1(String templateHtml, Long templateId) {
        log.info("开始模板1检测 - templateId: {}, HTML长度: {}", templateId, 
                templateHtml != null ? templateHtml.length() : 0);

        if (templateHtml == null || templateHtml.trim().isEmpty()) {
            log.warn("HTML模板内容为空，无法进行检测");
            return new TemplateDetectionResult(false, "EMPTY_CONTENT", 
                    java.util.Arrays.asList("HTML内容为空"), 0.0);
        }

        java.util.List<String> detectedFeatures = new java.util.ArrayList<>();
        double confidence = 0.0;
        String detectionMethod = "CONTENT_ANALYSIS";

        // 特征点1: 检查模板1特有的蓝色渐变背景
        boolean hasBlueGradient = templateHtml.contains("linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)");
        if (hasBlueGradient) {
            detectedFeatures.add("蓝色渐变背景(#4A90E2-#357ABD)");
            confidence += 0.4;
            log.info("检测到模板1特征: 蓝色渐变背景");
        }

        // 特征点2: 检查模板1特有的CSS类名
        boolean hasTemplate1Classes = templateHtml.contains("class=\"resume-container\"") && 
                                     templateHtml.contains("class=\"header\"") &&
                                     templateHtml.contains("class=\"contact-info\"");
        if (hasTemplate1Classes) {
            detectedFeatures.add("模板1特有CSS类名(resume-container, header, contact-info)");
            confidence += 0.3;
            log.info("检测到模板1特征: 特有CSS类名");
        }

        // 特征点3: 检查模板1特有的HTML结构
        boolean hasTemplate1Structure = templateHtml.contains("<div class=\"contact-row\">") &&
                                       templateHtml.contains("<div class=\"contact-item\">") &&
                                       templateHtml.contains("class=\"content-area\"");
        if (hasTemplate1Structure) {
            detectedFeatures.add("模板1特有HTML结构(contact-row, contact-item, content-area)");
            confidence += 0.2;
            log.info("检测到模板1特征: 特有HTML结构");
        }

        // 特征点4: 检查模板1特有的颜色值
        boolean hasTemplate1Colors = templateHtml.contains("#4A90E2") && 
                                     templateHtml.contains("#357ABD") &&
                                     templateHtml.contains("background: #fafafa");
        if (hasTemplate1Colors) {
            detectedFeatures.add("模板1特有颜色值(#4A90E2, #357ABD, #fafafa)");
            confidence += 0.1;
            log.info("检测到模板1特征: 特有颜色值");
        }

        // 特征点5: 检查模板1特有的emoji图标
        boolean hasTemplate1Emojis = templateHtml.contains("👤") && 
                                    templateHtml.contains("📞") &&
                                    templateHtml.contains("✉️") &&
                                    templateHtml.contains("📍") &&
                                    templateHtml.contains("🎯");
        if (hasTemplate1Emojis) {
            detectedFeatures.add("模板1特有emoji图标(👤📞✉️📍🎯)");
            confidence += 0.1;
            log.info("检测到模板1特征: 特有emoji图标");
        }

        // 如果提供了templateId，进行双重验证
        if (templateId != null) {
            if (templateId == 1L) {
                detectedFeatures.add("templateId验证(ID=1)");
                confidence += 0.2;
                detectionMethod = "DUAL_VERIFICATION";
                log.info("templateId验证: 确认为模板1 (ID=1)");
            } else {
                log.info("templateId验证: 不是模板1 (ID={})", templateId);
                // 如果templateId明确不是1，但内容特征匹配，降低置信度但不完全否定
                if (confidence > 0.5) {
                    confidence *= 0.7; // 降低置信度
                    detectedFeatures.add("templateId冲突(ID=" + templateId + ",但内容匹配模板1)");
                    log.warn("检测到templateId与内容特征冲突: ID={}, 但内容特征匹配模板1", templateId);
                }
            }
        }

        // 判断是否为模板1（置信度阈值为0.5）
        boolean isTemplate1 = confidence >= 0.5;

        log.info("模板1检测完成 - 结果: {}, 置信度: {:.2f}, 检测到的特征: {}", 
                isTemplate1, confidence, detectedFeatures);

        // 记录详细的检测日志
        if (isTemplate1) {
            log.info("✅ 确认为模板1 - 检测方法: {}, 特征数量: {}", detectionMethod, detectedFeatures.size());
        } else {
            log.info("❌ 不是模板1 - 检测方法: {}, 特征数量: {}, 可能原因: 置信度不足", 
                    detectionMethod, detectedFeatures.size());
        }

        return new TemplateDetectionResult(isTemplate1, detectionMethod, detectedFeatures, confidence);
    }
    
    /**
     * 读取HTML模板文件
     *
     * @param templateId 模板ID
     * @return 模板内容
     */
    public static String readTemplate(Long templateId) {
        try {
            String templatePath = TEMPLATE_PATH + templateId + ".html";
            log.info("尝试读取模板文件: {}", templatePath);
            ClassPathResource resource = new ClassPathResource(templatePath);

            if (!resource.exists()) {
                log.error("模板文件不存在: {}", templatePath);
                return getDefaultTemplate();
            }

            Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
            String content = FileCopyUtils.copyToString(reader);
            log.info("成功读取模板文件: {}, 内容长度: {}", templatePath, content.length());
            return content;
        } catch (IOException e) {
            log.error("读取HTML模板文件失败: " + e.getMessage(), e);
            // 如果读取失败，返回默认模板
            return getDefaultTemplate();
        }
    }
    
    /**
     * 获取默认模板
     */
    private static String getDefaultTemplate() {
        // 获取中文字体CSS
        String fontCSS = FontUtil.getChineseFontCSS();
        
        return "<!DOCTYPE html>\n" +
               "<html>\n" +
               "<head>\n" +
               "    <meta charset=\"UTF-8\">\n" +
               "    <title>${name}的简历</title>\n" +
               "    <style>\n" +
               fontCSS + "\n" +
               "        body { \n" +
               "            margin: 20px; \n" +
               "            line-height: 1.5; \n" +
               "            font-size: 14px;\n" +
               "        }\n" +
               "        h1, h2 { \n" +
               "            color: #333; \n" +
               "            margin-top: 20px;\n" +
               "            margin-bottom: 10px;\n" +
               "        }\n" +
               "        .resume-section { margin-bottom: 20px; }\n" +
               "        .section-header h2 { border-bottom: 2px solid #333; padding-bottom: 5px; }\n" +
               "        p { margin-bottom: 10px; }\n" +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <h1>${name}的简历</h1>\n" +
               "    <div class=\"basic-info\">\n" +
               "        <p>电话: ${phone}</p>\n" +
               "        <p>邮箱: ${email}</p>\n" +
               "        <p>地址: ${hometown}</p>\n" +
               "    </div>\n" +
               "    ${education}\n" +
               "    ${work}\n" +
               "    ${projects}\n" +
               "    ${practices}\n" +
               "    ${skills}\n" +
               "    ${certificates}\n" +
               "    ${campus}\n" +
               "    ${interests}\n" +
               "    ${selfEvaluation}\n" +
               "</body>\n" +
               "</html>";
    }
    
    /**
     * 转义HTML特殊字符
     * 
     * @param text 需要转义的文本
     * @return 转义后的文本
     */
    private static String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }
    
    /**
     * 将Markdown格式的文本转换为HTML
     * 
     * @param markdown Markdown格式的文本
     * @return HTML格式的文本
     */
    private static String markdownToHtml(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }

        String html = markdown;
        log.info("Markdown转换前: {}", markdown);

        // 处理加粗 **text** -> <strong>text</strong>
        html = html.replaceAll("\\*\\*(.*?)\\*\\*", "<strong>$1</strong>");
        log.info("加粗处理后: {}", html);

        // 处理斜体 *text* -> <em>text</em>
        html = html.replaceAll("\\*(.*?)\\*", "<em>$1</em>");

        // 处理链接 [text](url) -> <a href="url">text</a>
        html = html.replaceAll("\\[(.*?)\\]\\((.*?)\\)", "<a href=\"$2\">$1</a>");

        // 处理换行
        html = html.replace("\n", "<br/>");

        log.info("Markdown转换后: {}", html);
        return html;
    }
    
    /**
     * 使用简历数据填充HTML模板
     *
     * @param template 模板内容
     * @param resume 简历数据
     * @return 填充后的HTML内容
     */
    public static String fillTemplate(String template, ResumeFullSaveDto resume) {
        return fillTemplate(template, resume, null);
    }

    /**
     * 使用简历数据填充HTML模板（带模板ID）
     *
     * @param template 模板内容
     * @param resume 简历数据
     * @param templateId 模板ID（用于双重验证）
     * @return 填充后的HTML内容
     */
    public static String fillTemplate(String template, ResumeFullSaveDto resume, Long templateId) {
        log.info("开始填充HTML模板，模板长度: {}", template.length());

        if (resume == null) {
            log.warn("简历数据为空!");
            return template;
        }

        // 使用改进的模板检测逻辑
        TemplateDetectionResult detectionResult = detectTemplate1(template, templateId);
        boolean isTemplate1 = detectionResult.isTemplate1();
        log.info("模板检测结果 - 是否为模板1: {}, 检测方法: {}, 置信度: {}", 
                isTemplate1, detectionResult.getDetectionMethod(), detectionResult.getConfidence());

        if (isTemplate1) {
            log.info("检测到模板1，使用专门的填充逻辑");
            return fillTemplate1(template, resume);
        }

        // 使用新的数据映射器
        Map<String, String> replacements;
        if (dataMapper != null) {
            log.info("使用ResumeDataMapper进行数据映射");
            replacements = dataMapper.mapAllModules(resume);
        } else {
            log.warn("ResumeDataMapper未初始化，使用旧版映射逻辑");
            replacements = createBasicReplacements(resume);
        }

        // 替换所有占位符
        String result = template;
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            result = result.replace(placeholder, value);
        }

        // 检查未替换的占位符
        checkUnreplacedPlaceholders(result);

        log.info("模板填充完成，填充后长度: {}", result.length());
        return result;
    }

    /**
     * 专门为模板1填充数据的方法
     */
    private static String fillTemplate1(String template, ResumeFullSaveDto resume) {
        log.info("🎯 使用模板1专用填充逻辑开始");

        Map<String, String> replacements = new HashMap<>();

        // 基本信息映射 - 详细日志记录
        log.info("📋 开始处理模板1基本信息映射");
        if (resume.getInformation() != null) {
            ResumeInformationVo info = resume.getInformation();
            
            // 逐个字段处理并记录
            String name = processBasicInfoField("姓名", info.getName(), "求职者");
            String age = processBasicInfoField("年龄", info.getAge() != null ? info.getAge().toString() : null, "");
            String gender = processBasicInfoField("性别", info.getGender(), "");
            String phone = processBasicInfoField("电话", info.getPhone(), "");
            String email = processBasicInfoField("邮箱", info.getEmail(), "");
            String hometown = processBasicInfoField("籍贯", info.getHometown(), "");
            String jobObjective = processBasicInfoField("求职意向", info.getJobObjective(), "");

            // 存储到替换映射中
            replacements.put("name", escapeHtml(name));
            replacements.put("age", age);
            replacements.put("gender", escapeHtml(gender));
            replacements.put("phone", escapeHtml(phone));
            replacements.put("email", escapeHtml(email));
            replacements.put("hometown", escapeHtml(hometown));
            replacements.put("jobObjective", escapeHtml(jobObjective));

            log.info("✅ 模板1基本信息映射完成 - 姓名: [{}], 年龄: [{}], 性别: [{}], 电话: [{}], 邮箱: [{}], 籍贯: [{}], 求职意向: [{}]",
                    name, age, gender, phone, email, hometown, jobObjective);
        } else {
            log.warn("⚠️ 简历基本信息为空，使用默认值");
            replacements.put("name", "求职者");
            replacements.put("age", "");
            replacements.put("gender", "");
            replacements.put("phone", "");
            replacements.put("email", "");
            replacements.put("hometown", "");
            replacements.put("jobObjective", "");
            
            log.info("🔄 已设置基本信息默认值");
        }

        // 直接使用构建方法生成内容，已经使用了正确的CSS类名
        replacements.put("education", buildEducationHtml(resume.getEducationList()));
        replacements.put("work", buildWorkHtml(resume.getWorkList()));
        replacements.put("projects", buildProjectsHtml(resume.getProjectList()));
        replacements.put("skills", buildSkillsHtml(resume.getTalentList()));
        replacements.put("certificates", buildCertificatesHtml(resume.getCertificate()));
        replacements.put("campus", buildCampusHtml(resume.getCampus()));
        replacements.put("interests", buildInterestsHtml(resume.getInterest()));
        replacements.put("selfEvaluation", buildSelfEvaluationHtml(resume.getEvaluate()));

        // 替换所有占位符
        String result = template;
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            result = result.replace(placeholder, value);
        }

        // 检查未替换的占位符
        checkUnreplacedPlaceholders(result);

        log.info("✅ 模板1填充完成，填充后长度: {}", result.length());
        return result;
    }

    /**
     * 处理基本信息字段并记录详细日志
     * 
     * @param fieldName 字段名称
     * @param originalValue 原始值
     * @param defaultValue 默认值
     * @return 处理后的值
     */
    private static String processBasicInfoField(String fieldName, String originalValue, String defaultValue) {
        if (originalValue != null && !originalValue.trim().isEmpty()) {
            log.debug("📝 字段[{}]: 原始值=[{}] -> 使用原始值", fieldName, originalValue);
            return originalValue.trim();
        } else {
            if (defaultValue != null && !defaultValue.isEmpty()) {
                log.debug("🔄 字段[{}]: 原始值为空 -> 使用默认值=[{}]", fieldName, defaultValue);
            } else {
                log.debug("⚪ 字段[{}]: 原始值为空 -> 使用空值", fieldName);
            }
            return defaultValue != null ? defaultValue : "";
        }
    }

    /**
     * 创建基本的替换映射（备用方案）
     */
    private static Map<String, String> createBasicReplacements(ResumeFullSaveDto resume) {
        Map<String, String> replacements = new HashMap<>();
        
        // 基本信息
        if (resume.getInformation() != null) {
            ResumeInformationVo info = resume.getInformation();
            replacements.put("name", escapeHtml(info.getName() != null ? info.getName() : "求职者"));
            replacements.put("age", info.getAge() != null ? info.getAge().toString() : "");
            replacements.put("gender", escapeHtml(info.getGender() != null ? info.getGender() : ""));
            replacements.put("phone", escapeHtml(info.getPhone() != null ? info.getPhone() : ""));
            replacements.put("email", escapeHtml(info.getEmail() != null ? info.getEmail() : ""));
            replacements.put("hometown", escapeHtml(info.getHometown() != null ? info.getHometown() : ""));
            replacements.put("jobObjective", escapeHtml(info.getJobObjective() != null ? info.getJobObjective() : ""));
            
            // 头像处理
            if (info.getAvatar() != null && !info.getAvatar().isEmpty()) {
                replacements.put("avatar", "<img src=\"" + info.getAvatar() + "\" alt=\"头像\" class=\"photo\" />");
            } else {
                replacements.put("avatar", "");
            }
        } else {
            replacements.put("name", "求职者");
            replacements.put("age", "");
            replacements.put("gender", "");
            replacements.put("phone", "");
            replacements.put("email", "");
            replacements.put("hometown", "");
            replacements.put("jobObjective", "");
            replacements.put("avatar", "");
        }
        
        // 教育经历
        replacements.put("education", buildEducationHtml(resume.getEducationList()));
        
        // 工作经验
        replacements.put("work", buildWorkHtml(resume.getWorkList()));
        
        // 项目经验
        replacements.put("projects", buildProjectsHtml(resume.getProjectList()));
        
        // 练手项目
        replacements.put("practices", buildPracticesHtml(resume.getPracticeList()));
        
        // 技能特长
        replacements.put("skills", buildSkillsHtml(resume.getTalentList()));
        
        // 证书奖项
        replacements.put("certificates", buildCertificatesHtml(resume.getCertificate()));
        
        // 校园经历
        replacements.put("campus", buildCampusHtml(resume.getCampus()));
        
        // 兴趣爱好
        replacements.put("interests", buildInterestsHtml(resume.getInterest()));
        
        // 自我评价
        replacements.put("selfEvaluation", buildSelfEvaluationHtml(resume.getEvaluate()));
        
        return replacements;
    }
    
    /**
     * 构建教育经历HTML
     */
    private static String buildEducationHtml(java.util.List<ResumeEducationalVo> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">教育经历</h2>");

        for (ResumeEducationalVo edu : educationList) {
            html.append("<div class=\"item\">");
            html.append("<div class=\"item-header\">");

            // 时间
            if (edu.getTimePeriod() != null) {
                html.append("<div class=\"item-time\">").append(escapeHtml(edu.getTimePeriod())).append("</div>");
            } else {
                html.append("<div class=\"item-time\"></div>");
            }

            // 学校名称
            if (edu.getSchool() != null) {
                html.append("<div class=\"item-title\">").append(escapeHtml(edu.getSchool())).append("</div>");
            } else {
                html.append("<div class=\"item-title\"></div>");
            }

            // 学历和专业
            StringBuilder degreeInfo = new StringBuilder();
            if (edu.getEducation() != null && !edu.getEducation().isEmpty()) {
                degreeInfo.append(escapeHtml(edu.getEducation()));
            }
            if (edu.getMajor() != null && !edu.getMajor().isEmpty()) {
                if (degreeInfo.length() > 0) {
                    degreeInfo.append("（").append(escapeHtml(edu.getMajor())).append("）");
                } else {
                    degreeInfo.append(escapeHtml(edu.getMajor()));
                }
            }
            if (degreeInfo.length() > 0) {
                html.append("<div class=\"item-detail\">").append(degreeInfo.toString()).append("</div>");
            } else {
                html.append("<div class=\"item-detail\"></div>");
            }

            html.append("</div>"); // End of item-header

            // 主修课程
            if (edu.getMainCourses() != null && !edu.getMainCourses().trim().isEmpty()) {
                html.append("<div class=\"item-content\">");
                html.append("主修课程：").append(markdownToHtml(edu.getMainCourses()));
                html.append("</div>");
            }

            html.append("</div>"); // End of item
        }

        html.append("</div>"); // End of section

        return html.toString();
    }
    
    /**
     * 构建工作经验HTML
     */
    private static String buildWorkHtml(java.util.List<ResumeWorkVo> workList) {
        if (workList == null || workList.isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">工作经验</h2>");

        for (ResumeWorkVo work : workList) {
            html.append("<div class=\"item\">");
            html.append("<div class=\"item-header\">");

            // 时间
            if (work.getTimePeriod() != null) {
                html.append("<div class=\"item-time\">").append(escapeHtml(work.getTimePeriod())).append("</div>");
            } else {
                html.append("<div class=\"item-time\"></div>");
            }

            // 公司名称
            if (work.getCompany() != null) {
                html.append("<div class=\"item-title\">").append(escapeHtml(work.getCompany())).append("</div>");
            } else {
                html.append("<div class=\"item-title\"></div>");
            }

            // 职位
            if (work.getPosition() != null) {
                html.append("<div class=\"item-detail\">").append(escapeHtml(work.getPosition())).append("</div>");
            } else {
                html.append("<div class=\"item-detail\"></div>");
            }

            html.append("</div>"); // End of item-header

            // 工作描述
            if (work.getWorkDescription() != null && !work.getWorkDescription().trim().isEmpty()) {
                log.info("处理工作描述: {}", work.getWorkDescription());
                html.append("<div class=\"item-content\">");
                html.append(markdownToHtml(work.getWorkDescription()));
                html.append("</div>");
            }

            html.append("</div>"); // End of item
        }

        html.append("</div>"); // End of section

        return html.toString();
    }
    
    /**
     * 构建项目经验HTML
     */
    private static String buildProjectsHtml(java.util.List<ResumeProjectExperienceVo> projectList) {
        if (projectList == null || projectList.isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">项目经验</h2>");

        for (ResumeProjectExperienceVo project : projectList) {
            html.append("<div class=\"item\">");
            html.append("<div class=\"item-header\">");

            // 时间
            if (project.getTimePeriod() != null) {
                html.append("<div class=\"item-time\">").append(escapeHtml(project.getTimePeriod())).append("</div>");
            } else {
                html.append("<div class=\"item-time\"></div>");
            }

            // 项目名称
            if (project.getProjectName() != null) {
                html.append("<div class=\"item-title\">").append(escapeHtml(project.getProjectName())).append("</div>");
            } else {
                html.append("<div class=\"item-title\"></div>");
            }

            // 角色
            if (project.getRole() != null) {
                html.append("<div class=\"item-detail\">").append(escapeHtml(project.getRole())).append("</div>");
            } else {
                html.append("<div class=\"item-detail\"></div>");
            }

            html.append("</div>"); // End of item-header

            // 项目描述
            if (project.getProjectDescription() != null && !project.getProjectDescription().trim().isEmpty()) {
                html.append("<div class=\"item-content\">");
                html.append(markdownToHtml(project.getProjectDescription()));
                html.append("</div>");
            }

            html.append("</div>"); // End of item
        }

        html.append("</div>"); // End of section

        return html.toString();
    }
    
    /**
     * 构建练手项目HTML
     */
    private static String buildPracticesHtml(java.util.List<ResumePracticeVo> practiceList) {
        if (practiceList == null || practiceList.isEmpty()) {
            return "";
        }
        
        StringBuilder html = new StringBuilder();
        html.append("<div class=\"resume-section\">");
        html.append("<div class=\"section-header\"><h2>练手项目</h2></div>");
        html.append("<div class=\"practice-content\">");
        
        for (ResumePracticeVo practice : practiceList) {
            html.append("<div class=\"practice-item\">");
            html.append("<div class=\"practice-header\">");
            
            if (practice.getTimePeriod() != null) {
                html.append("<div class=\"practice-time\">").append(escapeHtml(practice.getTimePeriod())).append("</div>");
            }
            if (practice.getProjectName() != null) {
                html.append("<div class=\"practice-name\">").append(escapeHtml(practice.getProjectName())).append("</div>");
            }
            if (practice.getRole() != null) {
                html.append("<div class=\"practice-role\">").append(escapeHtml(practice.getRole())).append("</div>");
            }
            
            html.append("</div>"); // End of practice-header
            
            // 项目URL
            if (practice.getProjectUrl() != null && !practice.getProjectUrl().trim().isEmpty()) {
                html.append("<div class=\"practice-url\">项目地址：");
                html.append("<a href=\"").append(escapeHtml(practice.getProjectUrl())).append("\">");
                html.append(escapeHtml(practice.getProjectUrl())).append("</a></div>");
            }
            
            // 项目描述
            if (practice.getProjectDescription() != null && !practice.getProjectDescription().trim().isEmpty()) {
                html.append("<div class=\"practice-description\">");
                html.append(markdownToHtml(practice.getProjectDescription()));
                html.append("</div>");
            }
            
            html.append("</div>"); // End of practice-item
        }
        
        html.append("</div>"); // End of practice-content
        html.append("</div>"); // End of resume-section
        
        return html.toString();
    }
    
    /**
     * 构建技能特长HTML
     */
    private static String buildSkillsHtml(java.util.List<ResumeTalentVo> skillList) {
        if (skillList == null || skillList.isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">技能特长</h2>");

        for (ResumeTalentVo skill : skillList) {
            html.append("<div class=\"skill-item\">");

            if (skill.getSkillName() != null && !skill.getSkillName().trim().isEmpty()) {
                html.append("<div class=\"skill-name\">").append(escapeHtml(skill.getSkillName())).append("</div>");
            }

            if (skill.getSkillDescription() != null && !skill.getSkillDescription().trim().isEmpty()) {
                html.append("<div class=\"skill-description\">");
                html.append(markdownToHtml(skill.getSkillDescription()));
                html.append("</div>");
            }

            html.append("</div>"); // End of skill-item
        }

        html.append("</div>"); // End of section

        return html.toString();
    }
    
    /**
     * 构建证书奖项HTML
     */
    private static String buildCertificatesHtml(ResumeCertificateVo certificate) {
        if (certificate == null || certificate.getCertificateName() == null || certificate.getCertificateName().trim().isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">荣誉证书</h2>");
        html.append("<div class=\"section-content\">");
        html.append(markdownToHtml(certificate.getCertificateName()));
        html.append("</div>"); // End of section-content
        html.append("</div>"); // End of section

        return html.toString();
    }

    /**
     * 构建校园经历HTML
     */
    private static String buildCampusHtml(ResumeCampusVo campus) {
        if (campus == null || campus.getCampusExperience() == null || campus.getCampusExperience().trim().isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">校园经历</h2>");
        html.append("<div class=\"section-content\">");
        html.append(markdownToHtml(campus.getCampusExperience()));
        html.append("</div>"); // End of section-content
        html.append("</div>"); // End of section

        return html.toString();
    }

    /**
     * 构建兴趣爱好HTML
     */
    private static String buildInterestsHtml(ResumeInterestVo interest) {
        if (interest == null || interest.getInterest() == null || interest.getInterest().trim().isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">兴趣爱好</h2>");
        html.append("<div class=\"section-content\">");
        html.append(markdownToHtml(interest.getInterest()));
        html.append("</div>"); // End of section-content
        html.append("</div>"); // End of section

        return html.toString();
    }

    /**
     * 构建自我评价HTML
     */
    private static String buildSelfEvaluationHtml(ResumeEvaluateVo evaluate) {
        if (evaluate == null || evaluate.getSelfEvaluation() == null || evaluate.getSelfEvaluation().trim().isEmpty()) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div class=\"section\">");
        html.append("<h2 class=\"section-title\">个人评价</h2>");
        html.append("<div class=\"section-content\">");
        html.append(markdownToHtml(evaluate.getSelfEvaluation()));
        html.append("</div>"); // End of section-content
        html.append("</div>"); // End of section

        return html.toString();
    }
    
    /**
     * 检查未替换的占位符 - 增强版本，提供详细的位置和统计信息
     */
    private static void checkUnreplacedPlaceholders(String content) {
        log.info("🔍 开始检查占位符替换完整性");
        
        java.util.List<String> unreplacedPlaceholders = new java.util.ArrayList<>();
        java.util.Map<String, Integer> placeholderPositions = new java.util.HashMap<>();
        
        String temp = content;
        int globalOffset = 0;
        
        while (temp.contains("${")) {
            int start = temp.indexOf("${");
            int end = temp.indexOf("}", start);
            if (end > start) {
                String placeholder = temp.substring(start, end + 1);
                int actualPosition = globalOffset + start;
                
                unreplacedPlaceholders.add(placeholder);
                placeholderPositions.put(placeholder, actualPosition);
                
                log.warn("❌ 发现未替换的占位符: {} (位置: {})", placeholder, actualPosition);
                
                globalOffset += end + 1;
                temp = temp.substring(end + 1);
            } else {
                break;
            }
        }
        
        // 统计和报告
        if (!unreplacedPlaceholders.isEmpty()) {
            log.warn("⚠️ 占位符替换不完整 - 总计发现 {} 个未替换的占位符", unreplacedPlaceholders.size());
            
            // 按占位符类型分组统计
            java.util.Map<String, Long> placeholderTypeCount = unreplacedPlaceholders.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            p -> p.replaceAll("\\$\\{([^}]+)\\}", "$1"),
                            java.util.stream.Collectors.counting()));
            
            log.warn("📊 未替换占位符统计:");
            placeholderTypeCount.forEach((type, count) -> 
                log.warn("   - {}: {} 个", type, count));
                
            // 检查是否包含基本信息占位符
            boolean hasBasicInfoPlaceholders = unreplacedPlaceholders.stream()
                    .anyMatch(p -> p.matches("\\$\\{(name|age|phone|email|hometown|jobObjective)\\}"));
            
            if (hasBasicInfoPlaceholders) {
                log.error("🚨 检测到基本信息占位符未替换，这可能导致模板1基本信息丢失问题！");
            }
        } else {
            log.info("✅ 占位符替换检查通过 - 所有占位符已成功替换");
        }
        
        // 记录替换完成度
        int totalContentLength = content.length();
        double completionRate = unreplacedPlaceholders.isEmpty() ? 100.0 : 
                (double)(totalContentLength - unreplacedPlaceholders.size() * 10) / totalContentLength * 100;
        
        log.info("📈 占位符替换完成度: {:.1f}% (内容长度: {}, 未替换: {})", 
                completionRate, totalContentLength, unreplacedPlaceholders.size());
    }



}