<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>${name}的简历</title>
    <style type="text/css">
        /* 中文字体支持 - PDF生成器优化版本 */
        * {
            font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', 'SimHei', '黑体', 'Arial Unicode MS', Arial, sans-serif !important;
            font-size: 14px;
            font-weight: normal;
        }

        body, p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, a, strong, em {
            font-family: inherit !important;
        }

        body {
            text-rendering: auto;
            -webkit-font-smoothing: auto;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            line-height: 1.6;
        }

        @media print {
            * {
                font-family: 'SimSun', '宋体', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif !important;
            }
        }
        
        .resume-template {
            display: flex;
            max-width: 210mm;
            margin: 0 auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 左侧边栏样式 */
        .sidebar {
            width: 30%;
            background-color: #2c3e50;
            color: #fff;
            padding: 30px;
            box-sizing: border-box;
        }
        
        .avatar-container {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #fff;
        }
        
        .avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .sidebar-name {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .sidebar-section {
            margin-bottom: 25px;
        }
        
        .sidebar-section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .contact-icon {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            color: #3498db;
        }
        
        .skills-list {
            padding-left: 0;
            list-style-type: none;
        }
        
        .skill-item {
            margin-bottom: 8px;
        }
        
        .skill-name {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .skill-level {
            height: 6px;
            background-color: #34495e;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .skill-level-fill {
            height: 100%;
            background-color: #3498db;
        }
        
        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            padding: 30px;
            background-color: #fff;
            box-sizing: border-box;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 30px;
            margin-bottom: 20px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #3498db;
        }
        
        .timeline-item::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 17px;
            width: 2px;
            height: calc(100% + 10px);
            background-color: #e0e0e0;
        }
        
        .timeline-item:last-child::after {
            display: none;
        }
        
        .timeline-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 18px;
            color: #2c3e50;
        }
        
        .timeline-subtitle {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        
        .timeline-date {
            position: absolute;
            right: 0;
            top: 0;
            color: #3498db;
            font-weight: bold;
        }
        
        .timeline-content {
            margin-top: 10px;
            color: #555;
        }
        
        /* 其他元素样式 */
        ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        p {
            margin: 0 0 10px 0;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="resume-template">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="avatar-container">
                ${avatar}
            </div>
            <div class="sidebar-name">${name}</div>
            
            <div class="sidebar-section">
                <div class="sidebar-section-title">个人信息</div>
                <div class="contact-item">
                    <div class="contact-icon">👤</div>
                    <div>${gender} | ${age}岁</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📞</div>
                    <div>${phone}</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📧</div>
                    <div>${email}</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">🏠</div>
                    <div>${hometown}</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">🎯</div>
                    <div>${jobObjective}</div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-section-title">专业技能</div>
                ${skillsSidebar}
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-section-title">自我评价</div>
                ${selfEvaluationSidebar}
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 教育经历 -->
            <div class="section">
                <div class="section-title">教育经历</div>
                ${educationMain}
            </div>
            
            <!-- 工作经验 -->
            <div class="section">
                <div class="section-title">工作经验</div>
                ${workMain}
            </div>
            
            <!-- 项目经验 -->
            <div class="section">
                <div class="section-title">项目经验</div>
                ${projectsMain}
            </div>
            
            <!-- 练手项目 -->
            <div class="section">
                <div class="section-title">练手项目</div>
                ${practicesMain}
            </div>
            
            <!-- 证书奖项 -->
            <div class="section">
                <div class="section-title">证书奖项</div>
                ${certificatesMain}
            </div>
            
            <!-- 校园经历 -->
            <div class="section">
                <div class="section-title">校园经历</div>
                ${campusMain}
            </div>
            
            <!-- 兴趣爱好 -->
            <div class="section">
                <div class="section-title">兴趣爱好</div>
                ${interestsMain}
            </div>
        </div>
    </div>
</body>
</html> 